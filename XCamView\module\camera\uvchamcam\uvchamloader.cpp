﻿#include <QApplication>
#include <QDir>
#include "uvchamloader.h"

UvchamLoader g_uvchamLoader;

static const char* concatFUNNAME(char* newstr, const char* funprefix, const char* funname)
{
    strcpy(newstr, funprefix);
    strcat(newstr, funname);
    return newstr;
}

UvchamLoader::UvchamLoader()
    : m_bLoad(false)
{

}

bool UvchamLoader::initLoader()
{
    QDir qdir(QApplication::applicationDirPath());
    qdir.setFilter(QDir::Files);
    qdir.setNameFilters(QStringList() <<
#if defined(_WIN32)
        "uvcham.dll"
#elif defined(__APPLE__)
        "uvcham.dylib"
#else
        "uvcham.so"
#endif
        );
    QStringList fileList = qdir.entryList();
    if(!fileList.empty())
    {
        const QString libPath = qdir.filePath(fileList.at(0));
        std::shared_ptr<QLibrary> libptr = std::make_shared<QLibrary>(libPath);
        libptr->load();
        if(libptr->isLoaded())
        {
            if((nullptr == m_pLibUvcham.get()) && InitUvcham(libptr, "Uvcham"))
                m_pLibUvcham = libptr;

            if(m_pLibUvcham)
            {
                Log(Log_Info, "Successed to load %s", libPath.toUtf8().data());
                m_bLoad = true;
                return true;
            }
            Log(Log_Info, "Failed to load %s", libPath.toUtf8().data());
        }
    }
    else
        Log(Log_Info, "Not found uvcham.dll");

    return false;
}

bool UvchamLoader::InitUvcham(const std::shared_ptr<QLibrary>& libptr, const char* prefix)
{
    char strFunname[128];
    pUvcham_enum = (PUvcham_enum)libptr->resolve(concatFUNNAME(strFunname, prefix, "_enum"));
    if (nullptr == pUvcham_enum)
        return false;

    pUvcham_open =   (PUvcham_open)libptr->resolve(concatFUNNAME(strFunname, prefix, "_open"));
    pUvcham_start =  (PUvcham_start)libptr->resolve(concatFUNNAME(strFunname, prefix, "_start"));
    pUvcham_stop =   (PUvcham_stop)libptr->resolve(concatFUNNAME(strFunname, prefix, "_stop"));
    pUvcham_close =  (PUvcham_close)libptr->resolve(concatFUNNAME(strFunname, prefix, "_close"));
    pUvcham_put =    (PUvcham_put)libptr->resolve(concatFUNNAME(strFunname, prefix, "_put"));
    pUvcham_get =    (PUvcham_get)libptr->resolve(concatFUNNAME(strFunname, prefix, "_get"));
    pUvcham_range =  (PUvcham_range)libptr->resolve(concatFUNNAME(strFunname, prefix, "_range"));
    pUvcham_pull =   (PUvcham_pull)libptr->resolve(concatFUNNAME(strFunname, prefix, "_pull"));
    pUvcham_record = (PUvcham_record)libptr->resolve(concatFUNNAME(strFunname, prefix, "_record"));
    return true;
}
