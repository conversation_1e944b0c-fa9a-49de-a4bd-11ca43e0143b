#ifndef MONITORVIEW_H
#define MONITORVIEW_H

#include <QWidget>
#include <QList>

class MonitorViewItem : public QWidget
{
    Q_OBJECT
public:
    explicit MonitorViewItem(QWidget* parent = nullptr);
    ~MonitorViewItem();

    virtual void Notify(int notify);

    void SetName(const QString& name);

protected:
    void paintEvent(QPaintEvent *event) override;
    void mouseDoubleClickEvent(QMouseEvent* event) override;

private:
    QString m_name;
};

class MonitorView : public QWidget
{
    Q_OBJECT
public:
    explicit MonitorView(QWidget* parent = nullptr);

    virtual void Notify(int notify);

private:
    QList<MonitorViewItem*> m_pViewItemList;
};

#endif
