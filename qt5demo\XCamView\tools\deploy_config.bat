@echo off
echo 部署数据库配置文件到构建目录...
echo.

set SOURCE_CONFIG=D:\work\XCamView\XCamView\config\database.ini
set DEBUG_TARGET=D:\work\XCamView\build-XCamView-Desktop_Qt_5_14_2_MSVC2017_64bit-Debug\debug\config
set RELEASE_TARGET=D:\work\XCamView\build-XCamView-Desktop_Qt_5_14_2_MSVC2017_64bit-Release\release\config

echo 源配置文件: %SOURCE_CONFIG%
echo.

if not exist "%SOURCE_CONFIG%" (
    echo 错误: 源配置文件不存在!
    echo 请先创建: %SOURCE_CONFIG%
    pause
    exit /b 1
)

echo 配置文件内容:
echo ==========================================
type "%SOURCE_CONFIG%"
echo ==========================================
echo.

echo 创建目标目录...
if not exist "%DEBUG_TARGET%" mkdir "%DEBUG_TARGET%"
if not exist "%RELEASE_TARGET%" mkdir "%RELEASE_TARGET%"

echo 复制到Debug目录...
copy "%SOURCE_CONFIG%" "%DEBUG_TARGET%\database.ini"
if %ERRORLEVEL% EQU 0 (
    echo ✅ Debug配置部署成功
) else (
    echo ❌ Debug配置部署失败
)

echo 复制到Release目录...
copy "%SOURCE_CONFIG%" "%RELEASE_TARGET%\database.ini"
if %ERRORLEVEL% EQU 0 (
    echo ✅ Release配置部署成功
) else (
    echo ❌ Release配置部署失败
)

echo.
echo 部署完成!
echo.
echo 配置文件位置:
echo Debug:   %DEBUG_TARGET%\database.ini
echo Release: %RELEASE_TARGET%\database.ini
echo.
pause
