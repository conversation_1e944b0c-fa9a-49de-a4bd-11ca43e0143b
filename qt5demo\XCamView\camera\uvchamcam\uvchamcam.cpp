﻿#include "uvchamcam.h"
#include "uvchamloader.h"
#include <QMessageBox>
#include <memory>

UvchamCam::UvchamCam(QObject *parent)
    : Camera(parent), m_hUvcham(nullptr), m_bOpened(false), m_bRunning(false), m_pData(nullptr)
    , m_width(0), m_height(0), m_preExpTime(-10000), m_preExpGain(-10000), m_preWBRed(-10000)
    , m_preWBGreen(-10000), m_preWBBlue(-10000)
{
    connect(&m_timer, SIGNAL(timeout()), this, SLOT(OnTimerOut()));
}

UvchamCam::~UvchamCam()
{
    if(m_pData) delete []m_pData;
}

bool UvchamCam::InitLoader()
{
    bool b = m_uvchamLoader.initLoader();
    if(!b)
        QMessageBox::critical(nullptr, g_i18n->value("ids_message"), g_i18n->value("ids_no_uvcham_dll"));
    return b;
}

QList<CameraSt> UvchamCam::Enum()
{
    UvchamDevice arr[UVCHAM_MAX];
    int cnt = m_uvchamLoader.pUvcham_enum(arr);
    QList<CameraSt> list;
    for(int i = 0; i < cnt; ++i)
    {
        CameraSt st;
        st.name = QString::fromWCharArray(arr[i].displayname);
        st.id = QString::fromWCharArray(arr[i].id);
        list.append(st);
    }
    Log("%s: count = %d", __func__, cnt);
    return list;
}

bool UvchamCam::Open(const CameraSt &info)
{
    std::unique_ptr<wchar_t[]> id = std::make_unique<wchar_t[]>(info.id.length() + 1);
    info.id.toWCharArray(id.get());
    id[info.id.length()] = L'\0';
    m_hUvcham = m_uvchamLoader.pUvcham_open(id.get());

    Log("%s: id = %s, handle = 0x%x", __func__, info.id.toUtf8().data(), m_hUvcham);

    m_bOpened = nullptr != m_hUvcham;
    if(m_bOpened)
        m_camInfo = info;
    return m_bOpened;
}

void UvchamCam::Close()
{
    Log("%s", __func__);

    if(m_hUvcham)
    {
        m_timer.stop();
        m_uvchamLoader.pUvcham_close(m_hUvcham);
        m_hUvcham = nullptr;
        m_bOpened = m_bRunning = false;
        m_width = m_height = 0;
        m_camInfo.Clear();
    }
}

void UvchamCam::Stop()
{
    if(m_hUvcham)
    {
        m_timer.stop();
        m_uvchamLoader.pUvcham_stop(m_hUvcham);
        m_bRunning = false;
        m_width = m_height = 0;
    }
}

HRESULT UvchamCam::Start()
{
    HRESULT ret = E_FAIL;
    if(m_hUvcham)
    {
        m_uvchamLoader.pUvcham_put(m_hUvcham, UVCHAM_FORMAT, 2);
        int res = 0;
        m_uvchamLoader.pUvcham_get(m_hUvcham, UVCHAM_RES, &res);
        m_uvchamLoader.pUvcham_get(m_hUvcham, UVCHAM_WIDTH | res, &m_width);
        m_uvchamLoader.pUvcham_get(m_hUvcham, UVCHAM_HEIGHT | res, &m_height);
        if(m_pData) { delete []m_pData; m_pData = nullptr; }
        const int totalSize = TDIBWIDTHBYTES(m_width * 24) * m_height;
        m_pData = new uchar[totalSize];
        ret = m_uvchamLoader.pUvcham_start(m_hUvcham, nullptr, UVCHAM_CALLBACK, this);
        m_bRunning = SUCCEEDED(ret);
        if(m_bRunning)
        {
           m_uvchamLoader.pUvcham_get(m_hUvcham, UVCHAM_EXPOTIME, &m_preExpTime);
           m_uvchamLoader.pUvcham_get(m_hUvcham, UVCHAM_AGAIN, &m_preExpGain);
           m_uvchamLoader.pUvcham_get(m_hUvcham, UVCHAM_WBRED, &m_preWBRed);
           m_uvchamLoader.pUvcham_get(m_hUvcham, UVCHAM_WBGREEN, &m_preWBGreen);
           m_uvchamLoader.pUvcham_get(m_hUvcham, UVCHAM_WBBLUE, &m_preWBBlue);
           m_timer.start(1000);
        }
    }

    Log("%s: return = %d", __func__, ret);
    return ret;
}

bool UvchamCam::IsOpened()
{
    return m_bOpened;
}

bool UvchamCam::IsRunning()
{
    return m_bRunning;
}

HRESULT UvchamCam::PutPara(unsigned nId, int val)
{
    HRESULT ret = E_FAIL;
    if(m_hUvcham)
    {
        if(CAM_PARA_RES == nId)
        {
            int curRes = 0;
            m_uvchamLoader.pUvcham_get(m_hUvcham, nId, &curRes);
            if(curRes != val)
            {
                CameraSt camInfo = m_camInfo;
                Close();
                Open(camInfo);
                m_uvchamLoader.pUvcham_put(m_hUvcham, nId, val);
                emit Notify(NOTIFY_CAM_RES);
                Start();
            }
        }
        else
        {
            if(CAM_PARA_EXPOTIME == nId)
                val /= 1000;
            ret = m_uvchamLoader.pUvcham_put(m_hUvcham, nId, val);
        }
    }
    return ret;
}

HRESULT UvchamCam::GetPara(unsigned nId, int *pVal)
{
    HRESULT ret = E_FAIL;
    if(m_hUvcham)
    {
        ret = m_uvchamLoader.pUvcham_get(m_hUvcham, nId, pVal);
        if(CAM_PARA_EXPOTIME == nId && pVal)
            *pVal *= 1000;
    }
    return ret;
}

HRESULT UvchamCam::GetParaRange(unsigned nId, int *pMin, int *pMax, int *pDef)
{
    HRESULT ret = E_FAIL;
    if(m_hUvcham)
    {
        int min = 0, max = 0, def = 0;
        if(nullptr == pMin) pMin = &min;
        if(nullptr == pMax) pMax = &max;
        if(nullptr == pDef) pDef = &def;
        ret = m_uvchamLoader.pUvcham_range(m_hUvcham, nId, pMin, pMax, pDef);
        if(CAM_PARA_EXPOTIME == nId)
        {
            *pMin *= 1000;
            *pMax *= 1000;
            *pDef *= 1000;
        }
    }
    return ret;
}

int UvchamCam::GetImageWidth()
{
    if(m_hUvcham)
    {
        int res = 0;
        m_uvchamLoader.pUvcham_get(m_hUvcham, UVCHAM_RES, &res);
        m_uvchamLoader.pUvcham_get(m_hUvcham, UVCHAM_WIDTH | res, &m_width);
    }
    return m_width;
}

int UvchamCam::GetImageHeight()
{
    if(m_hUvcham)
    {
        int res = 0;
        m_uvchamLoader.pUvcham_get(m_hUvcham, UVCHAM_RES, &res);
        m_uvchamLoader.pUvcham_get(m_hUvcham, UVCHAM_HEIGHT | res, &m_height);
    }
    return m_height;
}

void UvchamCam::GetImage(void* pFrameBuffer)
{
    if(pFrameBuffer && m_pData)
    {
        m_mutex.lock();
        const int stride = TDIBWIDTHBYTES(m_width * 24);
        for(int j = 0; j < m_height; ++j)
            memcpy((uchar*)pFrameBuffer + j * stride, m_pData + (m_height - 1 - j) * stride, stride);
        m_mutex.unlock();
    }
}

void UvchamCam::OnTimerOut()
{
    int val = 0;
    m_uvchamLoader.pUvcham_get(m_hUvcham, UVCHAM_AEXPO, &val);
    if(1 == val)
    {
        int time = 0, gain = 0;
        m_uvchamLoader.pUvcham_get(m_hUvcham, UVCHAM_EXPOTIME, &time);
        m_uvchamLoader.pUvcham_get(m_hUvcham, UVCHAM_AGAIN, &gain);
        if(time != m_preExpTime || gain != m_preExpGain)
        {
            m_preExpTime = time;
            m_preExpGain = gain;
            emit CamEvent(CAM_EVENT_EXP_CHANGED);
        }
    }

    m_uvchamLoader.pUvcham_get(m_hUvcham, UVCHAM_WBMODE, &val);
    if(WB_MANUAL != val)
    {
        int red = 0, green = 0, blue = 0;
        m_uvchamLoader.pUvcham_get(m_hUvcham, UVCHAM_WBRED, &red);
        m_uvchamLoader.pUvcham_get(m_hUvcham, UVCHAM_WBGREEN, &green);
        m_uvchamLoader.pUvcham_get(m_hUvcham, UVCHAM_WBBLUE, &blue);
        if(red != m_preWBRed || green != m_preWBGreen || blue != m_preWBBlue)
        {
            m_preWBRed = red;
            m_preWBGreen = green;
            m_preWBBlue = blue;
            emit CamEvent(CAM_EVENT_WB_CHANGED);
        }
    }
}

void UvchamCam::UVCHAM_CALLBACK(unsigned nEvent, void *pCallbackCtx)
{
    UvchamCam *pCam = reinterpret_cast<UvchamCam*>(pCallbackCtx);
    if(pCam)
    {
        if(UVCHAM_EVENT_IMAGE == nEvent)
            pCam->PullImagePrivate();
        pCam->CamEvent(nEvent);
    }
}

void UvchamCam::PullImagePrivate()
{
    if(m_hUvcham)
    {
        m_mutex.lock();
        m_uvchamLoader.pUvcham_pull(m_hUvcham, m_pData);
        m_mutex.unlock();
    }
}
