#include "TpDatabase.h"
#include <QDebug>
#include <QLoggingCategory>
#include <QMutex>
#include <QMutexLocker>

// 日志分类
Q_LOGGING_CATEGORY(tpDatabaseLog, "tpdatabase")

namespace TpDatabase {

// 全局状态
static bool g_initialized = false;
static int g_logLevel = 3; // 默认信息级别
static QMutex g_mutex;

bool initialize()
{
    QMutexLocker locker(&g_mutex);
    
    if (g_initialized) {
        return true;
    }
    
    qDebug(tpDatabaseLog) << "TpDatabase: 初始化模块，版本:" << version();
    
    // 执行初始化操作
    // 这里可以添加必要的初始化代码，比如：
    // - 注册元类型
    // - 设置默认配置
    // - 初始化资源等
    
    // 注册元类型，用于信号槽传递
    qRegisterMetaType<TpDatabaseErrorCode>("TpDatabaseErrorCode");
    qRegisterMetaType<TpDatabaseConnectionState>("TpDatabaseConnectionState");
    qRegisterMetaType<TpDatabaseResult>("TpDatabaseResult");
    
    g_initialized = true;
    
    qDebug(tpDatabaseLog) << "TpDatabase: 模块初始化完成";
    return true;
}

void cleanup()
{
    QMutexLocker locker(&g_mutex);
    
    if (!g_initialized) {
        return;
    }
    
    qDebug(tpDatabaseLog) << "TpDatabase: 清理模块";
    
    // 执行清理操作
    // 这里可以添加必要的清理代码，比如：
    // - 关闭所有连接
    // - 清理缓存
    // - 释放资源等
    
    g_initialized = false;
    
    qDebug(tpDatabaseLog) << "TpDatabase: 模块清理完成";
}

void setLogLevel(int level)
{
    QMutexLocker locker(&g_mutex);
    
    if (level < 0 || level > 4) {
        qWarning(tpDatabaseLog) << "TpDatabase: 无效的日志级别:" << level;
        return;
    }
    
    g_logLevel = level;
    
    // 根据级别设置Qt日志过滤
    switch (level) {
    case 0: // 关闭
        QLoggingCategory::setFilterRules("tpdatabase.debug=false\n"
                                        "tpdatabase.info=false\n"
                                        "tpdatabase.warning=false\n"
                                        "tpdatabase.critical=false");
        break;
    case 1: // 错误
        QLoggingCategory::setFilterRules("tpdatabase.debug=false\n"
                                        "tpdatabase.info=false\n"
                                        "tpdatabase.warning=false\n"
                                        "tpdatabase.critical=true");
        break;
    case 2: // 警告
        QLoggingCategory::setFilterRules("tpdatabase.debug=false\n"
                                        "tpdatabase.info=false\n"
                                        "tpdatabase.warning=true\n"
                                        "tpdatabase.critical=true");
        break;
    case 3: // 信息
        QLoggingCategory::setFilterRules("tpdatabase.debug=false\n"
                                        "tpdatabase.info=true\n"
                                        "tpdatabase.warning=true\n"
                                        "tpdatabase.critical=true");
        break;
    case 4: // 调试
        QLoggingCategory::setFilterRules("tpdatabase.debug=true\n"
                                        "tpdatabase.info=true\n"
                                        "tpdatabase.warning=true\n"
                                        "tpdatabase.critical=true");
        break;
    }
    
    qDebug(tpDatabaseLog) << "TpDatabase: 日志级别设置为:" << level;
}

int logLevel()
{
    QMutexLocker locker(&g_mutex);
    return g_logLevel;
}

} // namespace TpDatabase
