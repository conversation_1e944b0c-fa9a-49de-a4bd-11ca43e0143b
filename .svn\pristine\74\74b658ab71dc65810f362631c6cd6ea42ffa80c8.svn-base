#include "exposuregainwidget.h"
#include <QInputDialog>
#include <QGridLayout>

ExposureGainWidget::ExposureGainWidget(QWidget* parent)
    : CollapseWidget(QIcon(":/images/exposure.png"), g_i18n->value("ids_exposure_gain"), parent)
{
    const QString btnLabelQss = "QPushButton, QPushButton:disabled {background-color:transparent; color:black; text-align:right;}"
                                "QPushButton:hover {background-color:rgb(240, 240, 240); border: 1px solid #007BFF; border-radius: 3px;}"
                                "QPushButton:pressed {background-color:rgb(200, 200, 200); border: 1px solid #007BFF; border-radius: 3px;}";

    m_aexpCkb = new QCheckBox(g_i18n->value("ids_auto_exposure"));
    m_expTargetLbl = new QLabel(g_i18n->value("ids_expo_target"));
    m_expTargetValueLbl = new QLabel("0");
    m_expTargetValueLbl->setAlignment(Qt::AlignRight);
    m_expTargetSlider = new QSlider(Qt::Horizontal);
    m_expTimeLbl = new QLabel(g_i18n->value("ids_expo_time"));
    m_expTimeValueLbl = new QPushButton("0");
    m_expTimeValueLbl->setFixedWidth(g_pFontMetrics->averageCharWidth() * 15);
    m_expTimeValueLbl->setStyleSheet(btnLabelQss);
    m_expTimeSlider = new QSlider(Qt::Horizontal);
    m_expGainLbl = new QLabel(g_i18n->value("ids_gain"));
    m_expGainValueLbl = new QPushButton("0");
    m_expGainValueLbl->setFixedWidth(g_pFontMetrics->averageCharWidth() * 15);
    m_expGainValueLbl->setStyleSheet(btnLabelQss);
    m_expGainSlider = new QSlider(Qt::Horizontal);
    m_defBtn = new QPushButton(g_i18n->value("ids_defaults"));

    QGridLayout *mainLyt = new QGridLayout();
    mainLyt->addWidget(m_aexpCkb, 0, 0);
    mainLyt->addWidget(m_expTargetLbl, 1, 0);
    mainLyt->addWidget(m_expTargetValueLbl, 1, 1);
    mainLyt->addWidget(m_expTargetSlider, 2, 0, 1, 2);
    mainLyt->addWidget(m_expTimeLbl, 3, 0);
    mainLyt->addWidget(m_expTimeValueLbl, 3, 1);
    mainLyt->addWidget(m_expTimeSlider, 4, 0, 1, 2);
    mainLyt->addWidget(m_expGainLbl, 5, 0);
    mainLyt->addWidget(m_expGainValueLbl, 5, 1);
    mainLyt->addWidget(m_expGainSlider, 6, 0, 1, 2);
    mainLyt->addWidget(m_defBtn, 7, 0, 1, 2, Qt::AlignCenter);
    setLayout(mainLyt);

    connect(m_aexpCkb, SIGNAL(stateChanged(int)), this, SLOT(OnAECkbChanged(int)));
    connect(m_expTargetSlider, SIGNAL(valueChanged(int)), this, SLOT(OnExpTargetChanged(int)));
    connect(m_expTimeSlider, SIGNAL(valueChanged(int)), this, SLOT(OnExpTimeChanged(int)));
    connect(m_expTimeValueLbl, SIGNAL(clicked()), this, SLOT(OnExpTimeValueLabelClicked()));
    connect(m_expGainSlider, SIGNAL(valueChanged(int)), this, SLOT(OnExpGainChanged(int)));
    connect(m_expGainValueLbl, SIGNAL(clicked()), this, SLOT(OnExpGainValueLabelClicked()));
    connect(m_defBtn, SIGNAL(clicked()), this, SLOT(OnDefBtnClicked()));

    connect(g_pCameraManager, SIGNAL(CamEvent(int)), this, SLOT(OnCamEvent(int)));

    EnableBtns(false);
}

void ExposureGainWidget::Notify(int notify)
{
    if(NOTIFY_CAM_OPEN == notify)
    {
        EnableBtns(true);
        m_aexpCkb->blockSignals(true);
        m_expTargetSlider->blockSignals(true);
        m_expTimeSlider->blockSignals(true);
        m_expGainSlider->blockSignals(true);
        int min = 0, max = 0;
        g_pCameraManager->GetParaRange(CAM_PARA_AEXPOTARGET, &min, &max, nullptr);
        m_expTargetSlider->setRange(min, max);
        g_pCameraManager->GetParaRange(CAM_PARA_EXPOTIME, &min, &max, nullptr);
        m_expTimeSlider->setRange(ConvertExpTimeToSlider(min), ConvertExpTimeToSlider(max));
        g_pCameraManager->GetParaRange(CAM_PARA_EXPOGAIN, &min, &max, nullptr);
        m_expGainSlider->setRange(min, max);
        int val = 0;
        g_pCameraManager->GetPara(CAM_PARA_AEXPO, &val);
        m_aexpCkb->setChecked(val > 0);
        if(m_aexpCkb->isChecked())
        {
            g_pCameraManager->GetPara(CAM_PARA_AEXPOTARGET, &val);
            m_expTargetSlider->setValue(val);
            SetExpTargetLabel(val);
        }
        else
        {
            g_pCameraManager->GetPara(CAM_PARA_EXPOTIME, &val);
            m_expTimeSlider->setValue(ConvertExpTimeToSlider(val));
            SetExpTimeLabel(val);
            g_pCameraManager->GetPara(CAM_PARA_EXPOGAIN, &val);
            m_expGainSlider->setValue(val);
            SetExpGainLabel(val);
        }
        m_aexpCkb->blockSignals(false);
        m_expTargetSlider->blockSignals(false);
        m_expTimeSlider->blockSignals(false);
        m_expGainSlider->blockSignals(false);
        UpdateBtnsEnabled();
    }
    else if(NOTIFY_CAM_CLOSE == notify)
    {
        EnableBtns(false);
    }
}

void ExposureGainWidget::OnAECkbChanged(int state)
{
    g_pCameraManager->PutPara(CAM_PARA_AEXPO, state > 0 ? 1 : 0);
    UpdateBtnsEnabled();
}

void ExposureGainWidget::OnExpTargetChanged(int value)
{
    int curVal = 0;
    g_pCameraManager->GetPara(CAM_PARA_AEXPOTARGET, &curVal);
    if(curVal != value)
    {
        g_pCameraManager->PutPara(CAM_PARA_AEXPOTARGET, value);
        SetExpTargetLabel(value);
    }
}

void ExposureGainWidget::OnExpTimeChanged(int value)
{
    int curVal = 0;
    const int val = ConvertExpTimeToCamera(value);
    g_pCameraManager->GetPara(CAM_PARA_EXPOTIME, &curVal);
    if(val != curVal)
    {
        g_pCameraManager->PutPara(CAM_PARA_EXPOTIME, val);
        SetExpTimeLabel(val);
    }
}

void ExposureGainWidget::OnExpTimeValueLabelClicked()
{
    bool ok;
    int min = 0, max = 0;
    g_pCameraManager->GetParaRange(CAM_PARA_EXPOTIME, &min, &max, nullptr);
    int value = QInputDialog::getInt(nullptr, g_i18n->value("ids_expo_time"),
                                     g_i18n->value("ids_expo_time") + g_i18n->value("ids_colon") + "[" + QString::number(min) + ", " + QString::number(max) + "]us"
                                     , 0, min, max, 1, &ok, windowFlags() & ~Qt::WindowMaximizeButtonHint & ~Qt::WindowMinimizeButtonHint);
    if(ok)
        m_expTimeSlider->setValue(ConvertExpTimeToSlider(value));
}

void ExposureGainWidget::OnExpGainChanged(int value)
{
    int curVal = 0;
    g_pCameraManager->GetPara(CAM_PARA_EXPOGAIN, &curVal);
    if(curVal != value)
    {
        g_pCameraManager->PutPara(CAM_PARA_EXPOGAIN, value);
        SetExpGainLabel(value);
    }
}

void ExposureGainWidget::OnExpGainValueLabelClicked()
{
    bool ok;
    int min = 0, max = 0;
    g_pCameraManager->GetParaRange(CAM_PARA_EXPOGAIN, &min, &max, nullptr);
    int value = QInputDialog::getInt(nullptr, g_i18n->value("ids_gain"),
                                     g_i18n->value("ids_gain") + g_i18n->value("ids_colon") + "[" + QString::number(min) + ", " + QString::number(max) + "]"
                                     , 0, min, max, 1, &ok, windowFlags() & ~Qt::WindowMaximizeButtonHint & ~Qt::WindowMinimizeButtonHint);
    if(ok)
        m_expGainSlider->setValue(value);
}

void ExposureGainWidget::OnDefBtnClicked()
{
    int def = 0;
    g_pCameraManager->GetParaRange(CAM_PARA_AEXPO, nullptr, nullptr, &def);
    m_aexpCkb->setChecked(def > 0);
    if(m_aexpCkb->isChecked())
    {
        g_pCameraManager->GetParaRange(CAM_PARA_AEXPOTARGET, nullptr, nullptr, &def);
        m_expTargetSlider->setValue(def);
    }
    else
    {
        g_pCameraManager->GetParaRange(CAM_PARA_EXPOTIME, nullptr, nullptr, &def);
        m_expTimeSlider->setValue(ConvertExpTimeToSlider(def));
        g_pCameraManager->GetParaRange(CAM_PARA_EXPOGAIN, nullptr, nullptr, &def);
        m_expGainSlider->setValue(def);
    }
}

void ExposureGainWidget::OnCamEvent(int event)
{
    if(CAM_EVENT_EXP_CHANGED == event)
    {
        int val = 0;
        g_pCameraManager->GetPara(CAM_PARA_EXPOTIME, &val);
        m_expTimeSlider->blockSignals(true);
        m_expTimeSlider->setValue(ConvertExpTimeToSlider(val));
        SetExpTimeLabel(val);
        m_expTimeSlider->blockSignals(false);

        g_pCameraManager->GetPara(CAM_PARA_EXPOGAIN, &val);
        m_expGainSlider->blockSignals(true);
        m_expGainSlider->setValue(val);
        SetExpGainLabel(val);
        m_expGainSlider->blockSignals(false);
    }
}

void ExposureGainWidget::EnableBtns(bool bEnabled)
{
    m_aexpCkb->setEnabled(bEnabled);
    m_expTargetSlider->setEnabled(bEnabled);
    m_expTimeSlider->setEnabled(bEnabled);
    m_expTimeValueLbl->setEnabled(bEnabled);
    m_expGainSlider->setEnabled(bEnabled);
    m_expGainValueLbl->setEnabled(bEnabled);
    m_defBtn->setEnabled(bEnabled);
}

void ExposureGainWidget::UpdateBtnsEnabled()
{
    m_expTargetSlider->setEnabled(m_aexpCkb->isChecked());
    m_expTimeSlider->setEnabled(!m_aexpCkb->isChecked());
    m_expTimeValueLbl->setEnabled(!m_aexpCkb->isChecked());
    m_expGainSlider->setEnabled(!m_aexpCkb->isChecked());
    m_expGainValueLbl->setEnabled(!m_aexpCkb->isChecked());
}

void ExposureGainWidget::SetExpTargetLabel(int val)
{
    m_expTargetValueLbl->setText(QString::number(val));
}

void ExposureGainWidget::SetExpTimeLabel(int val)
{
    const uint us = val;
    const uint iH = us / 3600000000;
    const uint iM = (us - iH * 3600000000) / 60000000;
    const uint iS = (us - iH * 3600000000 - iM * 60000000) / 1000000;
    const uint ims = (us - iH * 3600000000 - iM * 60000000 - iS * 1000000) / 1000;
    const uint ius = val - iH * 3600000000 - iM * 60000000 - iS * 1000000 - ims * 1000;

    QString str;
    if(0 != iH)
        str.sprintf("%.2dh%.2dm%.2ds%d.%.3dms", iH, iM, iS, ims, ius);
    else if(0 != iM)
        str.sprintf("%.2dm%.2ds%d.%.3dms", iM, iS, ims, ius);
    else if(0 != iS)
        str.sprintf("%.2ds%d.%.3dms", iS, ims, ius);
    else if(0 != ims)
        str.sprintf("%d.%.3dms", ims, ius);
    else
        str.sprintf("%dus", val);

    m_expTimeValueLbl->setText(str);
}

void ExposureGainWidget::SetExpGainLabel(int val)
{
    m_expGainValueLbl->setText(QString::number(val));
}

int ExposureGainWidget::ConvertExpTimeToSlider(int time) const
{
    return static_cast<int>(ConvertExpTimeToSliderV2(time / 1000.0) * 1000.0);
}

int ExposureGainWidget::ConvertExpTimeToCamera(int time) const
{
    return static_cast<int>(ConvertExpTimeToCameraV2(time / 1000.0) * 1000.0);
}

qreal ExposureGainWidget::ConvertExpTimeToSliderV2(qreal time) const
{
    int min = 0, max = 0;
    g_pCameraManager->GetParaRange(CAM_PARA_EXPOTIME, &min, &max, nullptr);
    qreal result = 0;
    qreal tmax = max / 1000.0;
    if(tmax <= 100)
        return time;
    qreal a = tmax / 100 - 1;
    if(time <= 100)
        result = time * a;
    else
        result = time + 100 * (a - 1);
    return result;
}

qreal ExposureGainWidget::ConvertExpTimeToCameraV2(qreal time) const
{
    int min = 0, max = 0;
    g_pCameraManager->GetParaRange(CAM_PARA_EXPOTIME, &min, &max, nullptr);
    qreal result = 0;
    qreal tmax = max / 1000.0;
    if(tmax <= 100)
        return time;
    qreal a = tmax / 100 - 1;
    if(time <= 100 * a)
        result = time / a;
    else
        result = time - 100 * (a - 1);
    return result;
}
