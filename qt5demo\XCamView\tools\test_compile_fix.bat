@echo off
echo Testing compilation after database refactor...
echo.

cd /d "D:\work\XCamView\build-XCamView-Desktop_Qt_5_14_2_MSVC2017_64bit-Release"

echo Cleaning previous build...
"D:\Qt\Qt5.14.2\Tools\QtCreator\bin\jom.exe" clean

echo.
echo Starting compilation test...
echo.

"D:\Qt\Qt5.14.2\Tools\QtCreator\bin\jom.exe" -f Makefile.Release

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ========================================
    echo SUCCESS: Compilation completed successfully!
    echo ========================================
    echo.
    echo The database refactor fixes have been applied correctly.
    echo You can now run the application.
) else (
    echo.
    echo ========================================
    echo ERROR: Compilation failed!
    echo ========================================
    echo.
    echo Please check the error messages above.
    echo Error code: %ERRORLEVEL%
)

echo.
pause
