#include "displayview.h"
#include "../../../mainwindow.h"
#include "measure/tpmeasuremanager.h"
#include "measure/tpshape.h"
#include "../../module/auxrect/auxrect.h"
#include <QMouseEvent>
#include <QPainter>
#include <QPaintEvent>
#include <QWheelEvent>
#include <QResizeEvent>
#include <QHBoxLayout>
#include <QVBoxLayout>

DisplayWidget::DisplayWidget(QWidget* parent, bool bEnableAWBRoi)
    : ViewWidget(parent), m_pMeasureManager(nullptr), m_pAWBRoiRect(nullptr), m_bAWBRoiBtnDown(false)
    , m_bMeasureBtnDown(false)
{
    setMouseTracking(true);

    m_pMeasureManager = new TpMeasureManager();
    connect(m_pMeasureManager, SIGNAL(calibrationLengthChanged(double)), g_pMainWindow, SLOT(OnCalibrationLength(double)));
    connect(m_pMeasureManager, SIGNAL(textDlg()), g_pMainWindow, SLOT(OnTextEditDlg()));

    if(bEnableAWBRoi)
    {
        m_pAWBRoiRect  = new AuxRect(g_i18n->value("ids_white_balance"), Qt::red, RECT_WB, this);
        connect(m_pAWBRoiRect, &AuxRect::sizeChangedFini, this, &DisplayWidget::OnAuxRectChangedFini);
    }

    connect(this, SIGNAL(ScaleChanged(double)), this, SLOT(OnScaleChanged(double)));
}

DisplayWidget::~DisplayWidget()
{
    if(m_pMeasureManager)
        delete m_pMeasureManager;
}
void DisplayWidget::EnableCalibration(bool bEnabled)
{
    if(m_pMeasureManager && m_pImage)
    {
        if(bEnabled)
        {
            m_pMeasureManager->setCaliCenter(m_ptCenter);
            m_pMeasureManager->setCaliWidth(m_pImage->width() / 2);
        }
        m_pMeasureManager->enableCalibration(bEnabled);
        if(bEnabled)
            m_pMeasureManager->setMeasureMode(M_Selected);
    }
}

TpMeasureManager *DisplayWidget::GetMeasureManager() const
{
    return m_pMeasureManager;
}

void DisplayWidget::OnAuxRectChangedFini(const QRectF &newRect, const eAuxRectType type)
{
    if(RECT_WB == type)
    {
        g_pCameraManager->PutPara(CAM_PARA_WBROILEFT, newRect.toAlignedRect().left());
        g_pCameraManager->PutPara(CAM_PARA_WBROITOP, newRect.toAlignedRect().top());
        g_pCameraManager->PutPara(CAM_PARA_WBROIWIDTH, newRect.toAlignedRect().width());
        g_pCameraManager->PutPara(CAM_PARA_WBROIHEIGHT, newRect.toAlignedRect().height());
    }
}

void DisplayWidget::OnScaleChanged(double scale)
{
    if(m_pAWBRoiRect)
        m_pAWBRoiRect->setScale(scale);
    m_pMeasureManager->setScale(scale);
}

void DisplayWidget::mouseMoveEvent(QMouseEvent *event)
{
    QPointF curPos = MapToImageCoord(event->pos());
    if (m_pAWBRoiRect && m_bAWBRoiBtnDown)
    {
        m_pAWBRoiRect->mouseMove(curPos);
        update();
        event->accept();
    }
    else if(m_bMeasureBtnDown || MMProcedure_DRAWING == m_pMeasureManager->getCurMMProc())
    {
        m_pMeasureManager->mouseMove(curPos);
        update();
    }
    else
    {
        if(m_pAWBRoiRect && (POS_NONE != m_pAWBRoiRect->mouseHover(curPos)))
            UpdateAuxRectCursor(m_pAWBRoiRect->mouseHover(curPos));
        else
        {
            Qt::CursorShape shape = Qt::ArrowCursor;
            eMMCursor cursor = m_pMeasureManager->getCursorShape(curPos, shape);
            if(MMCursor_Select != cursor)
            {
                switch(cursor)
                {
                case MMCursor_System: setCursor(shape); break;
                case MMCursor_Drag: setCursor(QCursor(QPixmap(":/images/dragMoveCursor.png"))); break;
                case MMCursor_Move: setCursor(QCursor(QPixmap(":/images/labelMoveCursor.png"))); break;
                default: setCursor(m_defCurShape); break;
                }
            }
            else
            {
                setCursor(m_defCurShape);
                QWidget::mouseMoveEvent(event);
            }
        }
    }
}

void DisplayWidget::mousePressEvent(QMouseEvent *event)
{
    const QPointF curPos = MapToImageCoord(event->pos());
    if (event->button() == Qt::LeftButton)
    {
        if(m_pAWBRoiRect && m_pAWBRoiRect->hasFocus(curPos))
        {
            m_pAWBRoiRect->mousePress(curPos);
            m_bAWBRoiBtnDown = true;
            update();
            event->accept();
        }
        else if(m_pMeasureManager->mousePress(event, curPos))
        {
            m_bMeasureBtnDown = true;
            event->accept();
            update();
        }
        else
            QWidget::mousePressEvent(event);
    }
    else if(event->button() == Qt::RightButton)
    {
        m_pMeasureManager->mousePress(event, curPos);
    }
    else
        QWidget::mousePressEvent(event);
}

void DisplayWidget::mouseReleaseEvent(QMouseEvent *event)
{
    if(m_pAWBRoiRect && m_bAWBRoiBtnDown)
    {
        m_bAWBRoiBtnDown = false;
        m_pAWBRoiRect->mouseRelease();
        update();
        event->accept();
    }
    else if(m_bMeasureBtnDown)
    {
        const QPointF curPos = MapToImageCoord(event->pos());
        m_bMeasureBtnDown = false;
        m_pMeasureManager->mouseRelease(event, curPos);
        event->accept();
        update();
    }
    else
        QWidget::mouseReleaseEvent(event);
}

void DisplayWidget::paint(QPainter &painter)
{
    if (m_pAWBRoiRect)
        m_pAWBRoiRect->paint(painter, m_ptCenter);
    if(m_pMeasureManager)
    {
        TpShape::setOffset(-m_ptCenter);
        m_pMeasureManager->paint(painter);
    }
}

void DisplayWidget::UpdateMeasureType()
{
    switch(g_measureActType)
    {
    case ACT_SELECT: m_pMeasureManager->setMeasureMode(M_Selected); break;
    case ACT_POINT: m_pMeasureManager->setMeasureMode(M_Point); break;
    case ACT_ANGLE3P: m_pMeasureManager->setMeasureMode(M_Angle); break;
    case ACT_ANGLE4P: m_pMeasureManager->setMeasureMode(M_4PtAngle); break;
    case ACT_ARROW: m_pMeasureManager->setMeasureMode(M_Arrow); break;
    case ACT_ARBLINE: m_pMeasureManager->setMeasureMode(M_ArbLine); break;
    case ACT_PARALLEL4P: m_pMeasureManager->setMeasureMode(M_Parallel); break;
    case ACT_PARALLEL8P: m_pMeasureManager->setMeasureMode(M_TwoParallels); break;
    case ACT_HLINE: m_pMeasureManager->setMeasureMode(M_HLine); break;
    case ACT_VLINE: m_pMeasureManager->setMeasureMode(M_VLine); break;
    case ACT_VERTICAL3P: m_pMeasureManager->setMeasureMode(M_Vertical); break;
    case ACT_VERTICAL4P: m_pMeasureManager->setMeasureMode(M_4PtVertical); break;
    case ACT_RECT: m_pMeasureManager->setMeasureMode(M_Rectangle); break;
    case ACT_CIRCLECR: m_pMeasureManager->setMeasureMode(M_Circle); break;
    case ACT_CIRCLE2P: m_pMeasureManager->setMeasureMode(M_2PtCircle); break;
    case ACT_CIRCLE3P: m_pMeasureManager->setMeasureMode(M_3PtCircle); break;
    case ACT_ELLIPSE: m_pMeasureManager->setMeasureMode(M_Ellipse); break;
    case ACT_ANNULUS: m_pMeasureManager->setMeasureMode(M_Annulus); break;
    case ACT_TWOCIRCLECR: m_pMeasureManager->setMeasureMode(M_TwoCircles); break;
    case ACT_TWOCIRCLE3P: m_pMeasureManager->setMeasureMode(M_3PtTwoCircles); break;
    case ACT_ARC: m_pMeasureManager->setMeasureMode(M_Arc); break;
    case ACT_POLYGON: m_pMeasureManager->setMeasureMode(M_Polygon); break;
    case ACT_CURVE: m_pMeasureManager->setMeasureMode(M_Curve); break;
    case ACT_TEXT: m_pMeasureManager->setMeasureMode(M_Text); break;
    default: break;
    }
}

void DisplayWidget::DoZoomChangedExt()
{
    if(m_pAWBRoiRect)
        m_pAWBRoiRect->setScale(m_fScale);
    m_pMeasureManager->setScale(m_fScale);
}

void DisplayWidget::UpdateAuxRectCursor(ePOS type)
{
    switch (type) {
    case POS_LEFT_TOP:
    case POS_RIGHT_BOTTOM:
        setCursor(Qt::SizeFDiagCursor);
        break;
    case POS_LEFT_BOTTOM:
    case POS_RIGHT_TOP:
        setCursor(Qt::SizeBDiagCursor);
        break;
    case POS_LEFT_CENTER:
    case POS_RIGHT_CENTER:
        setCursor(Qt::SizeHorCursor);
        break;
    case POS_TOP_CENTER:
    case POS_BOTTOM_CENTER:
        setCursor(Qt::SizeVerCursor);
        break;
    case POS_INNER:
        setCursor(Qt::SizeAllCursor);
        break;
    default:
        setCursor(m_defCurShape);
        break;
    }
}

DisplayView::DisplayView(DisplayWidget* pDisplayWidget, QWidget *parent)
    : View(pDisplayWidget, parent)
{

}

void DisplayView::EnableCalibration(bool bEnabled)
{
    if(qobject_cast<DisplayWidget*>(m_pViewWidget))
        qobject_cast<DisplayWidget*>(m_pViewWidget)->EnableCalibration(bEnabled);
}

TpMeasureManager *DisplayView::GetMeasureManager() const
{
    if(qobject_cast<DisplayWidget*>(m_pViewWidget))
        return qobject_cast<DisplayWidget*>(m_pViewWidget)->GetMeasureManager();
    return nullptr;
}
