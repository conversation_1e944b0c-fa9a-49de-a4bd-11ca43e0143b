#ifndef TPDATABASECONNECTION_H
#define TPDATABASECONNECTION_H

#include "TpDatabaseConfig.h"
#include "TpDatabaseResult.h"
#include <QObject>
#include <QSqlDatabase>
#include <QSqlQuery>
#include <QSqlError>
#include <QTimer>
#include <QMutex>

#if defined(TPDATABASE_LIBRARY)
#define TPDATABASE_EXPORT Q_DECL_EXPORT
#else
#define TPDATABASE_EXPORT Q_DECL_IMPORT
#endif

/**
 * @brief 数据库连接状态枚举
 */
enum class TpDatabaseConnectionState {
    NotConnected,       // 未连接
    Connecting,         // 连接中
    Connected,          // 已连接
    ConnectionFailed,   // 连接失败
    Reconnecting,       // 重连中
    Disconnecting       // 断开连接中
};

/**
 * @brief 数据库连接管理类
 * 
 * 基于Qt5Demo的TpdatabaseHandler扩展而来，提供：
 * - 数据库连接管理
 * - 连接状态监控
 * - 自动重连机制
 * - 健康检查
 * - 线程安全
 */
class TPDATABASE_EXPORT TpDatabaseConnection : public QObject
{
    Q_OBJECT

public:
    /**
     * @brief 构造函数
     * @param config 数据库配置
     * @param parent 父对象
     */
    explicit TpDatabaseConnection(const TpDatabaseConfig& config, QObject *parent = nullptr);
    
    /**
     * @brief 构造函数（使用默认配置）
     * @param parent 父对象
     */
    explicit TpDatabaseConnection(QObject *parent = nullptr);
    
    /**
     * @brief 析构函数
     */
    ~TpDatabaseConnection();

    // 连接管理
    
    /**
     * @brief 连接到数据库
     * @return 连接结果
     */
    TpDatabaseResult connectToDatabase();
    
    /**
     * @brief 断开数据库连接
     */
    void disconnectFromDatabase();
    
    /**
     * @brief 检查是否已连接
     * @return true表示已连接
     */
    bool isConnected() const;
    
    /**
     * @brief 获取连接状态
     * @return 连接状态枚举值
     */
    TpDatabaseConnectionState connectionState() const;
    
    /**
     * @brief 测试连接有效性
     * @return 测试结果
     */
    TpDatabaseResult testConnection();

    // 配置管理
    
    /**
     * @brief 获取当前配置
     * @return 数据库配置对象
     */
    TpDatabaseConfig config() const;
    
    /**
     * @brief 设置新配置
     * @param config 新的数据库配置
     * @return 设置结果
     */
    TpDatabaseResult setConfig(const TpDatabaseConfig& config);
    
    /**
     * @brief 重新加载配置
     * @param configPath 配置文件路径，为空则自动搜索
     * @return 加载结果
     */
    TpDatabaseResult reloadConfig(const QString& configPath = "");

    // 数据库访问
    
    /**
     * @brief 获取数据库对象引用（非线程安全）
     * @return 数据库对象引用
     */
    QSqlDatabase& database();
    
    /**
     * @brief 获取数据库对象（线程安全）
     * @return 数据库对象副本
     */
    QSqlDatabase getDatabase() const;
    
    /**
     * @brief 获取连接名称
     * @return 连接名称
     */
    QString connectionName() const;

    // 用户认证（兼容Qt5Demo）
    
    /**
     * @brief 验证用户登录
     * @param username 用户名
     * @param password 密码
     * @return 验证结果
     */
    TpDatabaseResult validateUser(const QString& username, const QString& password);

    // 健康检查和监控
    
    /**
     * @brief 启用健康检查
     * @param enabled 是否启用
     */
    void setHealthCheckEnabled(bool enabled);
    
    /**
     * @brief 检查健康检查是否启用
     * @return true表示已启用
     */
    bool isHealthCheckEnabled() const;
    
    /**
     * @brief 获取最后错误信息
     * @return 错误信息字符串
     */
    QString lastError() const;
    
    /**
     * @brief 获取连接统计信息
     * @return 统计信息映射
     */
    QVariantMap getConnectionStats() const;

    // 线程安全
    
    /**
     * @brief 获取互斥锁（用于外部同步）
     * @return 互斥锁引用
     */
    QMutex& mutex() const;

signals:
    /**
     * @brief 连接状态变化信号
     * @param state 新的连接状态
     */
    void connectionStateChanged(TpDatabaseConnectionState state);
    
    /**
     * @brief 错误发生信号
     * @param error 错误信息
     */
    void errorOccurred(const QString& error);
    
    /**
     * @brief 连接丢失信号
     */
    void connectionLost();
    
    /**
     * @brief 连接恢复信号
     */
    void connectionRestored();

private slots:
    /**
     * @brief 健康检查定时器槽函数
     */
    void onHealthCheckTimer();
    
    /**
     * @brief 重连定时器槽函数
     */
    void onReconnectTimer();

private:
    // 核心方法
    TpDatabaseResult createConnection();
    TpDatabaseResult performHealthCheck();
    void handleConnectionError(const QSqlError& error);
    void startHealthCheckTimer();
    void stopHealthCheckTimer();
    void startReconnectTimer();
    void stopReconnectTimer();
    
    // 工具方法
    QString generateConnectionName();
    void logConnectionAttempt() const;
    void logConnectionResult(bool success) const;
    void updateConnectionStats();
    
    // 状态管理
    void setState(TpDatabaseConnectionState state);
    void setLastError(const QString& error);

private:
    // 配置和状态
    TpDatabaseConfig m_config;
    TpDatabaseConnectionState m_connectionState;
    QString m_connectionName;
    QString m_lastError;
    
    // 数据库对象
    QSqlDatabase m_database;
    
    // 定时器
    QTimer* m_healthCheckTimer;
    QTimer* m_reconnectTimer;
    bool m_healthCheckEnabled;
    
    // 统计信息
    QDateTime m_connectionTime;
    QDateTime m_lastHealthCheck;
    int m_reconnectAttempts;
    int m_queryCount;
    int m_errorCount;
    
    // 线程安全
    mutable QMutex m_mutex;
    
    // 静态计数器
    static int s_connectionCounter;
    static QMutex s_counterMutex;
};

#endif // TPDATABASECONNECTION_H
