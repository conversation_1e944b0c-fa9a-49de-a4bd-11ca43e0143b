-- 为liu用户创建MySQL远程连接权限
-- 解决 "Access denied for user 'liu'@'DESKTOP-KDN5QHV'" 问题

-- 1. 创建liu用户的各种连接方式
-- 通配符用户（最灵活）
CREATE USER IF NOT EXISTS 'liu'@'%' IDENTIFIED BY 'SecurePass123!';
GRANT ALL PRIVILEGES ON student.* TO 'liu'@'%';

-- 特定主机名用户
CREATE USER IF NOT EXISTS 'liu'@'DESKTOP-KDN5QHV' IDENTIFIED BY 'SecurePass123!';
GRANT ALL PRIVILEGES ON student.* TO 'liu'@'DESKTOP-KDN5QHV';

-- IP网段用户
CREATE USER IF NOT EXISTS 'liu'@'192.168.6.%' IDENTIFIED BY 'SecurePass123!';
GRANT ALL PRIVILEGES ON student.* TO 'liu'@'192.168.6.%';

-- 本地IP用户
CREATE USER IF NOT EXISTS 'liu'@'127.0.0.1' IDENTIFIED BY 'SecurePass123!';
GRANT ALL PRIVILEGES ON student.* TO 'liu'@'127.0.0.1';

-- localhost用户
CREATE USER IF NOT EXISTS 'liu'@'localhost' IDENTIFIED BY 'SecurePass123!';
GRANT ALL PRIVILEGES ON student.* TO 'liu'@'localhost';

-- 2. 刷新权限
FLUSH PRIVILEGES;

-- 3. 验证用户创建
SELECT user, host FROM mysql.user WHERE user = 'liu' ORDER BY host;

-- 4. 显示权限
SHOW GRANTS FOR 'liu'@'%';
SHOW GRANTS FOR 'liu'@'DESKTOP-KDN5QHV';
SHOW GRANTS FOR 'liu'@'192.168.6.%';

-- 完成！现在liu用户应该可以从任何位置连接了
