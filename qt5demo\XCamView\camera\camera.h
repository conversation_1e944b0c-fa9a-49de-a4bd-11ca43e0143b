﻿#ifndef CAMERA_H
#define CAMERA_H

#include <QObject>
#include <QString>
#include <QList>
#include "cameradef.h"

class Camera : public QObject
{
    Q_OBJECT

public:
    explicit Camera(QObject* parent = nullptr);

    virtual bool InitLoader();

    virtual QList<CameraSt> Enum();

    virtual bool Open(const CameraSt& info);
    const CameraSt& GetCamInfo() const;
    virtual void Close();
    virtual void Stop();
    virtual HRESULT Start();
    virtual bool IsOpened();
    virtual bool IsRunning();

    virtual HRESULT PutPara(unsigned nId, int val);
    virtual HRESULT GetPara(unsigned nId, int* pVal);
    virtual HRESULT GetParaRange(unsigned nId, int* pMin, int* pMax, int* pDef);

    virtual int  GetImageWidth();
    virtual int  GetImageHeight();
    virtual void GetImage(void* pFrameBuffer);

signals:
    void Notify(int notify);
    void CamEvent(int event);

protected:
    CameraSt m_camInfo;
};

#endif
