#include "resolutionwidget.h"
#include "../../mainwindow.h"
#include <QHBoxLayout>
#include <QGridLayout>

ResolutionWidget::ResolutionWidget(QWidget* parent)
    : CollapseWidget(QIcon(":/images/exposure.png"), g_i18n->value("ids_resolution"), parent)
{
    m_pCaptureBtn = new QPushButton(QIcon(":/images/capture.png"), g_i18n->value("ids_snap"));
    m_pCaptureBtn->setFixedHeight(g_pFontMetrics->height() * 5);
    m_pCaptureBtn->setIconSize(QSize(g_pFontMetrics->height() * 4.8, g_pFontMetrics->height() * 4.8));
    m_pRecordBtn = new QPushButton(QIcon(":/images/record_start.png"), g_i18n->value("ids_record"));
    m_pRecordBtn->setFixedHeight(g_pFontMetrics->height() * 5);
    m_pRecordBtn->setIconSize(QSize(g_pFontMetrics->height() * 4.8, g_pFontMetrics->height() * 4.8));
    m_pLiveResLbl = new QLabel(g_i18n->value("ids_live") + g_i18n->value("ids_colon"));
    m_pLiveResCbx = new QComboBox();

    QHBoxLayout* liveResLyt = new QHBoxLayout();
    liveResLyt->addWidget(m_pLiveResLbl);
    liveResLyt->addWidget(m_pLiveResCbx, 1);

    QGridLayout* mainLyt = new QGridLayout();
    mainLyt->addWidget(m_pCaptureBtn, 0, 0);
    mainLyt->addWidget(m_pRecordBtn, 0, 1);
    mainLyt->addLayout(liveResLyt, 1, 0, 1, 2);
    setLayout(mainLyt);

    connect(m_pCaptureBtn, SIGNAL(clicked()), g_pSignalConnect, SIGNAL(Capture()));
    connect(m_pLiveResCbx, SIGNAL(activated(int)), this, SLOT(OnLiveResCbxActived(int)));

    EnableBtns(false);
}

void ResolutionWidget::Notify(int notify)
{
    if(NOTIFY_CAM_OPEN == notify)
    {
        EnableBtns(true);
        int resCnt = 0;
        g_pCameraManager->GetParaRange(CAM_PARA_RES, nullptr, &resCnt, nullptr);
        m_pLiveResCbx->clear();
        for(int i = 0; i < resCnt; ++i)
        {
            int width = 0, height = 0;
            g_pCameraManager->GetPara(CAM_PARA_WIDTH | i, &width);
            g_pCameraManager->GetPara(CAM_PARA_HEIGHT | i, &height);
            m_pLiveResCbx->addItem(QString("%1 x %2").arg(width).arg(height));
            g_minResWidth = g_minResWidth < width ?  g_minResWidth : width;
            g_minResHeight = g_minResHeight < height ?  g_minResHeight : height;
            g_maxResWidth = g_maxResWidth > width ?  g_maxResWidth : width;
            g_maxResHeight = g_maxResHeight > height ?  g_maxResHeight : height;
        }
    }
    else if(NOTIFY_CAM_CLOSE == notify)
    {
        EnableBtns(false);
    }
}

void ResolutionWidget::OnLiveResCbxActived(int index)
{
    g_pCameraManager->ChangeResolution(index);
}

void ResolutionWidget::EnableBtns(bool bEnabled)
{
    m_pCaptureBtn->setEnabled(bEnabled);
    m_pRecordBtn->setEnabled(bEnabled);
    m_pLiveResCbx->setEnabled(bEnabled);
}
