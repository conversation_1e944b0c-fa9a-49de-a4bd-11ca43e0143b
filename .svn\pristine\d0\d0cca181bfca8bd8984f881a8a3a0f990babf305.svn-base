QT       += core gui widgets xml

greaterThan(QT_MAJOR_VERSION, 4): QT += widgets

CONFIG += c++17
DEFINES += QT_DEPRECATED_WARNINGS
INCLUDEPATH += $$PWD/lib/include

win32 {
    contains(QT_ARCH, x86_64) || contains(QT_ARCH, amd64) {
        CONFIG(debug, debug|release){
            LIBS += $$PWD/lib/x64/TpMeasureManagerd.lib
        } else {
            LIBS += $$PWD/lib/x64/TpMeasureManager.lib
        }
    } else {
        CONFIG(debug, debug|release){
            LIBS += $$PWD/lib/x86/TpMeasureManagerd.lib
        } else {
            LIBS += $$PWD/lib/x86/TpMeasureManager.lib
        }
    }
}

SOURCES += \
    main.cpp \
    global.cpp \
    mainwindow.cpp \
    module/camera/cameramanager.cpp \
    module/camera/camera.cpp \
    module/camera/uvchamcam/uvchamcam.cpp \
    module/camera/uvchamcam/uvchamloader.cpp \
    panel/collapsewidget.cpp \
    panel/leftpanel.cpp \
    panel/camerapanel/camerapanel.cpp \
    panel/camerapanel/cameralistwidget.cpp \
    panel/camerapanel/resolutionwidget.cpp \
    panel/camerapanel/exposuregainwidget.cpp \
    panel/camerapanel/whitebalancewidget.cpp \
    panel/camerapanel/coloradjustwidget.cpp \
    panel/camerapanel/powerfrequencywidget.cpp \
    panel/monitorpanel/monitorwidget.cpp \
    panel/monitorpanel/monitordialog.cpp \
    panel/chatpanel/chatwidget.cpp \
    panel/centralpanel/tptabwidget.cpp \
    panel/centralpanel/view.cpp \
    panel/centralpanel/displayview/displayview.cpp \
    panel/centralpanel/displayview/previewview.cpp \
    panel/centralpanel/displayview/imageview.cpp \
    panel/centralpanel/monitorview/monitorview.cpp \
    panel/measure/tpcalibrationdlg.cpp \
    panel/measure/tptexteditdlg.cpp \
    panel/login/logindialog.cpp \
    util/i18n/i18n.cpp \
    util/TpSaveLoader/tpsaveloader.cpp \
    util/auxrect/auxrect.cpp \
    util/tutils/tutils.cpp \
    util/TpSignalConnect/TpSignalConnect.cpp

HEADERS += \
    global.h \
    mainwindow.h \
    module/camera/cameramanager.h \
    module/camera/cameradef.h \
    module/camera/camera.h \
    module/camera/uvchamcam/uvchamcam.h \
    module/camera/uvchamcam/uvchamloader.h \
    panel/collapsewidget.h \
    panel/leftpanel.h \
    panel/camerapanel/camerapanel.h \
    panel/camerapanel/cameralistwidget.h \
    panel/camerapanel/resolutionwidget.h \
    panel/camerapanel/exposuregainwidget.h \
    panel/camerapanel/whitebalancewidget.h \
    panel/camerapanel/coloradjustwidget.h \
    panel/camerapanel/powerfrequencywidget.h \
    panel/monitorpanel/monitorwidget.h \
    panel/monitorpanel/monitordialog.h \
    panel/chatpanel/chatwidget.h \
    panel/centralpanel/tptabwidget.h \
    panel/centralpanel/view.h \
    panel/centralpanel/displayview/displayview.h \
    panel/centralpanel/displayview/previewview.h \
    panel/centralpanel/displayview/imageview.h \
    panel/centralpanel/monitorview/monitorview.h \
    panel/measure/tpcalibrationdlg.h \
    panel/measure/tptexteditdlg.h \
    panel/login/logindialog.h \
    util/i18n/i18n.h \
    util/TpSaveLoader/tpsaveloader.h \
    util/auxrect/auxrect.h \
    util/tutils/tutils.h \
    util/TpSignalConnect/TpSignalConnect.h

PRECOMPILED_HEADER = predef.h
RESOURCES += res/XCamView.qrc
