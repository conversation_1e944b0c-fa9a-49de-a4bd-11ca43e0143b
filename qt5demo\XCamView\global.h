#ifndef GLOBAL_H
#define GLOBAL_H

#include <QFontMetrics>
#include <QString>
#include <QList>

typedef struct
{
    QString magnification;
    double resolution;
    int unit;
}Calibration;

// 添加 MessageType 枚举定义，供聊天和监控模块共享
enum MessageType {
    Heartbeat,
    Message,
    NewParticipant,
    Participantleft,
    FileName,
    Refuse,
    UserListRequest,    // 请求用户列表
    UserListResponse    // 响应用户列表
};

extern QFontMetrics* g_pFontMetrics;
extern bool g_bWBDlgHidden;
extern int g_WBMode;
extern QList<Calibration> g_caliList;
extern int g_caliIndex;

extern int g_zoomcnt;
extern int g_zoom[];
extern int g_zoomIndex;
extern double g_zoomScale;

extern double g_zoomWheel[];
extern int g_zoomWheelCnt;
int GetZoomWheelIndex(double scale, bool bUp);

extern int g_measureActType;

extern int g_minResWidth;
extern int g_minResHeight;
extern int g_maxResWidth;
extern int g_maxResHeight;

enum LogLevel {
    Log_Debug,
    Log_Info,
    Log_Warning,
    Log_Error,
    Log_Critical
};

void LogInit();
void Log(const char* format, ...);
void Log(LogLevel level, const char* format, ...);
void Log(const QString& str, LogLevel level = Log_Debug);

#endif // GLOBAL_H
