# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.30

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: XCamView
# Configurations: Release
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.5


#############################################
# Set configuration variable for custom commands.

CONFIGURATION = Release
# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles\rules.ninja

# =============================================================================

#############################################
# Logical path to working directory; prefix for absolute paths.

cmake_ninja_workdir = D$:\work\XCamView\XCamView\build\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\
# =============================================================================
# Object build statements for EXECUTABLE target XCamView


#############################################
# Order-only phony target for XCamView

build cmake_object_order_depends_target_XCamView: phony || XCamView_autogen XCamView_autogen\PNK5WDWK6L\qrc_XCamView.cpp XCamView_autogen\mocs_compilation.cpp XCamView_autogen\timestamp XCamView_autogen_timestamp_deps

build CMakeFiles\XCamView.dir\cmake_pch.cxx.obj: CXX_COMPILER__XCamView_unscanned_Release D$:\work\XCamView\XCamView\build\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\CMakeFiles\XCamView.dir\cmake_pch.cxx | CMakeFiles\XCamView.dir\cmake_pch.hxx || cmake_object_order_depends_target_XCamView
  DEFINES = -DQT_CORE_LIB -DQT_GUI_LIB -DQT_NO_DEBUG -DQT_SQL_LIB -DQT_WIDGETS_LIB -DQT_XML_LIB -DUNICODE -DWIN32 -DWIN64 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -D_UNICODE -D_WIN64
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /O2 /Ob2 /DNDEBUG -std:c++17 -MD -Zc:__cplusplus -permissive- -utf-8 /YcD:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/CMakeFiles/XCamView.dir/cmake_pch.hxx /FpD:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/CMakeFiles/XCamView.dir/./cmake_pch.cxx.pch /FID:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/CMakeFiles/XCamView.dir/cmake_pch.hxx
  INCLUDES = -ID:\work\XCamView\XCamView\build\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\XCamView_autogen\include -ID:\work\XCamView\XCamView\lib\include -external:IE:\Qt\6.10.0\msvc2022_64\include\QtCore -external:IE:\Qt\6.10.0\msvc2022_64\include -external:IE:\Qt\6.10.0\msvc2022_64\mkspecs\win32-msvc -external:IE:\Qt\6.10.0\msvc2022_64\include\QtWidgets -external:IE:\Qt\6.10.0\msvc2022_64\include\QtGui -external:IE:\Qt\6.10.0\msvc2022_64\include\QtXml -external:IE:\Qt\6.10.0\msvc2022_64\include\QtSql -external:W0
  OBJECT_DIR = CMakeFiles\XCamView.dir
  OBJECT_FILE_DIR = CMakeFiles\XCamView.dir
  TARGET_COMPILE_PDB = CMakeFiles\XCamView.dir\
  TARGET_PDB = XCamView.pdb


#############################################
# Additional output files.

build CMakeFiles\XCamView.dir\.\cmake_pch.cxx.pch: phony CMakeFiles\XCamView.dir\cmake_pch.cxx.obj

build CMakeFiles\XCamView.dir\XCamView_autogen\mocs_compilation.cpp.obj: CXX_COMPILER__XCamView_unscanned_Release D$:\work\XCamView\XCamView\build\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\XCamView_autogen\mocs_compilation.cpp | CMakeFiles\XCamView.dir\cmake_pch.hxx CMakeFiles\XCamView.dir\cmake_pch.cxx.pch || cmake_object_order_depends_target_XCamView
  DEFINES = -DQT_CORE_LIB -DQT_GUI_LIB -DQT_NO_DEBUG -DQT_SQL_LIB -DQT_WIDGETS_LIB -DQT_XML_LIB -DUNICODE -DWIN32 -DWIN64 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -D_UNICODE -D_WIN64
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /O2 /Ob2 /DNDEBUG -std:c++17 -MD -Zc:__cplusplus -permissive- -utf-8 /YuD:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/CMakeFiles/XCamView.dir/cmake_pch.hxx /FpD:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/CMakeFiles/XCamView.dir/./cmake_pch.cxx.pch /FID:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/CMakeFiles/XCamView.dir/cmake_pch.hxx
  INCLUDES = -ID:\work\XCamView\XCamView\build\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\XCamView_autogen\include -ID:\work\XCamView\XCamView\lib\include -external:IE:\Qt\6.10.0\msvc2022_64\include\QtCore -external:IE:\Qt\6.10.0\msvc2022_64\include -external:IE:\Qt\6.10.0\msvc2022_64\mkspecs\win32-msvc -external:IE:\Qt\6.10.0\msvc2022_64\include\QtWidgets -external:IE:\Qt\6.10.0\msvc2022_64\include\QtGui -external:IE:\Qt\6.10.0\msvc2022_64\include\QtXml -external:IE:\Qt\6.10.0\msvc2022_64\include\QtSql -external:W0
  OBJECT_DIR = CMakeFiles\XCamView.dir
  OBJECT_FILE_DIR = CMakeFiles\XCamView.dir\XCamView_autogen
  TARGET_COMPILE_PDB = CMakeFiles\XCamView.dir\
  TARGET_PDB = XCamView.pdb

build CMakeFiles\XCamView.dir\main.cpp.obj: CXX_COMPILER__XCamView_unscanned_Release D$:\work\XCamView\XCamView\main.cpp | CMakeFiles\XCamView.dir\cmake_pch.hxx CMakeFiles\XCamView.dir\cmake_pch.cxx.pch || cmake_object_order_depends_target_XCamView
  DEFINES = -DQT_CORE_LIB -DQT_GUI_LIB -DQT_NO_DEBUG -DQT_SQL_LIB -DQT_WIDGETS_LIB -DQT_XML_LIB -DUNICODE -DWIN32 -DWIN64 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -D_UNICODE -D_WIN64
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /O2 /Ob2 /DNDEBUG -std:c++17 -MD -Zc:__cplusplus -permissive- -utf-8 /YuD:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/CMakeFiles/XCamView.dir/cmake_pch.hxx /FpD:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/CMakeFiles/XCamView.dir/./cmake_pch.cxx.pch /FID:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/CMakeFiles/XCamView.dir/cmake_pch.hxx
  INCLUDES = -ID:\work\XCamView\XCamView\build\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\XCamView_autogen\include -ID:\work\XCamView\XCamView\lib\include -external:IE:\Qt\6.10.0\msvc2022_64\include\QtCore -external:IE:\Qt\6.10.0\msvc2022_64\include -external:IE:\Qt\6.10.0\msvc2022_64\mkspecs\win32-msvc -external:IE:\Qt\6.10.0\msvc2022_64\include\QtWidgets -external:IE:\Qt\6.10.0\msvc2022_64\include\QtGui -external:IE:\Qt\6.10.0\msvc2022_64\include\QtXml -external:IE:\Qt\6.10.0\msvc2022_64\include\QtSql -external:W0
  OBJECT_DIR = CMakeFiles\XCamView.dir
  OBJECT_FILE_DIR = CMakeFiles\XCamView.dir
  TARGET_COMPILE_PDB = CMakeFiles\XCamView.dir\
  TARGET_PDB = XCamView.pdb

build CMakeFiles\XCamView.dir\mainwindow.cpp.obj: CXX_COMPILER__XCamView_unscanned_Release D$:\work\XCamView\XCamView\mainwindow.cpp | CMakeFiles\XCamView.dir\cmake_pch.hxx CMakeFiles\XCamView.dir\cmake_pch.cxx.pch || cmake_object_order_depends_target_XCamView
  DEFINES = -DQT_CORE_LIB -DQT_GUI_LIB -DQT_NO_DEBUG -DQT_SQL_LIB -DQT_WIDGETS_LIB -DQT_XML_LIB -DUNICODE -DWIN32 -DWIN64 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -D_UNICODE -D_WIN64
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /O2 /Ob2 /DNDEBUG -std:c++17 -MD -Zc:__cplusplus -permissive- -utf-8 /YuD:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/CMakeFiles/XCamView.dir/cmake_pch.hxx /FpD:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/CMakeFiles/XCamView.dir/./cmake_pch.cxx.pch /FID:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/CMakeFiles/XCamView.dir/cmake_pch.hxx
  INCLUDES = -ID:\work\XCamView\XCamView\build\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\XCamView_autogen\include -ID:\work\XCamView\XCamView\lib\include -external:IE:\Qt\6.10.0\msvc2022_64\include\QtCore -external:IE:\Qt\6.10.0\msvc2022_64\include -external:IE:\Qt\6.10.0\msvc2022_64\mkspecs\win32-msvc -external:IE:\Qt\6.10.0\msvc2022_64\include\QtWidgets -external:IE:\Qt\6.10.0\msvc2022_64\include\QtGui -external:IE:\Qt\6.10.0\msvc2022_64\include\QtXml -external:IE:\Qt\6.10.0\msvc2022_64\include\QtSql -external:W0
  OBJECT_DIR = CMakeFiles\XCamView.dir
  OBJECT_FILE_DIR = CMakeFiles\XCamView.dir
  TARGET_COMPILE_PDB = CMakeFiles\XCamView.dir\
  TARGET_PDB = XCamView.pdb

build CMakeFiles\XCamView.dir\module\camera\uvchamcam\uvchamcam.cpp.obj: CXX_COMPILER__XCamView_unscanned_Release D$:\work\XCamView\XCamView\module\camera\uvchamcam\uvchamcam.cpp | CMakeFiles\XCamView.dir\cmake_pch.hxx CMakeFiles\XCamView.dir\cmake_pch.cxx.pch || cmake_object_order_depends_target_XCamView
  DEFINES = -DQT_CORE_LIB -DQT_GUI_LIB -DQT_NO_DEBUG -DQT_SQL_LIB -DQT_WIDGETS_LIB -DQT_XML_LIB -DUNICODE -DWIN32 -DWIN64 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -D_UNICODE -D_WIN64
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /O2 /Ob2 /DNDEBUG -std:c++17 -MD -Zc:__cplusplus -permissive- -utf-8 /YuD:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/CMakeFiles/XCamView.dir/cmake_pch.hxx /FpD:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/CMakeFiles/XCamView.dir/./cmake_pch.cxx.pch /FID:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/CMakeFiles/XCamView.dir/cmake_pch.hxx
  INCLUDES = -ID:\work\XCamView\XCamView\build\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\XCamView_autogen\include -ID:\work\XCamView\XCamView\lib\include -external:IE:\Qt\6.10.0\msvc2022_64\include\QtCore -external:IE:\Qt\6.10.0\msvc2022_64\include -external:IE:\Qt\6.10.0\msvc2022_64\mkspecs\win32-msvc -external:IE:\Qt\6.10.0\msvc2022_64\include\QtWidgets -external:IE:\Qt\6.10.0\msvc2022_64\include\QtGui -external:IE:\Qt\6.10.0\msvc2022_64\include\QtXml -external:IE:\Qt\6.10.0\msvc2022_64\include\QtSql -external:W0
  OBJECT_DIR = CMakeFiles\XCamView.dir
  OBJECT_FILE_DIR = CMakeFiles\XCamView.dir\module\camera\uvchamcam
  TARGET_COMPILE_PDB = CMakeFiles\XCamView.dir\
  TARGET_PDB = XCamView.pdb

build CMakeFiles\XCamView.dir\module\camera\uvchamcam\uvchamloader.cpp.obj: CXX_COMPILER__XCamView_unscanned_Release D$:\work\XCamView\XCamView\module\camera\uvchamcam\uvchamloader.cpp | CMakeFiles\XCamView.dir\cmake_pch.hxx CMakeFiles\XCamView.dir\cmake_pch.cxx.pch || cmake_object_order_depends_target_XCamView
  DEFINES = -DQT_CORE_LIB -DQT_GUI_LIB -DQT_NO_DEBUG -DQT_SQL_LIB -DQT_WIDGETS_LIB -DQT_XML_LIB -DUNICODE -DWIN32 -DWIN64 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -D_UNICODE -D_WIN64
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /O2 /Ob2 /DNDEBUG -std:c++17 -MD -Zc:__cplusplus -permissive- -utf-8 /YuD:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/CMakeFiles/XCamView.dir/cmake_pch.hxx /FpD:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/CMakeFiles/XCamView.dir/./cmake_pch.cxx.pch /FID:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/CMakeFiles/XCamView.dir/cmake_pch.hxx
  INCLUDES = -ID:\work\XCamView\XCamView\build\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\XCamView_autogen\include -ID:\work\XCamView\XCamView\lib\include -external:IE:\Qt\6.10.0\msvc2022_64\include\QtCore -external:IE:\Qt\6.10.0\msvc2022_64\include -external:IE:\Qt\6.10.0\msvc2022_64\mkspecs\win32-msvc -external:IE:\Qt\6.10.0\msvc2022_64\include\QtWidgets -external:IE:\Qt\6.10.0\msvc2022_64\include\QtGui -external:IE:\Qt\6.10.0\msvc2022_64\include\QtXml -external:IE:\Qt\6.10.0\msvc2022_64\include\QtSql -external:W0
  OBJECT_DIR = CMakeFiles\XCamView.dir
  OBJECT_FILE_DIR = CMakeFiles\XCamView.dir\module\camera\uvchamcam
  TARGET_COMPILE_PDB = CMakeFiles\XCamView.dir\
  TARGET_PDB = XCamView.pdb

build CMakeFiles\XCamView.dir\module\camera\camera.cpp.obj: CXX_COMPILER__XCamView_unscanned_Release D$:\work\XCamView\XCamView\module\camera\camera.cpp | CMakeFiles\XCamView.dir\cmake_pch.hxx CMakeFiles\XCamView.dir\cmake_pch.cxx.pch || cmake_object_order_depends_target_XCamView
  DEFINES = -DQT_CORE_LIB -DQT_GUI_LIB -DQT_NO_DEBUG -DQT_SQL_LIB -DQT_WIDGETS_LIB -DQT_XML_LIB -DUNICODE -DWIN32 -DWIN64 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -D_UNICODE -D_WIN64
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /O2 /Ob2 /DNDEBUG -std:c++17 -MD -Zc:__cplusplus -permissive- -utf-8 /YuD:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/CMakeFiles/XCamView.dir/cmake_pch.hxx /FpD:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/CMakeFiles/XCamView.dir/./cmake_pch.cxx.pch /FID:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/CMakeFiles/XCamView.dir/cmake_pch.hxx
  INCLUDES = -ID:\work\XCamView\XCamView\build\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\XCamView_autogen\include -ID:\work\XCamView\XCamView\lib\include -external:IE:\Qt\6.10.0\msvc2022_64\include\QtCore -external:IE:\Qt\6.10.0\msvc2022_64\include -external:IE:\Qt\6.10.0\msvc2022_64\mkspecs\win32-msvc -external:IE:\Qt\6.10.0\msvc2022_64\include\QtWidgets -external:IE:\Qt\6.10.0\msvc2022_64\include\QtGui -external:IE:\Qt\6.10.0\msvc2022_64\include\QtXml -external:IE:\Qt\6.10.0\msvc2022_64\include\QtSql -external:W0
  OBJECT_DIR = CMakeFiles\XCamView.dir
  OBJECT_FILE_DIR = CMakeFiles\XCamView.dir\module\camera
  TARGET_COMPILE_PDB = CMakeFiles\XCamView.dir\
  TARGET_PDB = XCamView.pdb

build CMakeFiles\XCamView.dir\module\camera\cameramanager.cpp.obj: CXX_COMPILER__XCamView_unscanned_Release D$:\work\XCamView\XCamView\module\camera\cameramanager.cpp | CMakeFiles\XCamView.dir\cmake_pch.hxx CMakeFiles\XCamView.dir\cmake_pch.cxx.pch || cmake_object_order_depends_target_XCamView
  DEFINES = -DQT_CORE_LIB -DQT_GUI_LIB -DQT_NO_DEBUG -DQT_SQL_LIB -DQT_WIDGETS_LIB -DQT_XML_LIB -DUNICODE -DWIN32 -DWIN64 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -D_UNICODE -D_WIN64
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /O2 /Ob2 /DNDEBUG -std:c++17 -MD -Zc:__cplusplus -permissive- -utf-8 /YuD:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/CMakeFiles/XCamView.dir/cmake_pch.hxx /FpD:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/CMakeFiles/XCamView.dir/./cmake_pch.cxx.pch /FID:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/CMakeFiles/XCamView.dir/cmake_pch.hxx
  INCLUDES = -ID:\work\XCamView\XCamView\build\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\XCamView_autogen\include -ID:\work\XCamView\XCamView\lib\include -external:IE:\Qt\6.10.0\msvc2022_64\include\QtCore -external:IE:\Qt\6.10.0\msvc2022_64\include -external:IE:\Qt\6.10.0\msvc2022_64\mkspecs\win32-msvc -external:IE:\Qt\6.10.0\msvc2022_64\include\QtWidgets -external:IE:\Qt\6.10.0\msvc2022_64\include\QtGui -external:IE:\Qt\6.10.0\msvc2022_64\include\QtXml -external:IE:\Qt\6.10.0\msvc2022_64\include\QtSql -external:W0
  OBJECT_DIR = CMakeFiles\XCamView.dir
  OBJECT_FILE_DIR = CMakeFiles\XCamView.dir\module\camera
  TARGET_COMPILE_PDB = CMakeFiles\XCamView.dir\
  TARGET_PDB = XCamView.pdb

build CMakeFiles\XCamView.dir\module\auxrect\auxrect.cpp.obj: CXX_COMPILER__XCamView_unscanned_Release D$:\work\XCamView\XCamView\module\auxrect\auxrect.cpp | CMakeFiles\XCamView.dir\cmake_pch.hxx CMakeFiles\XCamView.dir\cmake_pch.cxx.pch || cmake_object_order_depends_target_XCamView
  DEFINES = -DQT_CORE_LIB -DQT_GUI_LIB -DQT_NO_DEBUG -DQT_SQL_LIB -DQT_WIDGETS_LIB -DQT_XML_LIB -DUNICODE -DWIN32 -DWIN64 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -D_UNICODE -D_WIN64
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /O2 /Ob2 /DNDEBUG -std:c++17 -MD -Zc:__cplusplus -permissive- -utf-8 /YuD:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/CMakeFiles/XCamView.dir/cmake_pch.hxx /FpD:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/CMakeFiles/XCamView.dir/./cmake_pch.cxx.pch /FID:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/CMakeFiles/XCamView.dir/cmake_pch.hxx
  INCLUDES = -ID:\work\XCamView\XCamView\build\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\XCamView_autogen\include -ID:\work\XCamView\XCamView\lib\include -external:IE:\Qt\6.10.0\msvc2022_64\include\QtCore -external:IE:\Qt\6.10.0\msvc2022_64\include -external:IE:\Qt\6.10.0\msvc2022_64\mkspecs\win32-msvc -external:IE:\Qt\6.10.0\msvc2022_64\include\QtWidgets -external:IE:\Qt\6.10.0\msvc2022_64\include\QtGui -external:IE:\Qt\6.10.0\msvc2022_64\include\QtXml -external:IE:\Qt\6.10.0\msvc2022_64\include\QtSql -external:W0
  OBJECT_DIR = CMakeFiles\XCamView.dir
  OBJECT_FILE_DIR = CMakeFiles\XCamView.dir\module\auxrect
  TARGET_COMPILE_PDB = CMakeFiles\XCamView.dir\
  TARGET_PDB = XCamView.pdb

build CMakeFiles\XCamView.dir\module\i18n\i18n.cpp.obj: CXX_COMPILER__XCamView_unscanned_Release D$:\work\XCamView\XCamView\module\i18n\i18n.cpp | CMakeFiles\XCamView.dir\cmake_pch.hxx CMakeFiles\XCamView.dir\cmake_pch.cxx.pch || cmake_object_order_depends_target_XCamView
  DEFINES = -DQT_CORE_LIB -DQT_GUI_LIB -DQT_NO_DEBUG -DQT_SQL_LIB -DQT_WIDGETS_LIB -DQT_XML_LIB -DUNICODE -DWIN32 -DWIN64 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -D_UNICODE -D_WIN64
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /O2 /Ob2 /DNDEBUG -std:c++17 -MD -Zc:__cplusplus -permissive- -utf-8 /YuD:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/CMakeFiles/XCamView.dir/cmake_pch.hxx /FpD:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/CMakeFiles/XCamView.dir/./cmake_pch.cxx.pch /FID:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/CMakeFiles/XCamView.dir/cmake_pch.hxx
  INCLUDES = -ID:\work\XCamView\XCamView\build\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\XCamView_autogen\include -ID:\work\XCamView\XCamView\lib\include -external:IE:\Qt\6.10.0\msvc2022_64\include\QtCore -external:IE:\Qt\6.10.0\msvc2022_64\include -external:IE:\Qt\6.10.0\msvc2022_64\mkspecs\win32-msvc -external:IE:\Qt\6.10.0\msvc2022_64\include\QtWidgets -external:IE:\Qt\6.10.0\msvc2022_64\include\QtGui -external:IE:\Qt\6.10.0\msvc2022_64\include\QtXml -external:IE:\Qt\6.10.0\msvc2022_64\include\QtSql -external:W0
  OBJECT_DIR = CMakeFiles\XCamView.dir
  OBJECT_FILE_DIR = CMakeFiles\XCamView.dir\module\i18n
  TARGET_COMPILE_PDB = CMakeFiles\XCamView.dir\
  TARGET_PDB = XCamView.pdb

build CMakeFiles\XCamView.dir\module\TpSaveLoader\tpsaveloader.cpp.obj: CXX_COMPILER__XCamView_unscanned_Release D$:\work\XCamView\XCamView\module\TpSaveLoader\tpsaveloader.cpp | CMakeFiles\XCamView.dir\cmake_pch.hxx CMakeFiles\XCamView.dir\cmake_pch.cxx.pch || cmake_object_order_depends_target_XCamView
  DEFINES = -DQT_CORE_LIB -DQT_GUI_LIB -DQT_NO_DEBUG -DQT_SQL_LIB -DQT_WIDGETS_LIB -DQT_XML_LIB -DUNICODE -DWIN32 -DWIN64 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -D_UNICODE -D_WIN64
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /O2 /Ob2 /DNDEBUG -std:c++17 -MD -Zc:__cplusplus -permissive- -utf-8 /YuD:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/CMakeFiles/XCamView.dir/cmake_pch.hxx /FpD:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/CMakeFiles/XCamView.dir/./cmake_pch.cxx.pch /FID:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/CMakeFiles/XCamView.dir/cmake_pch.hxx
  INCLUDES = -ID:\work\XCamView\XCamView\build\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\XCamView_autogen\include -ID:\work\XCamView\XCamView\lib\include -external:IE:\Qt\6.10.0\msvc2022_64\include\QtCore -external:IE:\Qt\6.10.0\msvc2022_64\include -external:IE:\Qt\6.10.0\msvc2022_64\mkspecs\win32-msvc -external:IE:\Qt\6.10.0\msvc2022_64\include\QtWidgets -external:IE:\Qt\6.10.0\msvc2022_64\include\QtGui -external:IE:\Qt\6.10.0\msvc2022_64\include\QtXml -external:IE:\Qt\6.10.0\msvc2022_64\include\QtSql -external:W0
  OBJECT_DIR = CMakeFiles\XCamView.dir
  OBJECT_FILE_DIR = CMakeFiles\XCamView.dir\module\TpSaveLoader
  TARGET_COMPILE_PDB = CMakeFiles\XCamView.dir\
  TARGET_PDB = XCamView.pdb

build CMakeFiles\XCamView.dir\module\TpSignalConnect\TpSignalConnect.cpp.obj: CXX_COMPILER__XCamView_unscanned_Release D$:\work\XCamView\XCamView\module\TpSignalConnect\TpSignalConnect.cpp | CMakeFiles\XCamView.dir\cmake_pch.hxx CMakeFiles\XCamView.dir\cmake_pch.cxx.pch || cmake_object_order_depends_target_XCamView
  DEFINES = -DQT_CORE_LIB -DQT_GUI_LIB -DQT_NO_DEBUG -DQT_SQL_LIB -DQT_WIDGETS_LIB -DQT_XML_LIB -DUNICODE -DWIN32 -DWIN64 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -D_UNICODE -D_WIN64
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /O2 /Ob2 /DNDEBUG -std:c++17 -MD -Zc:__cplusplus -permissive- -utf-8 /YuD:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/CMakeFiles/XCamView.dir/cmake_pch.hxx /FpD:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/CMakeFiles/XCamView.dir/./cmake_pch.cxx.pch /FID:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/CMakeFiles/XCamView.dir/cmake_pch.hxx
  INCLUDES = -ID:\work\XCamView\XCamView\build\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\XCamView_autogen\include -ID:\work\XCamView\XCamView\lib\include -external:IE:\Qt\6.10.0\msvc2022_64\include\QtCore -external:IE:\Qt\6.10.0\msvc2022_64\include -external:IE:\Qt\6.10.0\msvc2022_64\mkspecs\win32-msvc -external:IE:\Qt\6.10.0\msvc2022_64\include\QtWidgets -external:IE:\Qt\6.10.0\msvc2022_64\include\QtGui -external:IE:\Qt\6.10.0\msvc2022_64\include\QtXml -external:IE:\Qt\6.10.0\msvc2022_64\include\QtSql -external:W0
  OBJECT_DIR = CMakeFiles\XCamView.dir
  OBJECT_FILE_DIR = CMakeFiles\XCamView.dir\module\TpSignalConnect
  TARGET_COMPILE_PDB = CMakeFiles\XCamView.dir\
  TARGET_PDB = XCamView.pdb

build CMakeFiles\XCamView.dir\module\tutils\tutils.cpp.obj: CXX_COMPILER__XCamView_unscanned_Release D$:\work\XCamView\XCamView\module\tutils\tutils.cpp | CMakeFiles\XCamView.dir\cmake_pch.hxx CMakeFiles\XCamView.dir\cmake_pch.cxx.pch || cmake_object_order_depends_target_XCamView
  DEFINES = -DQT_CORE_LIB -DQT_GUI_LIB -DQT_NO_DEBUG -DQT_SQL_LIB -DQT_WIDGETS_LIB -DQT_XML_LIB -DUNICODE -DWIN32 -DWIN64 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -D_UNICODE -D_WIN64
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /O2 /Ob2 /DNDEBUG -std:c++17 -MD -Zc:__cplusplus -permissive- -utf-8 /YuD:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/CMakeFiles/XCamView.dir/cmake_pch.hxx /FpD:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/CMakeFiles/XCamView.dir/./cmake_pch.cxx.pch /FID:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/CMakeFiles/XCamView.dir/cmake_pch.hxx
  INCLUDES = -ID:\work\XCamView\XCamView\build\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\XCamView_autogen\include -ID:\work\XCamView\XCamView\lib\include -external:IE:\Qt\6.10.0\msvc2022_64\include\QtCore -external:IE:\Qt\6.10.0\msvc2022_64\include -external:IE:\Qt\6.10.0\msvc2022_64\mkspecs\win32-msvc -external:IE:\Qt\6.10.0\msvc2022_64\include\QtWidgets -external:IE:\Qt\6.10.0\msvc2022_64\include\QtGui -external:IE:\Qt\6.10.0\msvc2022_64\include\QtXml -external:IE:\Qt\6.10.0\msvc2022_64\include\QtSql -external:W0
  OBJECT_DIR = CMakeFiles\XCamView.dir
  OBJECT_FILE_DIR = CMakeFiles\XCamView.dir\module\tutils
  TARGET_COMPILE_PDB = CMakeFiles\XCamView.dir\
  TARGET_PDB = XCamView.pdb

build CMakeFiles\XCamView.dir\panel\camerapanel\cameralistwidget.cpp.obj: CXX_COMPILER__XCamView_unscanned_Release D$:\work\XCamView\XCamView\panel\camerapanel\cameralistwidget.cpp | CMakeFiles\XCamView.dir\cmake_pch.hxx CMakeFiles\XCamView.dir\cmake_pch.cxx.pch || cmake_object_order_depends_target_XCamView
  DEFINES = -DQT_CORE_LIB -DQT_GUI_LIB -DQT_NO_DEBUG -DQT_SQL_LIB -DQT_WIDGETS_LIB -DQT_XML_LIB -DUNICODE -DWIN32 -DWIN64 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -D_UNICODE -D_WIN64
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /O2 /Ob2 /DNDEBUG -std:c++17 -MD -Zc:__cplusplus -permissive- -utf-8 /YuD:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/CMakeFiles/XCamView.dir/cmake_pch.hxx /FpD:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/CMakeFiles/XCamView.dir/./cmake_pch.cxx.pch /FID:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/CMakeFiles/XCamView.dir/cmake_pch.hxx
  INCLUDES = -ID:\work\XCamView\XCamView\build\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\XCamView_autogen\include -ID:\work\XCamView\XCamView\lib\include -external:IE:\Qt\6.10.0\msvc2022_64\include\QtCore -external:IE:\Qt\6.10.0\msvc2022_64\include -external:IE:\Qt\6.10.0\msvc2022_64\mkspecs\win32-msvc -external:IE:\Qt\6.10.0\msvc2022_64\include\QtWidgets -external:IE:\Qt\6.10.0\msvc2022_64\include\QtGui -external:IE:\Qt\6.10.0\msvc2022_64\include\QtXml -external:IE:\Qt\6.10.0\msvc2022_64\include\QtSql -external:W0
  OBJECT_DIR = CMakeFiles\XCamView.dir
  OBJECT_FILE_DIR = CMakeFiles\XCamView.dir\panel\camerapanel
  TARGET_COMPILE_PDB = CMakeFiles\XCamView.dir\
  TARGET_PDB = XCamView.pdb

build CMakeFiles\XCamView.dir\panel\camerapanel\camerapanel.cpp.obj: CXX_COMPILER__XCamView_unscanned_Release D$:\work\XCamView\XCamView\panel\camerapanel\camerapanel.cpp | CMakeFiles\XCamView.dir\cmake_pch.hxx CMakeFiles\XCamView.dir\cmake_pch.cxx.pch || cmake_object_order_depends_target_XCamView
  DEFINES = -DQT_CORE_LIB -DQT_GUI_LIB -DQT_NO_DEBUG -DQT_SQL_LIB -DQT_WIDGETS_LIB -DQT_XML_LIB -DUNICODE -DWIN32 -DWIN64 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -D_UNICODE -D_WIN64
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /O2 /Ob2 /DNDEBUG -std:c++17 -MD -Zc:__cplusplus -permissive- -utf-8 /YuD:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/CMakeFiles/XCamView.dir/cmake_pch.hxx /FpD:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/CMakeFiles/XCamView.dir/./cmake_pch.cxx.pch /FID:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/CMakeFiles/XCamView.dir/cmake_pch.hxx
  INCLUDES = -ID:\work\XCamView\XCamView\build\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\XCamView_autogen\include -ID:\work\XCamView\XCamView\lib\include -external:IE:\Qt\6.10.0\msvc2022_64\include\QtCore -external:IE:\Qt\6.10.0\msvc2022_64\include -external:IE:\Qt\6.10.0\msvc2022_64\mkspecs\win32-msvc -external:IE:\Qt\6.10.0\msvc2022_64\include\QtWidgets -external:IE:\Qt\6.10.0\msvc2022_64\include\QtGui -external:IE:\Qt\6.10.0\msvc2022_64\include\QtXml -external:IE:\Qt\6.10.0\msvc2022_64\include\QtSql -external:W0
  OBJECT_DIR = CMakeFiles\XCamView.dir
  OBJECT_FILE_DIR = CMakeFiles\XCamView.dir\panel\camerapanel
  TARGET_COMPILE_PDB = CMakeFiles\XCamView.dir\
  TARGET_PDB = XCamView.pdb

build CMakeFiles\XCamView.dir\panel\camerapanel\coloradjustwidget.cpp.obj: CXX_COMPILER__XCamView_unscanned_Release D$:\work\XCamView\XCamView\panel\camerapanel\coloradjustwidget.cpp | CMakeFiles\XCamView.dir\cmake_pch.hxx CMakeFiles\XCamView.dir\cmake_pch.cxx.pch || cmake_object_order_depends_target_XCamView
  DEFINES = -DQT_CORE_LIB -DQT_GUI_LIB -DQT_NO_DEBUG -DQT_SQL_LIB -DQT_WIDGETS_LIB -DQT_XML_LIB -DUNICODE -DWIN32 -DWIN64 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -D_UNICODE -D_WIN64
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /O2 /Ob2 /DNDEBUG -std:c++17 -MD -Zc:__cplusplus -permissive- -utf-8 /YuD:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/CMakeFiles/XCamView.dir/cmake_pch.hxx /FpD:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/CMakeFiles/XCamView.dir/./cmake_pch.cxx.pch /FID:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/CMakeFiles/XCamView.dir/cmake_pch.hxx
  INCLUDES = -ID:\work\XCamView\XCamView\build\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\XCamView_autogen\include -ID:\work\XCamView\XCamView\lib\include -external:IE:\Qt\6.10.0\msvc2022_64\include\QtCore -external:IE:\Qt\6.10.0\msvc2022_64\include -external:IE:\Qt\6.10.0\msvc2022_64\mkspecs\win32-msvc -external:IE:\Qt\6.10.0\msvc2022_64\include\QtWidgets -external:IE:\Qt\6.10.0\msvc2022_64\include\QtGui -external:IE:\Qt\6.10.0\msvc2022_64\include\QtXml -external:IE:\Qt\6.10.0\msvc2022_64\include\QtSql -external:W0
  OBJECT_DIR = CMakeFiles\XCamView.dir
  OBJECT_FILE_DIR = CMakeFiles\XCamView.dir\panel\camerapanel
  TARGET_COMPILE_PDB = CMakeFiles\XCamView.dir\
  TARGET_PDB = XCamView.pdb

build CMakeFiles\XCamView.dir\panel\camerapanel\exposuregainwidget.cpp.obj: CXX_COMPILER__XCamView_unscanned_Release D$:\work\XCamView\XCamView\panel\camerapanel\exposuregainwidget.cpp | CMakeFiles\XCamView.dir\cmake_pch.hxx CMakeFiles\XCamView.dir\cmake_pch.cxx.pch || cmake_object_order_depends_target_XCamView
  DEFINES = -DQT_CORE_LIB -DQT_GUI_LIB -DQT_NO_DEBUG -DQT_SQL_LIB -DQT_WIDGETS_LIB -DQT_XML_LIB -DUNICODE -DWIN32 -DWIN64 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -D_UNICODE -D_WIN64
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /O2 /Ob2 /DNDEBUG -std:c++17 -MD -Zc:__cplusplus -permissive- -utf-8 /YuD:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/CMakeFiles/XCamView.dir/cmake_pch.hxx /FpD:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/CMakeFiles/XCamView.dir/./cmake_pch.cxx.pch /FID:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/CMakeFiles/XCamView.dir/cmake_pch.hxx
  INCLUDES = -ID:\work\XCamView\XCamView\build\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\XCamView_autogen\include -ID:\work\XCamView\XCamView\lib\include -external:IE:\Qt\6.10.0\msvc2022_64\include\QtCore -external:IE:\Qt\6.10.0\msvc2022_64\include -external:IE:\Qt\6.10.0\msvc2022_64\mkspecs\win32-msvc -external:IE:\Qt\6.10.0\msvc2022_64\include\QtWidgets -external:IE:\Qt\6.10.0\msvc2022_64\include\QtGui -external:IE:\Qt\6.10.0\msvc2022_64\include\QtXml -external:IE:\Qt\6.10.0\msvc2022_64\include\QtSql -external:W0
  OBJECT_DIR = CMakeFiles\XCamView.dir
  OBJECT_FILE_DIR = CMakeFiles\XCamView.dir\panel\camerapanel
  TARGET_COMPILE_PDB = CMakeFiles\XCamView.dir\
  TARGET_PDB = XCamView.pdb

build CMakeFiles\XCamView.dir\panel\camerapanel\powerfrequencywidget.cpp.obj: CXX_COMPILER__XCamView_unscanned_Release D$:\work\XCamView\XCamView\panel\camerapanel\powerfrequencywidget.cpp | CMakeFiles\XCamView.dir\cmake_pch.hxx CMakeFiles\XCamView.dir\cmake_pch.cxx.pch || cmake_object_order_depends_target_XCamView
  DEFINES = -DQT_CORE_LIB -DQT_GUI_LIB -DQT_NO_DEBUG -DQT_SQL_LIB -DQT_WIDGETS_LIB -DQT_XML_LIB -DUNICODE -DWIN32 -DWIN64 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -D_UNICODE -D_WIN64
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /O2 /Ob2 /DNDEBUG -std:c++17 -MD -Zc:__cplusplus -permissive- -utf-8 /YuD:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/CMakeFiles/XCamView.dir/cmake_pch.hxx /FpD:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/CMakeFiles/XCamView.dir/./cmake_pch.cxx.pch /FID:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/CMakeFiles/XCamView.dir/cmake_pch.hxx
  INCLUDES = -ID:\work\XCamView\XCamView\build\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\XCamView_autogen\include -ID:\work\XCamView\XCamView\lib\include -external:IE:\Qt\6.10.0\msvc2022_64\include\QtCore -external:IE:\Qt\6.10.0\msvc2022_64\include -external:IE:\Qt\6.10.0\msvc2022_64\mkspecs\win32-msvc -external:IE:\Qt\6.10.0\msvc2022_64\include\QtWidgets -external:IE:\Qt\6.10.0\msvc2022_64\include\QtGui -external:IE:\Qt\6.10.0\msvc2022_64\include\QtXml -external:IE:\Qt\6.10.0\msvc2022_64\include\QtSql -external:W0
  OBJECT_DIR = CMakeFiles\XCamView.dir
  OBJECT_FILE_DIR = CMakeFiles\XCamView.dir\panel\camerapanel
  TARGET_COMPILE_PDB = CMakeFiles\XCamView.dir\
  TARGET_PDB = XCamView.pdb

build CMakeFiles\XCamView.dir\panel\camerapanel\resolutionwidget.cpp.obj: CXX_COMPILER__XCamView_unscanned_Release D$:\work\XCamView\XCamView\panel\camerapanel\resolutionwidget.cpp | CMakeFiles\XCamView.dir\cmake_pch.hxx CMakeFiles\XCamView.dir\cmake_pch.cxx.pch || cmake_object_order_depends_target_XCamView
  DEFINES = -DQT_CORE_LIB -DQT_GUI_LIB -DQT_NO_DEBUG -DQT_SQL_LIB -DQT_WIDGETS_LIB -DQT_XML_LIB -DUNICODE -DWIN32 -DWIN64 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -D_UNICODE -D_WIN64
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /O2 /Ob2 /DNDEBUG -std:c++17 -MD -Zc:__cplusplus -permissive- -utf-8 /YuD:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/CMakeFiles/XCamView.dir/cmake_pch.hxx /FpD:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/CMakeFiles/XCamView.dir/./cmake_pch.cxx.pch /FID:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/CMakeFiles/XCamView.dir/cmake_pch.hxx
  INCLUDES = -ID:\work\XCamView\XCamView\build\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\XCamView_autogen\include -ID:\work\XCamView\XCamView\lib\include -external:IE:\Qt\6.10.0\msvc2022_64\include\QtCore -external:IE:\Qt\6.10.0\msvc2022_64\include -external:IE:\Qt\6.10.0\msvc2022_64\mkspecs\win32-msvc -external:IE:\Qt\6.10.0\msvc2022_64\include\QtWidgets -external:IE:\Qt\6.10.0\msvc2022_64\include\QtGui -external:IE:\Qt\6.10.0\msvc2022_64\include\QtXml -external:IE:\Qt\6.10.0\msvc2022_64\include\QtSql -external:W0
  OBJECT_DIR = CMakeFiles\XCamView.dir
  OBJECT_FILE_DIR = CMakeFiles\XCamView.dir\panel\camerapanel
  TARGET_COMPILE_PDB = CMakeFiles\XCamView.dir\
  TARGET_PDB = XCamView.pdb

build CMakeFiles\XCamView.dir\panel\camerapanel\whitebalancewidget.cpp.obj: CXX_COMPILER__XCamView_unscanned_Release D$:\work\XCamView\XCamView\panel\camerapanel\whitebalancewidget.cpp | CMakeFiles\XCamView.dir\cmake_pch.hxx CMakeFiles\XCamView.dir\cmake_pch.cxx.pch || cmake_object_order_depends_target_XCamView
  DEFINES = -DQT_CORE_LIB -DQT_GUI_LIB -DQT_NO_DEBUG -DQT_SQL_LIB -DQT_WIDGETS_LIB -DQT_XML_LIB -DUNICODE -DWIN32 -DWIN64 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -D_UNICODE -D_WIN64
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /O2 /Ob2 /DNDEBUG -std:c++17 -MD -Zc:__cplusplus -permissive- -utf-8 /YuD:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/CMakeFiles/XCamView.dir/cmake_pch.hxx /FpD:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/CMakeFiles/XCamView.dir/./cmake_pch.cxx.pch /FID:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/CMakeFiles/XCamView.dir/cmake_pch.hxx
  INCLUDES = -ID:\work\XCamView\XCamView\build\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\XCamView_autogen\include -ID:\work\XCamView\XCamView\lib\include -external:IE:\Qt\6.10.0\msvc2022_64\include\QtCore -external:IE:\Qt\6.10.0\msvc2022_64\include -external:IE:\Qt\6.10.0\msvc2022_64\mkspecs\win32-msvc -external:IE:\Qt\6.10.0\msvc2022_64\include\QtWidgets -external:IE:\Qt\6.10.0\msvc2022_64\include\QtGui -external:IE:\Qt\6.10.0\msvc2022_64\include\QtXml -external:IE:\Qt\6.10.0\msvc2022_64\include\QtSql -external:W0
  OBJECT_DIR = CMakeFiles\XCamView.dir
  OBJECT_FILE_DIR = CMakeFiles\XCamView.dir\panel\camerapanel
  TARGET_COMPILE_PDB = CMakeFiles\XCamView.dir\
  TARGET_PDB = XCamView.pdb

build CMakeFiles\XCamView.dir\panel\centralpanel\displayview\displayview.cpp.obj: CXX_COMPILER__XCamView_unscanned_Release D$:\work\XCamView\XCamView\panel\centralpanel\displayview\displayview.cpp | CMakeFiles\XCamView.dir\cmake_pch.hxx CMakeFiles\XCamView.dir\cmake_pch.cxx.pch || cmake_object_order_depends_target_XCamView
  DEFINES = -DQT_CORE_LIB -DQT_GUI_LIB -DQT_NO_DEBUG -DQT_SQL_LIB -DQT_WIDGETS_LIB -DQT_XML_LIB -DUNICODE -DWIN32 -DWIN64 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -D_UNICODE -D_WIN64
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /O2 /Ob2 /DNDEBUG -std:c++17 -MD -Zc:__cplusplus -permissive- -utf-8 /YuD:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/CMakeFiles/XCamView.dir/cmake_pch.hxx /FpD:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/CMakeFiles/XCamView.dir/./cmake_pch.cxx.pch /FID:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/CMakeFiles/XCamView.dir/cmake_pch.hxx
  INCLUDES = -ID:\work\XCamView\XCamView\build\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\XCamView_autogen\include -ID:\work\XCamView\XCamView\lib\include -external:IE:\Qt\6.10.0\msvc2022_64\include\QtCore -external:IE:\Qt\6.10.0\msvc2022_64\include -external:IE:\Qt\6.10.0\msvc2022_64\mkspecs\win32-msvc -external:IE:\Qt\6.10.0\msvc2022_64\include\QtWidgets -external:IE:\Qt\6.10.0\msvc2022_64\include\QtGui -external:IE:\Qt\6.10.0\msvc2022_64\include\QtXml -external:IE:\Qt\6.10.0\msvc2022_64\include\QtSql -external:W0
  OBJECT_DIR = CMakeFiles\XCamView.dir
  OBJECT_FILE_DIR = CMakeFiles\XCamView.dir\panel\centralpanel\displayview
  TARGET_COMPILE_PDB = CMakeFiles\XCamView.dir\
  TARGET_PDB = XCamView.pdb

build CMakeFiles\XCamView.dir\panel\centralpanel\displayview\imageview.cpp.obj: CXX_COMPILER__XCamView_unscanned_Release D$:\work\XCamView\XCamView\panel\centralpanel\displayview\imageview.cpp | CMakeFiles\XCamView.dir\cmake_pch.hxx CMakeFiles\XCamView.dir\cmake_pch.cxx.pch || cmake_object_order_depends_target_XCamView
  DEFINES = -DQT_CORE_LIB -DQT_GUI_LIB -DQT_NO_DEBUG -DQT_SQL_LIB -DQT_WIDGETS_LIB -DQT_XML_LIB -DUNICODE -DWIN32 -DWIN64 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -D_UNICODE -D_WIN64
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /O2 /Ob2 /DNDEBUG -std:c++17 -MD -Zc:__cplusplus -permissive- -utf-8 /YuD:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/CMakeFiles/XCamView.dir/cmake_pch.hxx /FpD:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/CMakeFiles/XCamView.dir/./cmake_pch.cxx.pch /FID:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/CMakeFiles/XCamView.dir/cmake_pch.hxx
  INCLUDES = -ID:\work\XCamView\XCamView\build\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\XCamView_autogen\include -ID:\work\XCamView\XCamView\lib\include -external:IE:\Qt\6.10.0\msvc2022_64\include\QtCore -external:IE:\Qt\6.10.0\msvc2022_64\include -external:IE:\Qt\6.10.0\msvc2022_64\mkspecs\win32-msvc -external:IE:\Qt\6.10.0\msvc2022_64\include\QtWidgets -external:IE:\Qt\6.10.0\msvc2022_64\include\QtGui -external:IE:\Qt\6.10.0\msvc2022_64\include\QtXml -external:IE:\Qt\6.10.0\msvc2022_64\include\QtSql -external:W0
  OBJECT_DIR = CMakeFiles\XCamView.dir
  OBJECT_FILE_DIR = CMakeFiles\XCamView.dir\panel\centralpanel\displayview
  TARGET_COMPILE_PDB = CMakeFiles\XCamView.dir\
  TARGET_PDB = XCamView.pdb

build CMakeFiles\XCamView.dir\panel\centralpanel\displayview\previewview.cpp.obj: CXX_COMPILER__XCamView_unscanned_Release D$:\work\XCamView\XCamView\panel\centralpanel\displayview\previewview.cpp | CMakeFiles\XCamView.dir\cmake_pch.hxx CMakeFiles\XCamView.dir\cmake_pch.cxx.pch || cmake_object_order_depends_target_XCamView
  DEFINES = -DQT_CORE_LIB -DQT_GUI_LIB -DQT_NO_DEBUG -DQT_SQL_LIB -DQT_WIDGETS_LIB -DQT_XML_LIB -DUNICODE -DWIN32 -DWIN64 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -D_UNICODE -D_WIN64
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /O2 /Ob2 /DNDEBUG -std:c++17 -MD -Zc:__cplusplus -permissive- -utf-8 /YuD:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/CMakeFiles/XCamView.dir/cmake_pch.hxx /FpD:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/CMakeFiles/XCamView.dir/./cmake_pch.cxx.pch /FID:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/CMakeFiles/XCamView.dir/cmake_pch.hxx
  INCLUDES = -ID:\work\XCamView\XCamView\build\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\XCamView_autogen\include -ID:\work\XCamView\XCamView\lib\include -external:IE:\Qt\6.10.0\msvc2022_64\include\QtCore -external:IE:\Qt\6.10.0\msvc2022_64\include -external:IE:\Qt\6.10.0\msvc2022_64\mkspecs\win32-msvc -external:IE:\Qt\6.10.0\msvc2022_64\include\QtWidgets -external:IE:\Qt\6.10.0\msvc2022_64\include\QtGui -external:IE:\Qt\6.10.0\msvc2022_64\include\QtXml -external:IE:\Qt\6.10.0\msvc2022_64\include\QtSql -external:W0
  OBJECT_DIR = CMakeFiles\XCamView.dir
  OBJECT_FILE_DIR = CMakeFiles\XCamView.dir\panel\centralpanel\displayview
  TARGET_COMPILE_PDB = CMakeFiles\XCamView.dir\
  TARGET_PDB = XCamView.pdb

build CMakeFiles\XCamView.dir\panel\centralpanel\monitorview\monitorview.cpp.obj: CXX_COMPILER__XCamView_unscanned_Release D$:\work\XCamView\XCamView\panel\centralpanel\monitorview\monitorview.cpp | CMakeFiles\XCamView.dir\cmake_pch.hxx CMakeFiles\XCamView.dir\cmake_pch.cxx.pch || cmake_object_order_depends_target_XCamView
  DEFINES = -DQT_CORE_LIB -DQT_GUI_LIB -DQT_NO_DEBUG -DQT_SQL_LIB -DQT_WIDGETS_LIB -DQT_XML_LIB -DUNICODE -DWIN32 -DWIN64 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -D_UNICODE -D_WIN64
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /O2 /Ob2 /DNDEBUG -std:c++17 -MD -Zc:__cplusplus -permissive- -utf-8 /YuD:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/CMakeFiles/XCamView.dir/cmake_pch.hxx /FpD:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/CMakeFiles/XCamView.dir/./cmake_pch.cxx.pch /FID:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/CMakeFiles/XCamView.dir/cmake_pch.hxx
  INCLUDES = -ID:\work\XCamView\XCamView\build\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\XCamView_autogen\include -ID:\work\XCamView\XCamView\lib\include -external:IE:\Qt\6.10.0\msvc2022_64\include\QtCore -external:IE:\Qt\6.10.0\msvc2022_64\include -external:IE:\Qt\6.10.0\msvc2022_64\mkspecs\win32-msvc -external:IE:\Qt\6.10.0\msvc2022_64\include\QtWidgets -external:IE:\Qt\6.10.0\msvc2022_64\include\QtGui -external:IE:\Qt\6.10.0\msvc2022_64\include\QtXml -external:IE:\Qt\6.10.0\msvc2022_64\include\QtSql -external:W0
  OBJECT_DIR = CMakeFiles\XCamView.dir
  OBJECT_FILE_DIR = CMakeFiles\XCamView.dir\panel\centralpanel\monitorview
  TARGET_COMPILE_PDB = CMakeFiles\XCamView.dir\
  TARGET_PDB = XCamView.pdb

build CMakeFiles\XCamView.dir\panel\centralpanel\tptabwidget.cpp.obj: CXX_COMPILER__XCamView_unscanned_Release D$:\work\XCamView\XCamView\panel\centralpanel\tptabwidget.cpp | CMakeFiles\XCamView.dir\cmake_pch.hxx CMakeFiles\XCamView.dir\cmake_pch.cxx.pch || cmake_object_order_depends_target_XCamView
  DEFINES = -DQT_CORE_LIB -DQT_GUI_LIB -DQT_NO_DEBUG -DQT_SQL_LIB -DQT_WIDGETS_LIB -DQT_XML_LIB -DUNICODE -DWIN32 -DWIN64 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -D_UNICODE -D_WIN64
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /O2 /Ob2 /DNDEBUG -std:c++17 -MD -Zc:__cplusplus -permissive- -utf-8 /YuD:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/CMakeFiles/XCamView.dir/cmake_pch.hxx /FpD:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/CMakeFiles/XCamView.dir/./cmake_pch.cxx.pch /FID:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/CMakeFiles/XCamView.dir/cmake_pch.hxx
  INCLUDES = -ID:\work\XCamView\XCamView\build\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\XCamView_autogen\include -ID:\work\XCamView\XCamView\lib\include -external:IE:\Qt\6.10.0\msvc2022_64\include\QtCore -external:IE:\Qt\6.10.0\msvc2022_64\include -external:IE:\Qt\6.10.0\msvc2022_64\mkspecs\win32-msvc -external:IE:\Qt\6.10.0\msvc2022_64\include\QtWidgets -external:IE:\Qt\6.10.0\msvc2022_64\include\QtGui -external:IE:\Qt\6.10.0\msvc2022_64\include\QtXml -external:IE:\Qt\6.10.0\msvc2022_64\include\QtSql -external:W0
  OBJECT_DIR = CMakeFiles\XCamView.dir
  OBJECT_FILE_DIR = CMakeFiles\XCamView.dir\panel\centralpanel
  TARGET_COMPILE_PDB = CMakeFiles\XCamView.dir\
  TARGET_PDB = XCamView.pdb

build CMakeFiles\XCamView.dir\panel\centralpanel\view.cpp.obj: CXX_COMPILER__XCamView_unscanned_Release D$:\work\XCamView\XCamView\panel\centralpanel\view.cpp | CMakeFiles\XCamView.dir\cmake_pch.hxx CMakeFiles\XCamView.dir\cmake_pch.cxx.pch || cmake_object_order_depends_target_XCamView
  DEFINES = -DQT_CORE_LIB -DQT_GUI_LIB -DQT_NO_DEBUG -DQT_SQL_LIB -DQT_WIDGETS_LIB -DQT_XML_LIB -DUNICODE -DWIN32 -DWIN64 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -D_UNICODE -D_WIN64
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /O2 /Ob2 /DNDEBUG -std:c++17 -MD -Zc:__cplusplus -permissive- -utf-8 /YuD:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/CMakeFiles/XCamView.dir/cmake_pch.hxx /FpD:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/CMakeFiles/XCamView.dir/./cmake_pch.cxx.pch /FID:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/CMakeFiles/XCamView.dir/cmake_pch.hxx
  INCLUDES = -ID:\work\XCamView\XCamView\build\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\XCamView_autogen\include -ID:\work\XCamView\XCamView\lib\include -external:IE:\Qt\6.10.0\msvc2022_64\include\QtCore -external:IE:\Qt\6.10.0\msvc2022_64\include -external:IE:\Qt\6.10.0\msvc2022_64\mkspecs\win32-msvc -external:IE:\Qt\6.10.0\msvc2022_64\include\QtWidgets -external:IE:\Qt\6.10.0\msvc2022_64\include\QtGui -external:IE:\Qt\6.10.0\msvc2022_64\include\QtXml -external:IE:\Qt\6.10.0\msvc2022_64\include\QtSql -external:W0
  OBJECT_DIR = CMakeFiles\XCamView.dir
  OBJECT_FILE_DIR = CMakeFiles\XCamView.dir\panel\centralpanel
  TARGET_COMPILE_PDB = CMakeFiles\XCamView.dir\
  TARGET_PDB = XCamView.pdb

build CMakeFiles\XCamView.dir\panel\chatpanel\chatwidget.cpp.obj: CXX_COMPILER__XCamView_unscanned_Release D$:\work\XCamView\XCamView\panel\chatpanel\chatwidget.cpp | CMakeFiles\XCamView.dir\cmake_pch.hxx CMakeFiles\XCamView.dir\cmake_pch.cxx.pch || cmake_object_order_depends_target_XCamView
  DEFINES = -DQT_CORE_LIB -DQT_GUI_LIB -DQT_NO_DEBUG -DQT_SQL_LIB -DQT_WIDGETS_LIB -DQT_XML_LIB -DUNICODE -DWIN32 -DWIN64 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -D_UNICODE -D_WIN64
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /O2 /Ob2 /DNDEBUG -std:c++17 -MD -Zc:__cplusplus -permissive- -utf-8 /YuD:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/CMakeFiles/XCamView.dir/cmake_pch.hxx /FpD:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/CMakeFiles/XCamView.dir/./cmake_pch.cxx.pch /FID:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/CMakeFiles/XCamView.dir/cmake_pch.hxx
  INCLUDES = -ID:\work\XCamView\XCamView\build\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\XCamView_autogen\include -ID:\work\XCamView\XCamView\lib\include -external:IE:\Qt\6.10.0\msvc2022_64\include\QtCore -external:IE:\Qt\6.10.0\msvc2022_64\include -external:IE:\Qt\6.10.0\msvc2022_64\mkspecs\win32-msvc -external:IE:\Qt\6.10.0\msvc2022_64\include\QtWidgets -external:IE:\Qt\6.10.0\msvc2022_64\include\QtGui -external:IE:\Qt\6.10.0\msvc2022_64\include\QtXml -external:IE:\Qt\6.10.0\msvc2022_64\include\QtSql -external:W0
  OBJECT_DIR = CMakeFiles\XCamView.dir
  OBJECT_FILE_DIR = CMakeFiles\XCamView.dir\panel\chatpanel
  TARGET_COMPILE_PDB = CMakeFiles\XCamView.dir\
  TARGET_PDB = XCamView.pdb

build CMakeFiles\XCamView.dir\panel\login\logindialog.cpp.obj: CXX_COMPILER__XCamView_unscanned_Release D$:\work\XCamView\XCamView\panel\login\logindialog.cpp | CMakeFiles\XCamView.dir\cmake_pch.hxx CMakeFiles\XCamView.dir\cmake_pch.cxx.pch || cmake_object_order_depends_target_XCamView
  DEFINES = -DQT_CORE_LIB -DQT_GUI_LIB -DQT_NO_DEBUG -DQT_SQL_LIB -DQT_WIDGETS_LIB -DQT_XML_LIB -DUNICODE -DWIN32 -DWIN64 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -D_UNICODE -D_WIN64
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /O2 /Ob2 /DNDEBUG -std:c++17 -MD -Zc:__cplusplus -permissive- -utf-8 /YuD:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/CMakeFiles/XCamView.dir/cmake_pch.hxx /FpD:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/CMakeFiles/XCamView.dir/./cmake_pch.cxx.pch /FID:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/CMakeFiles/XCamView.dir/cmake_pch.hxx
  INCLUDES = -ID:\work\XCamView\XCamView\build\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\XCamView_autogen\include -ID:\work\XCamView\XCamView\lib\include -external:IE:\Qt\6.10.0\msvc2022_64\include\QtCore -external:IE:\Qt\6.10.0\msvc2022_64\include -external:IE:\Qt\6.10.0\msvc2022_64\mkspecs\win32-msvc -external:IE:\Qt\6.10.0\msvc2022_64\include\QtWidgets -external:IE:\Qt\6.10.0\msvc2022_64\include\QtGui -external:IE:\Qt\6.10.0\msvc2022_64\include\QtXml -external:IE:\Qt\6.10.0\msvc2022_64\include\QtSql -external:W0
  OBJECT_DIR = CMakeFiles\XCamView.dir
  OBJECT_FILE_DIR = CMakeFiles\XCamView.dir\panel\login
  TARGET_COMPILE_PDB = CMakeFiles\XCamView.dir\
  TARGET_PDB = XCamView.pdb

build CMakeFiles\XCamView.dir\panel\measure\tpcalibrationdlg.cpp.obj: CXX_COMPILER__XCamView_unscanned_Release D$:\work\XCamView\XCamView\panel\measure\tpcalibrationdlg.cpp | CMakeFiles\XCamView.dir\cmake_pch.hxx CMakeFiles\XCamView.dir\cmake_pch.cxx.pch || cmake_object_order_depends_target_XCamView
  DEFINES = -DQT_CORE_LIB -DQT_GUI_LIB -DQT_NO_DEBUG -DQT_SQL_LIB -DQT_WIDGETS_LIB -DQT_XML_LIB -DUNICODE -DWIN32 -DWIN64 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -D_UNICODE -D_WIN64
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /O2 /Ob2 /DNDEBUG -std:c++17 -MD -Zc:__cplusplus -permissive- -utf-8 /YuD:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/CMakeFiles/XCamView.dir/cmake_pch.hxx /FpD:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/CMakeFiles/XCamView.dir/./cmake_pch.cxx.pch /FID:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/CMakeFiles/XCamView.dir/cmake_pch.hxx
  INCLUDES = -ID:\work\XCamView\XCamView\build\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\XCamView_autogen\include -ID:\work\XCamView\XCamView\lib\include -external:IE:\Qt\6.10.0\msvc2022_64\include\QtCore -external:IE:\Qt\6.10.0\msvc2022_64\include -external:IE:\Qt\6.10.0\msvc2022_64\mkspecs\win32-msvc -external:IE:\Qt\6.10.0\msvc2022_64\include\QtWidgets -external:IE:\Qt\6.10.0\msvc2022_64\include\QtGui -external:IE:\Qt\6.10.0\msvc2022_64\include\QtXml -external:IE:\Qt\6.10.0\msvc2022_64\include\QtSql -external:W0
  OBJECT_DIR = CMakeFiles\XCamView.dir
  OBJECT_FILE_DIR = CMakeFiles\XCamView.dir\panel\measure
  TARGET_COMPILE_PDB = CMakeFiles\XCamView.dir\
  TARGET_PDB = XCamView.pdb

build CMakeFiles\XCamView.dir\panel\measure\tptexteditdlg.cpp.obj: CXX_COMPILER__XCamView_unscanned_Release D$:\work\XCamView\XCamView\panel\measure\tptexteditdlg.cpp | CMakeFiles\XCamView.dir\cmake_pch.hxx CMakeFiles\XCamView.dir\cmake_pch.cxx.pch || cmake_object_order_depends_target_XCamView
  DEFINES = -DQT_CORE_LIB -DQT_GUI_LIB -DQT_NO_DEBUG -DQT_SQL_LIB -DQT_WIDGETS_LIB -DQT_XML_LIB -DUNICODE -DWIN32 -DWIN64 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -D_UNICODE -D_WIN64
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /O2 /Ob2 /DNDEBUG -std:c++17 -MD -Zc:__cplusplus -permissive- -utf-8 /YuD:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/CMakeFiles/XCamView.dir/cmake_pch.hxx /FpD:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/CMakeFiles/XCamView.dir/./cmake_pch.cxx.pch /FID:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/CMakeFiles/XCamView.dir/cmake_pch.hxx
  INCLUDES = -ID:\work\XCamView\XCamView\build\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\XCamView_autogen\include -ID:\work\XCamView\XCamView\lib\include -external:IE:\Qt\6.10.0\msvc2022_64\include\QtCore -external:IE:\Qt\6.10.0\msvc2022_64\include -external:IE:\Qt\6.10.0\msvc2022_64\mkspecs\win32-msvc -external:IE:\Qt\6.10.0\msvc2022_64\include\QtWidgets -external:IE:\Qt\6.10.0\msvc2022_64\include\QtGui -external:IE:\Qt\6.10.0\msvc2022_64\include\QtXml -external:IE:\Qt\6.10.0\msvc2022_64\include\QtSql -external:W0
  OBJECT_DIR = CMakeFiles\XCamView.dir
  OBJECT_FILE_DIR = CMakeFiles\XCamView.dir\panel\measure
  TARGET_COMPILE_PDB = CMakeFiles\XCamView.dir\
  TARGET_PDB = XCamView.pdb

build CMakeFiles\XCamView.dir\panel\monitorpanel\monitordialog.cpp.obj: CXX_COMPILER__XCamView_unscanned_Release D$:\work\XCamView\XCamView\panel\monitorpanel\monitordialog.cpp | CMakeFiles\XCamView.dir\cmake_pch.hxx CMakeFiles\XCamView.dir\cmake_pch.cxx.pch || cmake_object_order_depends_target_XCamView
  DEFINES = -DQT_CORE_LIB -DQT_GUI_LIB -DQT_NO_DEBUG -DQT_SQL_LIB -DQT_WIDGETS_LIB -DQT_XML_LIB -DUNICODE -DWIN32 -DWIN64 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -D_UNICODE -D_WIN64
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /O2 /Ob2 /DNDEBUG -std:c++17 -MD -Zc:__cplusplus -permissive- -utf-8 /YuD:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/CMakeFiles/XCamView.dir/cmake_pch.hxx /FpD:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/CMakeFiles/XCamView.dir/./cmake_pch.cxx.pch /FID:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/CMakeFiles/XCamView.dir/cmake_pch.hxx
  INCLUDES = -ID:\work\XCamView\XCamView\build\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\XCamView_autogen\include -ID:\work\XCamView\XCamView\lib\include -external:IE:\Qt\6.10.0\msvc2022_64\include\QtCore -external:IE:\Qt\6.10.0\msvc2022_64\include -external:IE:\Qt\6.10.0\msvc2022_64\mkspecs\win32-msvc -external:IE:\Qt\6.10.0\msvc2022_64\include\QtWidgets -external:IE:\Qt\6.10.0\msvc2022_64\include\QtGui -external:IE:\Qt\6.10.0\msvc2022_64\include\QtXml -external:IE:\Qt\6.10.0\msvc2022_64\include\QtSql -external:W0
  OBJECT_DIR = CMakeFiles\XCamView.dir
  OBJECT_FILE_DIR = CMakeFiles\XCamView.dir\panel\monitorpanel
  TARGET_COMPILE_PDB = CMakeFiles\XCamView.dir\
  TARGET_PDB = XCamView.pdb

build CMakeFiles\XCamView.dir\panel\monitorpanel\monitorwidget.cpp.obj: CXX_COMPILER__XCamView_unscanned_Release D$:\work\XCamView\XCamView\panel\monitorpanel\monitorwidget.cpp | CMakeFiles\XCamView.dir\cmake_pch.hxx CMakeFiles\XCamView.dir\cmake_pch.cxx.pch || cmake_object_order_depends_target_XCamView
  DEFINES = -DQT_CORE_LIB -DQT_GUI_LIB -DQT_NO_DEBUG -DQT_SQL_LIB -DQT_WIDGETS_LIB -DQT_XML_LIB -DUNICODE -DWIN32 -DWIN64 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -D_UNICODE -D_WIN64
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /O2 /Ob2 /DNDEBUG -std:c++17 -MD -Zc:__cplusplus -permissive- -utf-8 /YuD:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/CMakeFiles/XCamView.dir/cmake_pch.hxx /FpD:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/CMakeFiles/XCamView.dir/./cmake_pch.cxx.pch /FID:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/CMakeFiles/XCamView.dir/cmake_pch.hxx
  INCLUDES = -ID:\work\XCamView\XCamView\build\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\XCamView_autogen\include -ID:\work\XCamView\XCamView\lib\include -external:IE:\Qt\6.10.0\msvc2022_64\include\QtCore -external:IE:\Qt\6.10.0\msvc2022_64\include -external:IE:\Qt\6.10.0\msvc2022_64\mkspecs\win32-msvc -external:IE:\Qt\6.10.0\msvc2022_64\include\QtWidgets -external:IE:\Qt\6.10.0\msvc2022_64\include\QtGui -external:IE:\Qt\6.10.0\msvc2022_64\include\QtXml -external:IE:\Qt\6.10.0\msvc2022_64\include\QtSql -external:W0
  OBJECT_DIR = CMakeFiles\XCamView.dir
  OBJECT_FILE_DIR = CMakeFiles\XCamView.dir\panel\monitorpanel
  TARGET_COMPILE_PDB = CMakeFiles\XCamView.dir\
  TARGET_PDB = XCamView.pdb

build CMakeFiles\XCamView.dir\panel\collapsewidget.cpp.obj: CXX_COMPILER__XCamView_unscanned_Release D$:\work\XCamView\XCamView\panel\collapsewidget.cpp | CMakeFiles\XCamView.dir\cmake_pch.hxx CMakeFiles\XCamView.dir\cmake_pch.cxx.pch || cmake_object_order_depends_target_XCamView
  DEFINES = -DQT_CORE_LIB -DQT_GUI_LIB -DQT_NO_DEBUG -DQT_SQL_LIB -DQT_WIDGETS_LIB -DQT_XML_LIB -DUNICODE -DWIN32 -DWIN64 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -D_UNICODE -D_WIN64
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /O2 /Ob2 /DNDEBUG -std:c++17 -MD -Zc:__cplusplus -permissive- -utf-8 /YuD:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/CMakeFiles/XCamView.dir/cmake_pch.hxx /FpD:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/CMakeFiles/XCamView.dir/./cmake_pch.cxx.pch /FID:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/CMakeFiles/XCamView.dir/cmake_pch.hxx
  INCLUDES = -ID:\work\XCamView\XCamView\build\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\XCamView_autogen\include -ID:\work\XCamView\XCamView\lib\include -external:IE:\Qt\6.10.0\msvc2022_64\include\QtCore -external:IE:\Qt\6.10.0\msvc2022_64\include -external:IE:\Qt\6.10.0\msvc2022_64\mkspecs\win32-msvc -external:IE:\Qt\6.10.0\msvc2022_64\include\QtWidgets -external:IE:\Qt\6.10.0\msvc2022_64\include\QtGui -external:IE:\Qt\6.10.0\msvc2022_64\include\QtXml -external:IE:\Qt\6.10.0\msvc2022_64\include\QtSql -external:W0
  OBJECT_DIR = CMakeFiles\XCamView.dir
  OBJECT_FILE_DIR = CMakeFiles\XCamView.dir\panel
  TARGET_COMPILE_PDB = CMakeFiles\XCamView.dir\
  TARGET_PDB = XCamView.pdb

build CMakeFiles\XCamView.dir\panel\leftpanel.cpp.obj: CXX_COMPILER__XCamView_unscanned_Release D$:\work\XCamView\XCamView\panel\leftpanel.cpp | CMakeFiles\XCamView.dir\cmake_pch.hxx CMakeFiles\XCamView.dir\cmake_pch.cxx.pch || cmake_object_order_depends_target_XCamView
  DEFINES = -DQT_CORE_LIB -DQT_GUI_LIB -DQT_NO_DEBUG -DQT_SQL_LIB -DQT_WIDGETS_LIB -DQT_XML_LIB -DUNICODE -DWIN32 -DWIN64 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -D_UNICODE -D_WIN64
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /O2 /Ob2 /DNDEBUG -std:c++17 -MD -Zc:__cplusplus -permissive- -utf-8 /YuD:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/CMakeFiles/XCamView.dir/cmake_pch.hxx /FpD:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/CMakeFiles/XCamView.dir/./cmake_pch.cxx.pch /FID:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/CMakeFiles/XCamView.dir/cmake_pch.hxx
  INCLUDES = -ID:\work\XCamView\XCamView\build\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\XCamView_autogen\include -ID:\work\XCamView\XCamView\lib\include -external:IE:\Qt\6.10.0\msvc2022_64\include\QtCore -external:IE:\Qt\6.10.0\msvc2022_64\include -external:IE:\Qt\6.10.0\msvc2022_64\mkspecs\win32-msvc -external:IE:\Qt\6.10.0\msvc2022_64\include\QtWidgets -external:IE:\Qt\6.10.0\msvc2022_64\include\QtGui -external:IE:\Qt\6.10.0\msvc2022_64\include\QtXml -external:IE:\Qt\6.10.0\msvc2022_64\include\QtSql -external:W0
  OBJECT_DIR = CMakeFiles\XCamView.dir
  OBJECT_FILE_DIR = CMakeFiles\XCamView.dir\panel
  TARGET_COMPILE_PDB = CMakeFiles\XCamView.dir\
  TARGET_PDB = XCamView.pdb

build CMakeFiles\XCamView.dir\global.cpp.obj: CXX_COMPILER__XCamView_unscanned_Release D$:\work\XCamView\XCamView\global.cpp | CMakeFiles\XCamView.dir\cmake_pch.hxx CMakeFiles\XCamView.dir\cmake_pch.cxx.pch || cmake_object_order_depends_target_XCamView
  DEFINES = -DQT_CORE_LIB -DQT_GUI_LIB -DQT_NO_DEBUG -DQT_SQL_LIB -DQT_WIDGETS_LIB -DQT_XML_LIB -DUNICODE -DWIN32 -DWIN64 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -D_UNICODE -D_WIN64
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /O2 /Ob2 /DNDEBUG -std:c++17 -MD -Zc:__cplusplus -permissive- -utf-8 /YuD:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/CMakeFiles/XCamView.dir/cmake_pch.hxx /FpD:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/CMakeFiles/XCamView.dir/./cmake_pch.cxx.pch /FID:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/CMakeFiles/XCamView.dir/cmake_pch.hxx
  INCLUDES = -ID:\work\XCamView\XCamView\build\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\XCamView_autogen\include -ID:\work\XCamView\XCamView\lib\include -external:IE:\Qt\6.10.0\msvc2022_64\include\QtCore -external:IE:\Qt\6.10.0\msvc2022_64\include -external:IE:\Qt\6.10.0\msvc2022_64\mkspecs\win32-msvc -external:IE:\Qt\6.10.0\msvc2022_64\include\QtWidgets -external:IE:\Qt\6.10.0\msvc2022_64\include\QtGui -external:IE:\Qt\6.10.0\msvc2022_64\include\QtXml -external:IE:\Qt\6.10.0\msvc2022_64\include\QtSql -external:W0
  OBJECT_DIR = CMakeFiles\XCamView.dir
  OBJECT_FILE_DIR = CMakeFiles\XCamView.dir
  TARGET_COMPILE_PDB = CMakeFiles\XCamView.dir\
  TARGET_PDB = XCamView.pdb

build CMakeFiles\XCamView.dir\XCamView_autogen\PNK5WDWK6L\qrc_XCamView.cpp.obj: CXX_COMPILER__XCamView_unscanned_Release D$:\work\XCamView\XCamView\build\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\XCamView_autogen\PNK5WDWK6L\qrc_XCamView.cpp | CMakeFiles\XCamView.dir\cmake_pch.hxx CMakeFiles\XCamView.dir\cmake_pch.cxx.pch || cmake_object_order_depends_target_XCamView
  DEFINES = -DQT_CORE_LIB -DQT_GUI_LIB -DQT_NO_DEBUG -DQT_SQL_LIB -DQT_WIDGETS_LIB -DQT_XML_LIB -DUNICODE -DWIN32 -DWIN64 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -D_UNICODE -D_WIN64
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /O2 /Ob2 /DNDEBUG -std:c++17 -MD -Zc:__cplusplus -permissive- -utf-8 /YuD:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/CMakeFiles/XCamView.dir/cmake_pch.hxx /FpD:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/CMakeFiles/XCamView.dir/./cmake_pch.cxx.pch /FID:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/CMakeFiles/XCamView.dir/cmake_pch.hxx
  INCLUDES = -ID:\work\XCamView\XCamView\build\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\XCamView_autogen\include -ID:\work\XCamView\XCamView\lib\include -external:IE:\Qt\6.10.0\msvc2022_64\include\QtCore -external:IE:\Qt\6.10.0\msvc2022_64\include -external:IE:\Qt\6.10.0\msvc2022_64\mkspecs\win32-msvc -external:IE:\Qt\6.10.0\msvc2022_64\include\QtWidgets -external:IE:\Qt\6.10.0\msvc2022_64\include\QtGui -external:IE:\Qt\6.10.0\msvc2022_64\include\QtXml -external:IE:\Qt\6.10.0\msvc2022_64\include\QtSql -external:W0
  OBJECT_DIR = CMakeFiles\XCamView.dir
  OBJECT_FILE_DIR = CMakeFiles\XCamView.dir\XCamView_autogen\PNK5WDWK6L
  TARGET_COMPILE_PDB = CMakeFiles\XCamView.dir\
  TARGET_PDB = XCamView.pdb


# =============================================================================
# Link build statements for EXECUTABLE target XCamView


#############################################
# Link the executable XCamView.exe

build XCamView.exe: CXX_EXECUTABLE_LINKER__XCamView_Release CMakeFiles\XCamView.dir\cmake_pch.cxx.obj CMakeFiles\XCamView.dir\XCamView_autogen\mocs_compilation.cpp.obj CMakeFiles\XCamView.dir\main.cpp.obj CMakeFiles\XCamView.dir\mainwindow.cpp.obj CMakeFiles\XCamView.dir\module\camera\uvchamcam\uvchamcam.cpp.obj CMakeFiles\XCamView.dir\module\camera\uvchamcam\uvchamloader.cpp.obj CMakeFiles\XCamView.dir\module\camera\camera.cpp.obj CMakeFiles\XCamView.dir\module\camera\cameramanager.cpp.obj CMakeFiles\XCamView.dir\module\auxrect\auxrect.cpp.obj CMakeFiles\XCamView.dir\module\i18n\i18n.cpp.obj CMakeFiles\XCamView.dir\module\TpSaveLoader\tpsaveloader.cpp.obj CMakeFiles\XCamView.dir\module\TpSignalConnect\TpSignalConnect.cpp.obj CMakeFiles\XCamView.dir\module\tutils\tutils.cpp.obj CMakeFiles\XCamView.dir\panel\camerapanel\cameralistwidget.cpp.obj CMakeFiles\XCamView.dir\panel\camerapanel\camerapanel.cpp.obj CMakeFiles\XCamView.dir\panel\camerapanel\coloradjustwidget.cpp.obj CMakeFiles\XCamView.dir\panel\camerapanel\exposuregainwidget.cpp.obj CMakeFiles\XCamView.dir\panel\camerapanel\powerfrequencywidget.cpp.obj CMakeFiles\XCamView.dir\panel\camerapanel\resolutionwidget.cpp.obj CMakeFiles\XCamView.dir\panel\camerapanel\whitebalancewidget.cpp.obj CMakeFiles\XCamView.dir\panel\centralpanel\displayview\displayview.cpp.obj CMakeFiles\XCamView.dir\panel\centralpanel\displayview\imageview.cpp.obj CMakeFiles\XCamView.dir\panel\centralpanel\displayview\previewview.cpp.obj CMakeFiles\XCamView.dir\panel\centralpanel\monitorview\monitorview.cpp.obj CMakeFiles\XCamView.dir\panel\centralpanel\tptabwidget.cpp.obj CMakeFiles\XCamView.dir\panel\centralpanel\view.cpp.obj CMakeFiles\XCamView.dir\panel\chatpanel\chatwidget.cpp.obj CMakeFiles\XCamView.dir\panel\login\logindialog.cpp.obj CMakeFiles\XCamView.dir\panel\measure\tpcalibrationdlg.cpp.obj CMakeFiles\XCamView.dir\panel\measure\tptexteditdlg.cpp.obj CMakeFiles\XCamView.dir\panel\monitorpanel\monitordialog.cpp.obj CMakeFiles\XCamView.dir\panel\monitorpanel\monitorwidget.cpp.obj CMakeFiles\XCamView.dir\panel\collapsewidget.cpp.obj CMakeFiles\XCamView.dir\panel\leftpanel.cpp.obj CMakeFiles\XCamView.dir\global.cpp.obj CMakeFiles\XCamView.dir\XCamView_autogen\PNK5WDWK6L\qrc_XCamView.cpp.obj | E$:\Qt\6.10.0\msvc2022_64\lib\Qt6Widgets.lib E$:\Qt\6.10.0\msvc2022_64\lib\Qt6Xml.lib E$:\Qt\6.10.0\msvc2022_64\lib\Qt6Sql.lib D$:\work\XCamView\XCamView\lib\x64\TpLog.lib D$:\work\XCamView\XCamView\lib\x64\TpMeasureManager.lib E$:\Qt\6.10.0\msvc2022_64\lib\Qt6Gui.lib E$:\Qt\6.10.0\msvc2022_64\lib\Qt6Core.lib E$:\Qt\6.10.0\msvc2022_64\lib\Qt6EntryPoint.lib || XCamView_autogen XCamView_autogen_timestamp_deps
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /O2 /Ob2 /DNDEBUG -MD
  LINK_FLAGS = /machine:x64 /INCREMENTAL:NO /subsystem:windows
  LINK_LIBRARIES = E:\Qt\6.10.0\msvc2022_64\lib\Qt6Widgets.lib  E:\Qt\6.10.0\msvc2022_64\lib\Qt6Xml.lib  E:\Qt\6.10.0\msvc2022_64\lib\Qt6Sql.lib  D:\work\XCamView\XCamView\lib\x64\TpLog.lib  D:\work\XCamView\XCamView\lib\x64\TpMeasureManager.lib  E:\Qt\6.10.0\msvc2022_64\lib\Qt6Gui.lib  d3d11.lib  dxgi.lib  dxguid.lib  d3d12.lib  E:\Qt\6.10.0\msvc2022_64\lib\Qt6Core.lib  mpr.lib  userenv.lib  E:\Qt\6.10.0\msvc2022_64\lib\Qt6EntryPoint.lib  shell32.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib
  OBJECT_DIR = CMakeFiles\XCamView.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  TARGET_COMPILE_PDB = CMakeFiles\XCamView.dir\
  TARGET_FILE = XCamView.exe
  TARGET_IMPLIB = XCamView.lib
  TARGET_PDB = XCamView.pdb


#############################################
# Utility command for edit_cache

build CMakeFiles\edit_cache.util: CUSTOM_COMMAND
  COMMAND = C:\Windows\system32\cmd.exe /C "cd /D D:\work\XCamView\XCamView\build\Desktop_Qt_6_10_0_MSVC2022_64bit-Release && E:\Qt\Tools\CMake_64\bin\cmake-gui.exe -SD:\work\XCamView\XCamView -BD:\work\XCamView\XCamView\build\Desktop_Qt_6_10_0_MSVC2022_64bit-Release"
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build edit_cache: phony CMakeFiles\edit_cache.util


#############################################
# Utility command for rebuild_cache

build CMakeFiles\rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = C:\Windows\system32\cmd.exe /C "cd /D D:\work\XCamView\XCamView\build\Desktop_Qt_6_10_0_MSVC2022_64bit-Release && E:\Qt\Tools\CMake_64\bin\cmake.exe --regenerate-during-build -SD:\work\XCamView\XCamView -BD:\work\XCamView\XCamView\build\Desktop_Qt_6_10_0_MSVC2022_64bit-Release"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles\rebuild_cache.util


#############################################
# Utility command for list_install_components

build list_install_components: phony


#############################################
# Utility command for install

build CMakeFiles\install.util: CUSTOM_COMMAND all
  COMMAND = C:\Windows\system32\cmd.exe /C "cd /D D:\work\XCamView\XCamView\build\Desktop_Qt_6_10_0_MSVC2022_64bit-Release && E:\Qt\Tools\CMake_64\bin\cmake.exe -P cmake_install.cmake"
  DESC = Install the project...
  pool = console
  restat = 1

build install: phony CMakeFiles\install.util


#############################################
# Utility command for install/local

build CMakeFiles\install\local.util: CUSTOM_COMMAND all
  COMMAND = C:\Windows\system32\cmd.exe /C "cd /D D:\work\XCamView\XCamView\build\Desktop_Qt_6_10_0_MSVC2022_64bit-Release && E:\Qt\Tools\CMake_64\bin\cmake.exe -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake"
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build install\local: phony CMakeFiles\install\local.util


#############################################
# Utility command for XCamView_autogen_timestamp_deps

build XCamView_autogen_timestamp_deps: phony


#############################################
# Utility command for XCamView_autogen

build XCamView_autogen: phony CMakeFiles\XCamView_autogen XCamView_autogen\timestamp XCamView_autogen\mocs_compilation.cpp XCamView_autogen_timestamp_deps


#############################################
# Custom command for XCamView_autogen\timestamp

build XCamView_autogen\timestamp XCamView_autogen\mocs_compilation.cpp | ${cmake_ninja_workdir}XCamView_autogen\timestamp ${cmake_ninja_workdir}XCamView_autogen\mocs_compilation.cpp: CUSTOM_COMMAND E$:\Qt\6.10.0\msvc2022_64\bin\moc.exe E$:\Qt\6.10.0\msvc2022_64\bin\uic.exe || XCamView_autogen_timestamp_deps
  COMMAND = C:\Windows\system32\cmd.exe /C "cd /D D:\work\XCamView\XCamView\build\Desktop_Qt_6_10_0_MSVC2022_64bit-Release && E:\Qt\Tools\CMake_64\bin\cmake.exe -E cmake_autogen D:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/CMakeFiles/XCamView_autogen.dir/AutogenInfo.json Release && E:\Qt\Tools\CMake_64\bin\cmake.exe -E touch D:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/XCamView_autogen/timestamp && E:\Qt\Tools\CMake_64\bin\cmake.exe -E cmake_transform_depfile Ninja gccdepfile D:/work/XCamView/XCamView D:/work/XCamView/XCamView D:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release D:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release D:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/XCamView_autogen/deps D:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/CMakeFiles/d/89e5b3fc135c8a026f3702009720a71a2a41bfaab38a85e5361b4d697171047e.d"
  DESC = Automatic MOC and UIC for target XCamView
  depfile = CMakeFiles\d\89e5b3fc135c8a026f3702009720a71a2a41bfaab38a85e5361b4d697171047e.d
  deps = gcc
  restat = 1


#############################################
# Custom command for XCamView_autogen\PNK5WDWK6L\qrc_XCamView.cpp

build XCamView_autogen\PNK5WDWK6L\qrc_XCamView.cpp | ${cmake_ninja_workdir}XCamView_autogen\PNK5WDWK6L\qrc_XCamView.cpp: CUSTOM_COMMAND D$:\work\XCamView\XCamView\res\XCamView.qrc CMakeFiles\XCamView_autogen.dir\AutoRcc_XCamView_PNK5WDWK6L_Info.json D$:\work\XCamView\XCamView\res\images\flip.png D$:\work\XCamView\XCamView\res\images\camera.png D$:\work\XCamView\XCamView\res\images\misc.png D$:\work\XCamView\XCamView\res\images\collapse.png D$:\work\XCamView\XCamView\res\images\save.png D$:\work\XCamView\XCamView\res\images\open.png D$:\work\XCamView\XCamView\res\images\exposure.png D$:\work\XCamView\XCamView\res\images\cooling.png D$:\work\XCamView\XCamView\res\images\res.png D$:\work\XCamView\XCamView\res\images\capture.png D$:\work\XCamView\XCamView\res\images\blacklevel.png D$:\work\XCamView\XCamView\res\images\record_stop.png D$:\work\XCamView\XCamView\res\images\bitdepth.png D$:\work\XCamView\XCamView\res\images\expand.png D$:\work\XCamView\XCamView\res\images\dragMoveCursor.png D$:\work\XCamView\XCamView\res\images\qsave.png D$:\work\XCamView\XCamView\res\images\light.png D$:\work\XCamView\XCamView\res\images\sample.png D$:\work\XCamView\XCamView\res\images\histogram.png D$:\work\XCamView\XCamView\res\images\color.png D$:\work\XCamView\XCamView\res\images\record_start.png D$:\work\XCamView\XCamView\res\images\dfc.png D$:\work\XCamView\XCamView\res\images\speed.png D$:\work\XCamView\XCamView\res\images\labelMoveCursor.png D$:\work\XCamView\XCamView\res\images\cg.png D$:\work\XCamView\XCamView\res\images\measure\arbline.svg D$:\work\XCamView\XCamView\res\images\measure\polygon.svg D$:\work\XCamView\XCamView\res\images\measure\select.svg D$:\work\XCamView\XCamView\res\images\measure\scalebar.svg D$:\work\XCamView\XCamView\res\images\measure\angle.svg D$:\work\XCamView\XCamView\res\images\measure\curve.svg D$:\work\XCamView\XCamView\res\images\measure\export.svg D$:\work\XCamView\XCamView\res\images\measure\vtline1.svg D$:\work\XCamView\XCamView\res\images\measure\layerburn.svg D$:\work\XCamView\XCamView\res\images\measure\twoCircles1.svg D$:\work\XCamView\XCamView\res\images\measure\delete.svg D$:\work\XCamView\XCamView\res\images\measure\vtline2.svg D$:\work\XCamView\XCamView\res\images\measure\circle3.svg D$:\work\XCamView\XCamView\res\images\measure\parallel.svg D$:\work\XCamView\XCamView\res\images\measure\arrow.svg D$:\work\XCamView\XCamView\res\images\measure\twoCircles2.svg D$:\work\XCamView\XCamView\res\images\measure\point.svg D$:\work\XCamView\XCamView\res\images\measure\annulus.svg D$:\work\XCamView\XCamView\res\images\measure\ellipse.svg D$:\work\XCamView\XCamView\res\images\measure\angle2.svg D$:\work\XCamView\XCamView\res\images\measure\twoParallel.svg D$:\work\XCamView\XCamView\res\images\measure\layernorm.svg D$:\work\XCamView\XCamView\res\images\measure\hline.svg D$:\work\XCamView\XCamView\res\images\measure\vline.svg D$:\work\XCamView\XCamView\res\images\measure\calibration.svg D$:\work\XCamView\XCamView\res\images\measure\circle.svg D$:\work\XCamView\XCamView\res\images\measure\twoCircles.svg D$:\work\XCamView\XCamView\res\images\measure\circle2.svg D$:\work\XCamView\XCamView\res\images\measure\circle1.svg D$:\work\XCamView\XCamView\res\images\measure\text.svg D$:\work\XCamView\XCamView\res\images\measure\vtline.svg D$:\work\XCamView\XCamView\res\images\measure\rectangle.svg D$:\work\XCamView\XCamView\res\images\measure\arc.svg D$:\work\XCamView\XCamView\res\images\measure\angle1.svg E$:\Qt\6.10.0\msvc2022_64\bin\rcc.exe E$:\Qt\6.10.0\msvc2022_64\bin\rcc.exe || XCamView_autogen XCamView_autogen_timestamp_deps
  COMMAND = C:\Windows\system32\cmd.exe /C "cd /D D:\work\XCamView\XCamView\build\Desktop_Qt_6_10_0_MSVC2022_64bit-Release && E:\Qt\Tools\CMake_64\bin\cmake.exe -E cmake_autorcc D:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/CMakeFiles/XCamView_autogen.dir/AutoRcc_XCamView_PNK5WDWK6L_Info.json Release"
  DESC = Automatic RCC for res/XCamView.qrc
  restat = 1


#############################################
# Phony custom command for CMakeFiles\XCamView_autogen

build CMakeFiles\XCamView_autogen | ${cmake_ninja_workdir}CMakeFiles\XCamView_autogen: phony XCamView_autogen\timestamp || XCamView_autogen_timestamp_deps

# =============================================================================
# Target aliases.

build XCamView: phony XCamView.exe

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: D:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release

build all: phony XCamView.exe

# =============================================================================
# Built-in targets


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja: RERUN_CMAKE | .qtc\package-manager\auto-setup.cmake .qtc\package-manager\maintenance_tool_provider.cmake CMakeCache.txt CMakeFiles\3.30.5\CMakeCXXCompiler.cmake CMakeFiles\3.30.5\CMakeRCCompiler.cmake CMakeFiles\3.30.5\CMakeSystem.cmake D$:\work\XCamView\XCamView\CMakeLists.txt D$:\work\XCamView\XCamView\res\XCamView.qrc E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsAdditionalTargetInfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsConfig.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsConfigVersion.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsConfigVersionImpl.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsDependencies.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsTargets-debug.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsTargets-relwithdebinfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsTargets.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsVersionlessTargets.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreAdditionalTargetInfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreConfig.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreConfigExtras.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreConfigVersion.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreConfigVersionImpl.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreDependencies.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreMacros.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreTargets-debug.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreTargets-relwithdebinfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreTargets.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreVersionlessAliasTargets.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateAdditionalTargetInfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateConfig.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateConfigVersion.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateConfigVersionImpl.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateTargets-debug.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateTargets-relwithdebinfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateTargets.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateVersionlessAliasTargets.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsAdditionalTargetInfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsConfig.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsConfigVersion.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsConfigVersionImpl.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsDependencies.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsTargets-debug.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsTargets-relwithdebinfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsTargets.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsVersionlessTargets.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6GuiAdditionalTargetInfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6GuiConfig.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6GuiConfigVersion.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6GuiConfigVersionImpl.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6GuiDependencies.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6GuiPlugins.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6GuiTargets-debug.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6GuiTargets-relwithdebinfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6GuiTargets.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6GuiVersionlessAliasTargets.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QGifPluginAdditionalTargetInfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QGifPluginConfig.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QGifPluginTargets-debug.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QGifPluginTargets-relwithdebinfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QGifPluginTargets.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QICNSPluginAdditionalTargetInfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QICNSPluginConfig.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QICNSPluginTargets-debug.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QICNSPluginTargets-relwithdebinfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QICNSPluginTargets.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QICOPluginAdditionalTargetInfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QICOPluginConfig.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QICOPluginTargets-debug.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QICOPluginTargets-relwithdebinfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QICOPluginTargets.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QJpegPluginAdditionalTargetInfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QJpegPluginConfig.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QJpegPluginTargets-debug.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QJpegPluginTargets-relwithdebinfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QJpegPluginTargets.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginAdditionalTargetInfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginConfig.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginTargets-debug.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginTargets-relwithdebinfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginTargets.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginAdditionalTargetInfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginConfig.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginTargets-debug.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginTargets-relwithdebinfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginTargets.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginAdditionalTargetInfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginConfig.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginTargets-debug.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginTargets-relwithdebinfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginTargets.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QSvgPluginAdditionalTargetInfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QSvgPluginConfig.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QSvgPluginTargets-debug.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QSvgPluginTargets-relwithdebinfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QSvgPluginTargets.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QTgaPluginAdditionalTargetInfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QTgaPluginConfig.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QTgaPluginTargets-debug.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QTgaPluginTargets-relwithdebinfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QTgaPluginTargets.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QTiffPluginAdditionalTargetInfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QTiffPluginConfig.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QTiffPluginTargets-debug.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QTiffPluginTargets-relwithdebinfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QTiffPluginTargets.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginAdditionalTargetInfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginConfig.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginTargets-debug.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginTargets-relwithdebinfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginTargets.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QVirtualKeyboardPluginAdditionalTargetInfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QVirtualKeyboardPluginConfig.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QVirtualKeyboardPluginTargets-debug.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QVirtualKeyboardPluginTargets-relwithdebinfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QVirtualKeyboardPluginTargets.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWbmpPluginAdditionalTargetInfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWbmpPluginConfig.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWbmpPluginTargets-debug.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWbmpPluginTargets-relwithdebinfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWbmpPluginTargets.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWebpPluginAdditionalTargetInfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWebpPluginConfig.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWebpPluginTargets-debug.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWebpPluginTargets-relwithdebinfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWebpPluginTargets.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginAdditionalTargetInfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginConfig.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginTargets-debug.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginTargets-relwithdebinfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginTargets.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginAdditionalTargetInfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginConfig.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginTargets-debug.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginTargets-relwithdebinfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginTargets.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Sql\Qt6QIBaseDriverPluginAdditionalTargetInfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Sql\Qt6QIBaseDriverPluginConfig.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Sql\Qt6QIBaseDriverPluginTargets-debug.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Sql\Qt6QIBaseDriverPluginTargets-relwithdebinfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Sql\Qt6QIBaseDriverPluginTargets.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Sql\Qt6QMimerSQLDriverPluginAdditionalTargetInfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Sql\Qt6QMimerSQLDriverPluginConfig.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Sql\Qt6QMimerSQLDriverPluginTargets-debug.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Sql\Qt6QMimerSQLDriverPluginTargets-relwithdebinfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Sql\Qt6QMimerSQLDriverPluginTargets.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Sql\Qt6QOCIDriverPluginAdditionalTargetInfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Sql\Qt6QOCIDriverPluginConfig.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Sql\Qt6QOCIDriverPluginTargets-debug.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Sql\Qt6QOCIDriverPluginTargets-relwithdebinfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Sql\Qt6QOCIDriverPluginTargets.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Sql\Qt6QODBCDriverPluginAdditionalTargetInfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Sql\Qt6QODBCDriverPluginConfig.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Sql\Qt6QODBCDriverPluginTargets-debug.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Sql\Qt6QODBCDriverPluginTargets-relwithdebinfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Sql\Qt6QODBCDriverPluginTargets.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Sql\Qt6QPSQLDriverPluginAdditionalTargetInfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Sql\Qt6QPSQLDriverPluginConfig.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Sql\Qt6QPSQLDriverPluginTargets-debug.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Sql\Qt6QPSQLDriverPluginTargets-relwithdebinfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Sql\Qt6QPSQLDriverPluginTargets.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Sql\Qt6QSQLiteDriverPluginAdditionalTargetInfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Sql\Qt6QSQLiteDriverPluginConfig.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Sql\Qt6QSQLiteDriverPluginTargets-debug.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Sql\Qt6QSQLiteDriverPluginTargets-relwithdebinfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Sql\Qt6QSQLiteDriverPluginTargets.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Sql\Qt6SqlAdditionalTargetInfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Sql\Qt6SqlConfig.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Sql\Qt6SqlConfigVersion.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Sql\Qt6SqlConfigVersionImpl.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Sql\Qt6SqlDependencies.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Sql\Qt6SqlPlugins.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Sql\Qt6SqlTargets-debug.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Sql\Qt6SqlTargets-relwithdebinfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Sql\Qt6SqlTargets.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Sql\Qt6SqlVersionlessAliasTargets.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsAdditionalTargetInfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsConfig.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsConfigVersion.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsConfigVersionImpl.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsDependencies.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsTargets-debug.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsTargets-relwithdebinfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsTargets.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsVersionlessTargets.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6QModernWindowsStylePluginAdditionalTargetInfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6QModernWindowsStylePluginConfig.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6QModernWindowsStylePluginTargets-debug.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6QModernWindowsStylePluginTargets-relwithdebinfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6QModernWindowsStylePluginTargets.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsAdditionalTargetInfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsConfig.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsConfigVersion.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsConfigVersionImpl.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsDependencies.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsMacros.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsPlugins.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsTargets-debug.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsTargets-relwithdebinfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsTargets.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsVersionlessAliasTargets.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Xml\Qt6XmlAdditionalTargetInfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Xml\Qt6XmlConfig.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Xml\Qt6XmlConfigVersion.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Xml\Qt6XmlConfigVersionImpl.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Xml\Qt6XmlDependencies.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Xml\Qt6XmlTargets-debug.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Xml\Qt6XmlTargets-relwithdebinfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Xml\Qt6XmlTargets.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Xml\Qt6XmlVersionlessAliasTargets.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateAdditionalTargetInfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateConfig.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateConfigVersion.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateConfigVersionImpl.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateTargets.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateVersionlessAliasTargets.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6\FindWrapAtomic.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6\FindWrapVulkanHeaders.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6\Qt6Config.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6\Qt6ConfigExtras.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6\Qt6ConfigVersion.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6\Qt6ConfigVersionImpl.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6\Qt6Dependencies.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6\Qt6Targets.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6\Qt6VersionlessAliasTargets.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6\QtFeature.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6\QtFeatureCommon.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6\QtInstallPaths.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6\QtPublicAndroidHelpers.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6\QtPublicAppleHelpers.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6\QtPublicCMakeEarlyPolicyHelpers.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6\QtPublicCMakeHelpers.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6\QtPublicCMakeVersionHelpers.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6\QtPublicDependencyHelpers.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6\QtPublicExternalProjectHelpers.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6\QtPublicFinalizerHelpers.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6\QtPublicFindPackageHelpers.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6\QtPublicGitHelpers.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6\QtPublicPluginHelpers.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6\QtPublicPluginHelpers_v2.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6\QtPublicSbomAttributionHelpers.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6\QtPublicSbomCpeHelpers.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6\QtPublicSbomDepHelpers.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6\QtPublicSbomFileHelpers.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6\QtPublicSbomGenerationHelpers.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6\QtPublicSbomHelpers.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6\QtPublicSbomLicenseHelpers.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6\QtPublicSbomOpsHelpers.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6\QtPublicSbomPurlHelpers.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6\QtPublicSbomPythonHelpers.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6\QtPublicSbomQtEntityHelpers.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6\QtPublicSbomSystemDepHelpers.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6\QtPublicTargetHelpers.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6\QtPublicTestHelpers.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6\QtPublicToolHelpers.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6\QtPublicWalkLibsHelpers.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6\QtPublicWindowsHelpers.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeCXXCompiler.cmake.in E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeCXXCompilerABI.cpp E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeCXXInformation.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeCheckCompilerFlagCommonPatterns.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeCommonLanguageInclude.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeCompilerIdDetection.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeDetermineCXXCompiler.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeDetermineCompiler.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeDetermineCompilerABI.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeDetermineCompilerId.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeDetermineCompilerSupport.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeDetermineRCCompiler.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeDetermineSystem.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeFindBinUtils.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeFindDependencyMacro.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeGenericSystem.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeInitializeConfigs.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeLanguageInformation.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeNinjaFindMake.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeParseImplicitIncludeInfo.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeParseImplicitLinkInfo.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeParseLibraryArchitecture.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeRCCompiler.cmake.in E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeRCInformation.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeSystem.cmake.in E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeSystemSpecificInformation.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeSystemSpecificInitialize.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeTestCXXCompiler.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeTestCompilerCommon.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeTestRCCompiler.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CheckCXXCompilerFlag.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CheckCXXSourceCompiles.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CheckIncludeFileCXX.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CheckLibraryExists.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\ADSP-DetermineCompiler.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\ARMCC-DetermineCompiler.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\ARMClang-DetermineCompiler.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\AppleClang-DetermineCompiler.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\Borland-DetermineCompiler.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\CMakeCommonCompilerMacros.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\Clang-DetermineCompiler.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\Clang-DetermineCompilerInternal.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\Compaq-CXX-DetermineCompiler.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\Cray-DetermineCompiler.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\CrayClang-DetermineCompiler.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\Embarcadero-DetermineCompiler.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\Fujitsu-DetermineCompiler.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\FujitsuClang-DetermineCompiler.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\GHS-DetermineCompiler.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\GNU-CXX-DetermineCompiler.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\HP-CXX-DetermineCompiler.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\IAR-DetermineCompiler.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\IBMCPP-CXX-DetermineVersionInternal.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\IBMClang-CXX-DetermineCompiler.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\Intel-DetermineCompiler.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\IntelLLVM-DetermineCompiler.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\LCC-CXX-DetermineCompiler.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\MSVC-CXX-CXXImportStd.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\MSVC-CXX.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\MSVC-DetermineCompiler.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\MSVC.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\NVHPC-DetermineCompiler.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\NVIDIA-DetermineCompiler.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\OpenWatcom-DetermineCompiler.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\OrangeC-DetermineCompiler.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\PGI-DetermineCompiler.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\PathScale-DetermineCompiler.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\SCO-DetermineCompiler.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\SunPro-CXX-DetermineCompiler.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\TI-DetermineCompiler.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\TIClang-DetermineCompiler.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\Tasking-DetermineCompiler.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\VisualAge-CXX-DetermineCompiler.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\Watcom-DetermineCompiler.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\XL-CXX-DetermineCompiler.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\XLClang-CXX-DetermineCompiler.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\zOS-CXX-DetermineCompiler.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\FindPackageHandleStandardArgs.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\FindPackageMessage.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\FindThreads.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\FindVulkan.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\GNUInstallDirs.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Internal\CMakeDetermineLinkerId.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Internal\CheckCompilerFlag.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Internal\CheckFlagCommonConfig.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Internal\CheckSourceCompiles.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Internal\FeatureTesting.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Platform\Windows-Determine-CXX.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Platform\Windows-Initialize.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Platform\Windows-MSVC-CXX.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Platform\Windows-MSVC.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Platform\Windows.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Platform\WindowsPaths.cmake
  pool = console


#############################################
# A missing CMake input file is not an error.

build .qtc\package-manager\auto-setup.cmake .qtc\package-manager\maintenance_tool_provider.cmake CMakeCache.txt CMakeFiles\3.30.5\CMakeCXXCompiler.cmake CMakeFiles\3.30.5\CMakeRCCompiler.cmake CMakeFiles\3.30.5\CMakeSystem.cmake D$:\work\XCamView\XCamView\CMakeLists.txt D$:\work\XCamView\XCamView\res\XCamView.qrc E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsAdditionalTargetInfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsConfig.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsConfigVersion.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsConfigVersionImpl.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsDependencies.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsTargets-debug.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsTargets-relwithdebinfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsTargets.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsVersionlessTargets.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreAdditionalTargetInfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreConfig.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreConfigExtras.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreConfigVersion.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreConfigVersionImpl.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreDependencies.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreMacros.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreTargets-debug.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreTargets-relwithdebinfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreTargets.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Core\Qt6CoreVersionlessAliasTargets.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateAdditionalTargetInfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateConfig.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateConfigVersion.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateConfigVersionImpl.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateTargets-debug.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateTargets-relwithdebinfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateTargets.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateVersionlessAliasTargets.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsAdditionalTargetInfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsConfig.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsConfigVersion.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsConfigVersionImpl.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsDependencies.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsTargets-debug.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsTargets-relwithdebinfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsTargets.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsVersionlessTargets.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6GuiAdditionalTargetInfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6GuiConfig.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6GuiConfigVersion.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6GuiConfigVersionImpl.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6GuiDependencies.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6GuiPlugins.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6GuiTargets-debug.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6GuiTargets-relwithdebinfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6GuiTargets.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6GuiVersionlessAliasTargets.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QGifPluginAdditionalTargetInfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QGifPluginConfig.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QGifPluginTargets-debug.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QGifPluginTargets-relwithdebinfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QGifPluginTargets.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QICNSPluginAdditionalTargetInfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QICNSPluginConfig.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QICNSPluginTargets-debug.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QICNSPluginTargets-relwithdebinfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QICNSPluginTargets.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QICOPluginAdditionalTargetInfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QICOPluginConfig.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QICOPluginTargets-debug.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QICOPluginTargets-relwithdebinfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QICOPluginTargets.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QJpegPluginAdditionalTargetInfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QJpegPluginConfig.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QJpegPluginTargets-debug.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QJpegPluginTargets-relwithdebinfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QJpegPluginTargets.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginAdditionalTargetInfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginConfig.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginTargets-debug.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginTargets-relwithdebinfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginTargets.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginAdditionalTargetInfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginConfig.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginTargets-debug.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginTargets-relwithdebinfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginTargets.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginAdditionalTargetInfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginConfig.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginTargets-debug.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginTargets-relwithdebinfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginTargets.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QSvgPluginAdditionalTargetInfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QSvgPluginConfig.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QSvgPluginTargets-debug.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QSvgPluginTargets-relwithdebinfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QSvgPluginTargets.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QTgaPluginAdditionalTargetInfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QTgaPluginConfig.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QTgaPluginTargets-debug.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QTgaPluginTargets-relwithdebinfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QTgaPluginTargets.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QTiffPluginAdditionalTargetInfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QTiffPluginConfig.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QTiffPluginTargets-debug.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QTiffPluginTargets-relwithdebinfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QTiffPluginTargets.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginAdditionalTargetInfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginConfig.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginTargets-debug.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginTargets-relwithdebinfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginTargets.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QVirtualKeyboardPluginAdditionalTargetInfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QVirtualKeyboardPluginConfig.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QVirtualKeyboardPluginTargets-debug.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QVirtualKeyboardPluginTargets-relwithdebinfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QVirtualKeyboardPluginTargets.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWbmpPluginAdditionalTargetInfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWbmpPluginConfig.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWbmpPluginTargets-debug.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWbmpPluginTargets-relwithdebinfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWbmpPluginTargets.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWebpPluginAdditionalTargetInfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWebpPluginConfig.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWebpPluginTargets-debug.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWebpPluginTargets-relwithdebinfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWebpPluginTargets.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginAdditionalTargetInfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginConfig.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginTargets-debug.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginTargets-relwithdebinfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginTargets.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginAdditionalTargetInfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginConfig.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginTargets-debug.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginTargets-relwithdebinfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginTargets.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Sql\Qt6QIBaseDriverPluginAdditionalTargetInfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Sql\Qt6QIBaseDriverPluginConfig.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Sql\Qt6QIBaseDriverPluginTargets-debug.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Sql\Qt6QIBaseDriverPluginTargets-relwithdebinfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Sql\Qt6QIBaseDriverPluginTargets.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Sql\Qt6QMimerSQLDriverPluginAdditionalTargetInfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Sql\Qt6QMimerSQLDriverPluginConfig.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Sql\Qt6QMimerSQLDriverPluginTargets-debug.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Sql\Qt6QMimerSQLDriverPluginTargets-relwithdebinfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Sql\Qt6QMimerSQLDriverPluginTargets.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Sql\Qt6QOCIDriverPluginAdditionalTargetInfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Sql\Qt6QOCIDriverPluginConfig.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Sql\Qt6QOCIDriverPluginTargets-debug.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Sql\Qt6QOCIDriverPluginTargets-relwithdebinfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Sql\Qt6QOCIDriverPluginTargets.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Sql\Qt6QODBCDriverPluginAdditionalTargetInfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Sql\Qt6QODBCDriverPluginConfig.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Sql\Qt6QODBCDriverPluginTargets-debug.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Sql\Qt6QODBCDriverPluginTargets-relwithdebinfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Sql\Qt6QODBCDriverPluginTargets.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Sql\Qt6QPSQLDriverPluginAdditionalTargetInfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Sql\Qt6QPSQLDriverPluginConfig.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Sql\Qt6QPSQLDriverPluginTargets-debug.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Sql\Qt6QPSQLDriverPluginTargets-relwithdebinfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Sql\Qt6QPSQLDriverPluginTargets.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Sql\Qt6QSQLiteDriverPluginAdditionalTargetInfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Sql\Qt6QSQLiteDriverPluginConfig.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Sql\Qt6QSQLiteDriverPluginTargets-debug.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Sql\Qt6QSQLiteDriverPluginTargets-relwithdebinfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Sql\Qt6QSQLiteDriverPluginTargets.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Sql\Qt6SqlAdditionalTargetInfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Sql\Qt6SqlConfig.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Sql\Qt6SqlConfigVersion.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Sql\Qt6SqlConfigVersionImpl.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Sql\Qt6SqlDependencies.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Sql\Qt6SqlPlugins.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Sql\Qt6SqlTargets-debug.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Sql\Qt6SqlTargets-relwithdebinfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Sql\Qt6SqlTargets.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Sql\Qt6SqlVersionlessAliasTargets.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsAdditionalTargetInfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsConfig.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsConfigVersion.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsConfigVersionImpl.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsDependencies.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsTargets-debug.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsTargets-relwithdebinfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsTargets.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsVersionlessTargets.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6QModernWindowsStylePluginAdditionalTargetInfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6QModernWindowsStylePluginConfig.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6QModernWindowsStylePluginTargets-debug.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6QModernWindowsStylePluginTargets-relwithdebinfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6QModernWindowsStylePluginTargets.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsAdditionalTargetInfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsConfig.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsConfigVersion.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsConfigVersionImpl.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsDependencies.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsMacros.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsPlugins.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsTargets-debug.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsTargets-relwithdebinfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsTargets.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Widgets\Qt6WidgetsVersionlessAliasTargets.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Xml\Qt6XmlAdditionalTargetInfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Xml\Qt6XmlConfig.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Xml\Qt6XmlConfigVersion.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Xml\Qt6XmlConfigVersionImpl.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Xml\Qt6XmlDependencies.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Xml\Qt6XmlTargets-debug.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Xml\Qt6XmlTargets-relwithdebinfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Xml\Qt6XmlTargets.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6Xml\Qt6XmlVersionlessAliasTargets.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateAdditionalTargetInfo.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateConfig.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateConfigVersion.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateConfigVersionImpl.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateTargets.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateVersionlessAliasTargets.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6\FindWrapAtomic.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6\FindWrapVulkanHeaders.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6\Qt6Config.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6\Qt6ConfigExtras.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6\Qt6ConfigVersion.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6\Qt6ConfigVersionImpl.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6\Qt6Dependencies.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6\Qt6Targets.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6\Qt6VersionlessAliasTargets.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6\QtFeature.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6\QtFeatureCommon.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6\QtInstallPaths.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6\QtPublicAndroidHelpers.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6\QtPublicAppleHelpers.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6\QtPublicCMakeEarlyPolicyHelpers.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6\QtPublicCMakeHelpers.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6\QtPublicCMakeVersionHelpers.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6\QtPublicDependencyHelpers.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6\QtPublicExternalProjectHelpers.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6\QtPublicFinalizerHelpers.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6\QtPublicFindPackageHelpers.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6\QtPublicGitHelpers.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6\QtPublicPluginHelpers.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6\QtPublicPluginHelpers_v2.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6\QtPublicSbomAttributionHelpers.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6\QtPublicSbomCpeHelpers.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6\QtPublicSbomDepHelpers.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6\QtPublicSbomFileHelpers.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6\QtPublicSbomGenerationHelpers.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6\QtPublicSbomHelpers.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6\QtPublicSbomLicenseHelpers.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6\QtPublicSbomOpsHelpers.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6\QtPublicSbomPurlHelpers.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6\QtPublicSbomPythonHelpers.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6\QtPublicSbomQtEntityHelpers.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6\QtPublicSbomSystemDepHelpers.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6\QtPublicTargetHelpers.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6\QtPublicTestHelpers.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6\QtPublicToolHelpers.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6\QtPublicWalkLibsHelpers.cmake E$:\Qt\6.10.0\msvc2022_64\lib\cmake\Qt6\QtPublicWindowsHelpers.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeCXXCompiler.cmake.in E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeCXXCompilerABI.cpp E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeCXXInformation.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeCheckCompilerFlagCommonPatterns.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeCommonLanguageInclude.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeCompilerIdDetection.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeDetermineCXXCompiler.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeDetermineCompiler.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeDetermineCompilerABI.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeDetermineCompilerId.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeDetermineCompilerSupport.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeDetermineRCCompiler.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeDetermineSystem.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeFindBinUtils.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeFindDependencyMacro.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeGenericSystem.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeInitializeConfigs.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeLanguageInformation.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeNinjaFindMake.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeParseImplicitIncludeInfo.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeParseImplicitLinkInfo.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeParseLibraryArchitecture.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeRCCompiler.cmake.in E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeRCInformation.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeSystem.cmake.in E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeSystemSpecificInformation.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeSystemSpecificInitialize.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeTestCXXCompiler.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeTestCompilerCommon.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeTestRCCompiler.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CheckCXXCompilerFlag.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CheckCXXSourceCompiles.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CheckIncludeFileCXX.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CheckLibraryExists.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\ADSP-DetermineCompiler.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\ARMCC-DetermineCompiler.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\ARMClang-DetermineCompiler.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\AppleClang-DetermineCompiler.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\Borland-DetermineCompiler.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\CMakeCommonCompilerMacros.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\Clang-DetermineCompiler.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\Clang-DetermineCompilerInternal.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\Compaq-CXX-DetermineCompiler.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\Cray-DetermineCompiler.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\CrayClang-DetermineCompiler.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\Embarcadero-DetermineCompiler.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\Fujitsu-DetermineCompiler.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\FujitsuClang-DetermineCompiler.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\GHS-DetermineCompiler.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\GNU-CXX-DetermineCompiler.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\HP-CXX-DetermineCompiler.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\IAR-DetermineCompiler.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\IBMCPP-CXX-DetermineVersionInternal.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\IBMClang-CXX-DetermineCompiler.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\Intel-DetermineCompiler.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\IntelLLVM-DetermineCompiler.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\LCC-CXX-DetermineCompiler.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\MSVC-CXX-CXXImportStd.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\MSVC-CXX.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\MSVC-DetermineCompiler.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\MSVC.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\NVHPC-DetermineCompiler.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\NVIDIA-DetermineCompiler.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\OpenWatcom-DetermineCompiler.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\OrangeC-DetermineCompiler.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\PGI-DetermineCompiler.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\PathScale-DetermineCompiler.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\SCO-DetermineCompiler.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\SunPro-CXX-DetermineCompiler.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\TI-DetermineCompiler.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\TIClang-DetermineCompiler.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\Tasking-DetermineCompiler.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\VisualAge-CXX-DetermineCompiler.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\Watcom-DetermineCompiler.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\XL-CXX-DetermineCompiler.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\XLClang-CXX-DetermineCompiler.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Compiler\zOS-CXX-DetermineCompiler.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\FindPackageHandleStandardArgs.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\FindPackageMessage.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\FindThreads.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\FindVulkan.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\GNUInstallDirs.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Internal\CMakeDetermineLinkerId.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Internal\CheckCompilerFlag.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Internal\CheckFlagCommonConfig.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Internal\CheckSourceCompiles.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Internal\FeatureTesting.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Platform\Windows-Determine-CXX.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Platform\Windows-Initialize.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Platform\Windows-MSVC-CXX.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Platform\Windows-MSVC.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Platform\Windows.cmake E$:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\Platform\WindowsPaths.cmake: phony


#############################################
# Clean additional files.

build CMakeFiles\clean.additional: CLEAN_ADDITIONAL
  CONFIG = Release


#############################################
# Clean all the built files.

build clean: CLEAN CMakeFiles\clean.additional


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all
