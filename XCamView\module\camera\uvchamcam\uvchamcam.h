﻿#ifndef UVCHAMCAM_H
#define UVCHAMCAM_H

#include "../camera.h"
#include "uvchamloader.h"
#include <QMutex>
#include <QTimer>

class UvchamCam : public Camera
{
    Q_OBJECT

public:
    explicit UvchamCam(QObject* parent = nullptr);
    ~UvchamCam();

    static bool InitLoader();
    static QList<CameraSt> Enum();

    bool Open(const CameraSt& info) override;
    void Close() override;
    void Stop() override;
    HRESULT Start() override;
    bool IsOpened() override;
    bool IsRunning() override;

    HRESULT PutPara(unsigned nId, int val) override;
    HRESULT GetPara(unsigned nId, int* pVal) override;
    HRESULT GetParaRange(unsigned nId, int* pMin, int* pMax, int* pDef) override;
    void ChangeResolution(int res) override;

    int  GetImageWidth() override;
    int  GetImageHeight() override;
    void GetImage(void* pFrameBuffer) override;

private slots:
    void OnTimerOut();

private:
    static void __stdcall UVCHAM_CALLBACK(unsigned nEvent, void* pCallbackCtx);

    void PullImagePrivate();

    QMutex        m_mutex;
    QTimer        m_timer;
    HUvcham       m_hUvcham;
    bool          m_bOpened;
    bool          m_bRunning;
    uchar*        m_pData;
    int           m_width;
    int           m_height;
    int           m_preExpTime;
    int           m_preExpGain;
    int           m_preWBRed;
    int           m_preWBGreen;
    int           m_preWBBlue;
};

#endif
