# This file is managed by Qt Creator, do not edit!

set("CMAKE_COLOR_DIAGNOSTICS" "ON" CACHE "BOOL" "" FORCE)
set("CMAKE_BUILD_TYPE" "Release" CACHE "STRING" "" FORCE)
set("CMAKE_C_COMPILER" "D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/HostX64/x64/cl.exe" CACHE "FILEPATH" "" FORCE)
set("QT_QMAKE_EXECUTABLE" "E:/Qt/6.10.0/msvc2022_64/bin/qmake.exe" CACHE "FILEPATH" "" FORCE)
set("QT_MAINTENANCE_TOOL" "E:/Qt/MaintenanceTool.exe" CACHE "FILEPATH" "" FORCE)
set("CMAKE_PREFIX_PATH" "E:/Qt/6.10.0/msvc2022_64" CACHE "PATH" "" FORCE)
set("CMAKE_CXX_FLAGS_INIT" "" CACHE "STRING" "" FORCE)
set("CMAKE_CXX_COMPILER" "D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/HostX64/x64/cl.exe" CACHE "FILEPATH" "" FORCE)
set("CMAKE_GENERATOR" "Ninja" CACHE "STRING" "" FORCE)
set("CMAKE_PROJECT_INCLUDE_BEFORE" "D:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/.qtc/package-manager/auto-setup.cmake" CACHE "FILEPATH" "" FORCE)