#ifndef TPSHAPE_H
#define TPSHAPE_H

#include <QFont>
#include <QRegion>
#include <QString>
#include <QMap>
#include "tpmeasuredef.h"

class TPMEASUREMANAGER_API TpShape
{
    const eShapeType       m_type;
    QFont                  m_sysFt;
    QList<QPointF>         m_orgPts;
    QPointF                m_labelDiffPt;
    QRegion                m_shapeRegion;
    QString                m_strName;
    ePenStyle              m_penStyle;
    bool                   m_bNeedRecalc;
    bool                   m_bVisible;
    bool                   m_bLock;
    bool                   m_bHoldFocus;
    int                    m_penWidth;
    double                 m_scale;
    double                 m_resolution;
    eUnit                  m_unit;
    QMap<QString, QString> m_langMap;
protected:
    static TpShape*     localCtx;
    static double       m_fW;
    static double       m_fH;
    static QPointF      m_offset;
    QList<QPointF>      m_pts;
    QList<QPointF>      m_distPts;
    QList<QPointF>      m_extraPts;
    QList<QRectF>       m_resizeHandles;
    ShapeLabel          m_shapeLabel;
    ShapeLabel          m_showLabel;
    QColor              m_penColor;
public:
    TpShape(eShapeType type);
    TpShape(const TpShape* src);
    virtual ~TpShape();
    virtual bool isMoved(const QPointF& pt);
    virtual bool isResized(const QPointF& pt) const;
    virtual void moveDiff(const QPointF& diff);
    virtual void setPt(int n, const QPointF& pt);
    virtual QRectF getDrawRect() const;
    virtual QList<QRectF> getResizeRects();
    virtual QRectF getLabelUpdateRect() const { return QRectF(); }
    virtual QPointF getLabelRoot() const { return QPointF(); }
    virtual QPointF getDistPt(int index) const;
    virtual QRegion getUpdateRegion(int expand = 5) const = 0;
    virtual double getLength() const { return -1; }
    virtual double getWidth() const { return -1; }
    virtual QPointF getStartPt() const { return QPointF(); }
    virtual QPointF getEndPt() const { return QPointF(); }
    virtual QPointF getCenterPt() const;
    virtual QString getText() const { return QString(); }
    virtual void setText(const QString&) {}
    void setScaleAndOffset(const double& scale, const QPointF& offset);
    void getInfo(const int& index, ShapeItem* info);
    void popBackPt();
    QPointF getPt(int n) const;
    QList<QPointF> getPts() const { return m_pts; }
    void backupPts() { m_orgPts = m_pts; }
    QList<QPointF> getOrgPts() const { return m_orgPts; }
    int getPtCount() const { return m_pts.count(); }
    bool isLabelMoved(const QPointF& pt) const;
    void moveLabelDiffPt(const QPointF& diff) { m_labelDiffPt += diff; }
    void setLabelDiffPt(const QPointF& pt) { m_labelDiffPt = pt; }
    QPointF getLabelDiffPt() const { return m_labelDiffPt; }
    eShapeType getType() const { return m_type; }
    QString getName() const { return m_strName; }
    void setName(const QString& name) { m_strName = name; }
    bool getVisible() const { return m_bVisible;}
    void setVisible(bool visible) { m_bVisible = visible;}
    bool getLock() const {return m_bLock;}
    void setLock(bool status) { m_bLock = status;}
    QColor getPenColor() const { return m_penColor; }
    void setPenColor(const QColor& color) { m_penColor = color;}
    int getPenWidth() const { return m_penWidth; }
    void setPenWidth(int penWidth) { m_penWidth = penWidth; }
    ePenStyle getPenStyle() const { return m_penStyle; }
    void setPenStyle(ePenStyle style) { m_penStyle = style; }
    int getResizeHandleIndex(const QPointF& pt) const;
    void vFlip();
    void hFlip();
    void setShowLabel(ShapeLabel showLabel) { m_showLabel.showLabel = showLabel.showLabel; }
    ShapeLabel getShowLabel() const { return m_showLabel; }
    void paintShape(QPainter& painter, bool bToImage = false);
    void setRecalcEnabled(bool enable) { if (canModify()) m_bNeedRecalc = enable; }
    void clearFocus() { m_bHoldFocus = false; }
    void holdFocus() { m_bHoldFocus = true; }
    bool hasFocus() const { return m_bHoldFocus; }
    void adjustLabel();
    bool canModify() const { return hasFocus() && !getLock() && getVisible(); }
    bool canProfile() const { return (m_type == S_ArbLine || m_type == S_HLine || m_type == S_VLine || m_type == S_Arrow); }
    void setScale(double scale) { m_scale = scale; }
    void setLangMap(const QMap<QString, QString>& langMap) { m_langMap = langMap; }
    void setResolution(double resolution) { m_resolution = resolution; }
    double getResolution() const { return m_resolution; }
    void setUnit(eUnit unit) { m_unit = unit; }
    eUnit getUnit() const { return m_unit; }
    static double realLengthToPixel(double realLength);
    static void setOffset(const QPointF& offset) { m_offset = offset; }
    static QPointF getOffset() {return m_offset;}
    static void initSize(double w, double h){ m_fW = w; m_fH = h; }
    static void setSysFont(QFont ft) { localCtx->m_sysFt = ft; }
    static double maxW() { return m_fW; }
    static double maxH() { return m_fH; }
protected:
    virtual void updateExternShape(){}
    virtual void updateDistPts() {}
    virtual double getAngle() const { return -1; }
    virtual double getLongAxis() const { return -1; }
    virtual double getShortAxis() const { return -1; }
    virtual double getDiameter() const { return -1; }
    virtual double getDiameter2() const { return -1; }
    virtual double getPerimeter() const { return -1; }
    virtual double getArea() const { return -1; }
    virtual double getLength2() const { return -1; }
    virtual double getDistance() const { return -1; }
    virtual void paintShapePrivate(QPainter& /*painter*/) const = 0;
    virtual QPointF getPosition() const { return QPointF(); }
    QRectF getLabelRect(QString text, int para = 0) const;
    QString getLabelText(eLabel type, int id = 0) const;
    QRegion lineRegion(const QPointF& pt1, const QPointF& pt2, int expand = 5) const;
    QRectF handleRect(const QPointF& pt) const;
    QPointF getExternPt(int n) const;
    QColor getInfoColor() const { return QColor(Qt::red); }
    QPen getPen() const;
private:
    void paintFocusRect(QPainter& painter);
    QPointF adjustDiff(const QPointF& diff, const QRectF& rect);
    QRectF getInsets(const QList<QPointF>& pts) const;
    QString getLabelName(eLabel type) const;
    QString getLabelValue(eLabel type, int id) const;
    QString appendUnit(QString str) const;
    double pixel2UnitRatio() const;
    void vFlip(QPointF& pt);
    void hFlip(QPointF& pt);
};

#endif
