QT       += core gui widgets xml network
QT       += sql

greaterThan(QT_MAJOR_VERSION, 4): QT += widgets

CONFIG += c++17
DEFINES += QT_DEPRECATED_WARNINGS
INCLUDEPATH += $$PWD/lib/include

win32 {
    QMAKE_CXXFLAGS += /utf-8
}
win32 {
    contains(QT_ARCH, x86_64) || contains(QT_ARCH, amd64) {
        CONFIG(debug, debug|release){
            LIBS += $$PWD/lib/x64/TpMeasureManagerd.lib
        } else {
            LIBS += $$PWD/lib/x64/TpMeasureManager.lib
        }
    } else {
        CONFIG(debug, debug|release){
            LIBS += $$PWD/lib/x86/TpMeasureManagerd.lib
        } else {
            LIBS += $$PWD/lib/x86/TpMeasureManager.lib
        }
    }
}

SOURCES += \
    main.cpp \
    global.cpp \
    mainwindow.cpp \
    camera/cameramanager.cpp \
    camera/camera.cpp \
    camera/uvchamcam/uvchamcam.cpp \
    camera/uvchamcam/uvchamloader.cpp \
    panel/chatpanel/filereceiver.cpp \
    panel/chatpanel/filesender.cpp \
    panel/collapsewidget.cpp \
    panel/leftpanel.cpp \
    panel/camerapanel/camerapanel.cpp \
    panel/camerapanel/cameralistwidget.cpp \
    panel/camerapanel/resolutionwidget.cpp \
    panel/camerapanel/exposuregainwidget.cpp \
    panel/camerapanel/whitebalancewidget.cpp \
    panel/camerapanel/coloradjustwidget.cpp \
    panel/camerapanel/powerfrequencywidget.cpp \
    panel/login/tpdatabasehandler.cpp \
    panel/monitorpanel/monitorwidget.cpp \
    panel/monitorpanel/monitordialog.cpp \
    panel/chatpanel/chatwidget.cpp \
    panel/centralpanel/tptabwidget.cpp \
    panel/centralpanel/view.cpp \
    panel/centralpanel/displayview/displayview.cpp \
    panel/centralpanel/displayview/previewview.cpp \
    panel/centralpanel/displayview/imageview.cpp \
    panel/centralpanel/monitorview/monitorview.cpp \
    panel/measure/tpcalibrationdlg.cpp \
    panel/measure/tptexteditdlg.cpp \
    panel/login/logindialog.cpp \
    util/i18n/i18n.cpp \
    util/TpSaveLoader/tpsaveloader.cpp \
    util/auxrect/auxrect.cpp \
    util/tutils/tutils.cpp

HEADERS += \
    global.h \
    mainwindow.h \
    camera/cameramanager.h \
    camera/cameradef.h \
    camera/camera.h \
    camera/uvchamcam/uvchamcam.h \
    camera/uvchamcam/uvchamloader.h \
    panel/chatpanel/filereceiver.h \
    panel/chatpanel/filesender.h \
    panel/collapsewidget.h \
    panel/leftpanel.h \
    panel/camerapanel/camerapanel.h \
    panel/camerapanel/cameralistwidget.h \
    panel/camerapanel/resolutionwidget.h \
    panel/camerapanel/exposuregainwidget.h \
    panel/camerapanel/whitebalancewidget.h \
    panel/camerapanel/coloradjustwidget.h \
    panel/camerapanel/powerfrequencywidget.h \
    panel/login/tpdatabasehandler.h \
    panel/monitorpanel/monitorwidget.h \
    panel/monitorpanel/monitordialog.h \
    panel/chatpanel/chatwidget.h \
    panel/centralpanel/tptabwidget.h \
    panel/centralpanel/view.h \
    panel/centralpanel/displayview/displayview.h \
    panel/centralpanel/displayview/previewview.h \
    panel/centralpanel/displayview/imageview.h \
    panel/centralpanel/monitorview/monitorview.h \
    panel/measure/tpcalibrationdlg.h \
    panel/measure/tptexteditdlg.h \
    panel/login/logindialog.h \
    util/i18n/i18n.h \
    util/TpSaveLoader/tpsaveloader.h \
    util/auxrect/auxrect.h \
    util/tutils/tutils.h

PRECOMPILED_HEADER = predef.h
RESOURCES += XCamView.qrc
