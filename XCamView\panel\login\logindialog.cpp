#include "logindialog.h"
#include <QHBoxLayout>
#include <QGridLayout>

LoginDialog::LoginDialog(QWidget *parent)
    : QDialog(parent)
{
    setWindowTitle(g_i18n->value("ids_login"));
    setWindowFlags(windowFlags() & ~Qt::WindowContextHelpButtonHint);

    m_pNameLabel = new QLabel(g_i18n->value("ids_name") + g_i18n->value("ids_colon"));
    m_pNameLineEdit = new QLineEdit();
    m_pPasswordLabel = new QLabel(g_i18n->value("ids_password") + g_i18n->value("ids_colon"));
    m_pPasswordLineEdit = new QLineEdit();
    m_pPasswordLineEdit->setEchoMode(QLineEdit::Password);
    m_pOKBtn = new QPushButton(g_i18n->value("ids_ok"));
    m_pCancelBtn = new QPushButton(g_i18n->value("ids_cancel"));

    QHBoxLayout* btnLyt = new QHBoxLayout();
    btnLyt->addStretch(1);
    btnLyt->addWidget(m_pOKBtn);
    btnLyt->addWidget(m_pCancelBtn);
    btnLyt->addStretch(1);

    QGridLayout* mainLyt = new QGridLayout();
    mainLyt->addWidget(m_pNameLabel, 0, 0);
    mainLyt->addWidget(m_pNameLineEdit, 0, 1);
    mainLyt->addWidget(m_pPasswordLabel, 1, 0);
    mainLyt->addWidget(m_pPasswordLineEdit, 1, 1);
    mainLyt->addLayout(btnLyt, 2, 0, 1, 2);
    mainLyt->setColumnStretch(1, 1);

    setLayout(mainLyt);

    connect(m_pOKBtn, SIGNAL(clicked()), this, SLOT(accept()));
    connect(m_pCancelBtn, SIGNAL(clicked()), this, SLOT(reject()));
}
