#include "imageview.h"
#include "measure/tpmeasuremanager.h"
#include <QPainter>
#include <QPaintEvent>
#include <QWheelEvent>
#include <QHBoxLayout>

ImageWidget::ImageWidget(QWidget* parent)
    : DisplayWidget(parent)
{
    const int width = g_pCameraManager->GetImageWidth();
    const int height = g_pCameraManager->GetImageHeight();
    if(m_pImage && (m_pImage->width() != width || m_pImage->height() != height))
    {
        delete m_pImage;
        m_pImage = nullptr;
    }
    if(nullptr == m_pImage)
        m_pImage = new QImage(width, height, QImage::Format_RGB888);
    m_ptCenter = m_pImage->rect().center();
    m_pMeasureManager->setMSize(width, height);
    if(m_pImage)
        g_pCameraManager->GetImage(m_pImage->bits());
    DoZoomChanged();
}

int ImageView::s_cnt = 0;

ImageView::ImageView(QWidget *parent)
    : DisplayView(new ImageWidget(), parent)
{
    ++s_cnt;
}

ImageView::~ImageView()
{
    --s_cnt;
}
