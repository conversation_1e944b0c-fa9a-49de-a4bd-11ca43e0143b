#ifndef TPMEASUREDEF_H
#define TPMEASUREDEF_H

#include <QString>
#include <QPointF>
#include <QColor>

#if defined(TPMEASUREMANAGER_LIBRARY)
#  define TPMEASUREMANAGER_API Q_DECL_EXPORT
#else
#  define TPMEASUREMANAGER_API Q_DECL_IMPORT
#endif

#ifndef MINDVALUE
#define MINDVALUE 0.000001
#endif

#ifndef INVALIDVALUE
#define INVALIDVALUE -1
#endif

#ifndef PI
#define PI 3.1415926
#endif

#ifndef PRECISION
#define PRECISION 2
#endif

#define CROSSLENGTH   12
#define DFT_FONTSIZE  20

enum eMMode {
    M_Normal,
    M_Selected,
    M_Rectangle,
    M_Ellipse,
    M_Polygon,
    M_Curve,
    M_HLine,
    M_VLine,
    M_ArbLine,
    M_<PERSON>,
    M_<PERSON>,
    M_<PERSON><PERSON>,
    M_4Pt<PERSON><PERSON>,
    <PERSON>_<PERSON><PERSON><PERSON>,
    M_<PERSON><PERSON><PERSON><PERSON><PERSON>,
    M_<PERSON>,
    M_2PtCircle,
    M_3Pt<PERSON>ir<PERSON>,
    M_Ann<PERSON>,
    M_TwoCircles,
    M_3PtTwoCircles,
    M_Arc,
    M_Text,
    M_CrossHLine,
    M_CrossVLine,
    M_Vertical,
    M_4PtVertical
};

enum eMMProcedure {
    MMProcedure_START,
    MMProcedure_DRAWING,
    MMProcedure_END
};

enum eEndPtType
{
    EPT_NULL = 0,
    EPT_RECTANGLE
};

enum eShapeType {
    S_None = 0,
    S_Rectangle = 0,
    S_Ellipse,
    S_Polygon ,
    S_HLine,
    S_VLine,
    S_ArbLine,
    S_Arrow,
    S_Point,
    S_Angle,
    S_FourPtAngle,
    S_Parallel,
    S_TwoParallels,
    S_Vertical,
    S_FourPtVertical,
    S_Circle,
    S_TwoPtCircle,
    S_ThreePtCircle,
    S_Curve,
    S_Annulus,
    S_TwoCircles,
    S_ThreePtTwoCircles,
    S_Arc,
    S_Text,
    S_ScaleBar,
    S_Calibration,
    S_CrossHLine,
    S_CrossVLine
};

enum ePenStyle {
    PS_Solid,
    PS_Dash,
    PS_Dot,
    PS_DashDot,
    PS_DashDotDot
};

typedef union tagShapeLabel{
    qint32 showLabel;
    struct {
        qint32 bPerimeter   : 1;
        qint32 bArea        : 1;
        qint32 bAxis        : 1;
        qint32 bLength      : 1;
        qint32 bPositioin   : 1;
        qint32 bAngle       : 1;
        qint32 bDistance    : 1;
        qint32 bDiameter    : 1;
        qint32 bWidth       : 1;
        qint32 reservation  : 22;
    };
}ShapeLabel;

typedef struct tagShapeProperty{
    int         index;
    eShapeType  type;
    QString     name;
    bool        visible;
    bool        lock;
    int         penWidth;
    ePenStyle   penStyle;
    QColor      penColor;
}ShapeProperty;

typedef struct tagShapeItem {
    ShapeProperty   property;
    ShapeLabel      shapeLabel;
    ShapeLabel      showLabel;
    QString         text;
    QPointF         center;
    QPointF         start;
    QPointF         end;
    QString         length;
    double          angle;
    double          diameter;
    double          perimeter;
    double          area;
    double          distance;
    double          width;

    bool operator == (const tagShapeItem& rhs)
    {
        return (rhs.property.visible == property.visible) && (rhs.property.lock == property.lock)
                && (rhs.property.type == property.type) && (rhs.property.name == property.name)
                && (rhs.property.penColor == property.penColor) && (rhs.property.penWidth == property.penWidth) && (rhs.property.penStyle == property.penStyle)
                && (rhs.shapeLabel.showLabel == shapeLabel.showLabel) && (rhs.showLabel.showLabel == showLabel.showLabel)
                && (rhs.text == text) && (rhs.center == center) && (rhs.start == start) && (rhs.end == end) && (rhs.length == length)
                && (fabs(rhs.angle - angle) < MINDVALUE) && (fabs(rhs.diameter - diameter) < MINDVALUE) && (fabs(rhs.perimeter - perimeter) < MINDVALUE) && (fabs(rhs.area - area) < MINDVALUE)
                && (fabs(rhs.distance - distance) < MINDVALUE) && (fabs(rhs.width - width) < MINDVALUE);
    }
}ShapeItem;

enum eLabel{
    LT_perimeter,
    LT_area,
    LT_longAxis,
    LT_shortAxis,
    LT_length,
    LT_position,
    LT_angle,
    LT_distance,
    LT_diameter,
    LT_width
};

enum eUnit
{
    UNIT_PX,
    UNIT_NM,
    UNIT_UM,
    UNIT_MM,
    UNIT_CM,
    UNIT_M,
    UNIT_IN
};

enum eDirection {
    eD_None,
    eD_Left,
    eD_Right,
    eD_Up,
    eD_Down
};

enum eMMsg {
    MMsg_None,
    MMsg_Update,
    MMsg_SetCursor,
    MMsg_Select,
    MMsg_Create,
    MMsg_Delete,
    MMsg_Chosen,
    MMsg_CChosen,
    MMsg_SChosen
};

enum eRectPtType
{
    RPT_X0Y0,
    RPT_X0Ym,
    RPT_X0Y1,
    RPT_XmY0,
    RPT_XmY1,
    RPT_X1Y0,
    RPT_X1Ym,
    RPT_X1Y1
};

enum eMMCursor {
    MMCursor_None,
    MMCursor_Custom,
    MMCursor_System,
    MMCursor_Select,
    MMCursor_Drag,
    MMCursor_Move
};

#define KEYPRESS_NONE  0x0000
#define KEYPRESS_CTRL  0x0001
#define KEYPRESS_SHIFT 0x0002
extern int g_iMulti;

#endif
