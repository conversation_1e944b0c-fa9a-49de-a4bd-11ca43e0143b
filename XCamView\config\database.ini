# TpDatabase 配置文件
# 基于Qt5Demo的数据库配置扩展

[Database]
# 数据库服务器地址（从Qt5Demo迁移）
host=*************

# 数据库端口
port=3306

# 数据库名称（从Qt5Demo迁移）
database=student

# 用户名（从Qt5Demo迁移）
username=liu

# 密码（从Qt5Demo迁移）
password=SecurePass123!

# 数据库驱动
driver=QMYSQL

# 连接选项（可选）
options=

[ConnectionPool]
# 最小连接数
minConnections=1

# 最大连接数
maxConnections=5

# 连接超时时间（毫秒）
connectionTimeout=30000

# 空闲超时时间（毫秒，5分钟）
idleTimeout=300000

# 健康检查间隔（毫秒，1分钟）
healthCheckInterval=60000

# 是否启用健康检查
enableHealthCheck=true

[Performance]
# 是否启用查询缓存
enableQueryCache=false

# 查询缓存大小
queryCacheSize=50

# 查询缓存超时（毫秒，5分钟）
queryCacheTimeout=300000

# 是否启用慢查询日志
enableSlowQueryLog=true

# 慢查询阈值（毫秒）
slowQueryThreshold=1000

# 是否启用批量操作优化
enableBatchOptimization=true

# 批量操作大小
batchSize=100

[Security]
# 是否启用SQL注入防护
enableSqlInjectionProtection=true

# 是否启用输入验证
enableInputValidation=true

# 是否启用敏感数据加密
enableSensitiveDataEncryption=false

# 加密密钥（如果启用加密）
encryptionKey=

# 是否启用访问控制
enableAccessControl=false
