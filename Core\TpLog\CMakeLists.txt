cmake_minimum_required(VERSION 3.16)

project(TpLog LANGUAGES CXX)

set(CMAKE_AUTOUIC ON)
set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTORCC ON)
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

find_package(QT NAMES Qt6 Qt5 REQUIRED COMPONENTS Core)
find_package(Qt${QT_VERSION_MAJOR} REQUIRED COMPONENTS Core)

add_library(TpLog SHARED
  tplog.cpp
  tplog.h
)

set_target_properties(TpLog PROPERTIES DEBUG_POSTFIX "d")

target_link_libraries(TpLog PRIVATE Qt${QT_VERSION_MAJOR}::Core)

target_compile_definitions(TpLog PRIVATE TPLOG_LIBRARY)
