﻿#ifndef CAMERAPANEL_H
#define CAMERAPANEL_H

#include <QScrollArea>

class CameraListWidget;
class ExposureGainWidget;
class ResolutionWidget;
class WhiteBalanceWidget;
class ColorAdjustWidget;
class PowerFrequencyWidget;

class CameraPanel : public QScrollArea
{
    Q_OBJECT
public:
    explicit CameraPanel(QWidget* parent = nullptr);

    void Notify(int notify);

private:
    CameraListWidget* m_pCameraListWidget;
    ResolutionWidget* m_pResolutionWidget;
    ExposureGainWidget* m_pExposureGainWidget;
    WhiteBalanceWidget* m_pWhiteBalanceWidget;
    ColorAdjustWidget* m_pColorAdjustWidget;
    PowerFrequencyWidget* m_pPowerFrequencyWidget;
};

#endif
