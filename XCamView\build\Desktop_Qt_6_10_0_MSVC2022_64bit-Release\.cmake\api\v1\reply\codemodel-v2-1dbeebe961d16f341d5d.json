{"configurations": [{"directories": [{"build": ".", "hasInstallRule": true, "jsonFile": "directory-.-Release-f3272430f48b1a850d82.json", "minimumCMakeVersion": {"string": "3.16"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2]}], "name": "Release", "projects": [{"directoryIndexes": [0], "name": "XCamView", "targetIndexes": [0, 1, 2]}], "targets": [{"directoryIndex": 0, "id": "XCamView::@6890427a1f51a3e7e1df", "jsonFile": "target-XCamView-Release-8f4e12ab2d5825149de6.json", "name": "XCamView", "projectIndex": 0}, {"directoryIndex": 0, "id": "XCamView_autogen::@6890427a1f51a3e7e1df", "jsonFile": "target-XCamView_autogen-Release-084c344b7292cece5a31.json", "name": "XCamView_autogen", "projectIndex": 0}, {"directoryIndex": 0, "id": "XCamView_autogen_timestamp_deps::@6890427a1f51a3e7e1df", "jsonFile": "target-XCamView_autogen_timestamp_deps-Release-dc38b38796601391d155.json", "name": "XCamView_autogen_timestamp_deps", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "D:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release", "source": "D:/work/XCamView/XCamView"}, "version": {"major": 2, "minor": 7}}