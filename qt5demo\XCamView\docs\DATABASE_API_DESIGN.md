XCamView 数据库API设计文档

## 🎯 **设计原则**
- **配置文件优先**：系统配置、相机配置使用INI文件
- **数据库专注核心**：用户管理、消息记录、文件信息
- **文件分离存储**：数据库存元信息，文件系统存实体

## 🗄️ **核心数据表**

```sql
-- 1. 用户表
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    user_type ENUM('teacher', 'student') DEFAULT 'student',
    real_name VARCHAR(100),
    status ENUM('active', 'inactive') DEFAULT 'active',
    last_login TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 2. 用户会话表
CREATE TABLE user_sessions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    session_token VARCHAR(255) UNIQUE NOT NULL,
    ip_address VARCHAR(45),
    login_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 3. 聊天室表
CREATE TABLE chat_rooms (
    id INT PRIMARY KEY AUTO_INCREMENT,
    room_name VARCHAR(100) NOT NULL,
    room_type ENUM('public', 'private') DEFAULT 'public',
    created_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- 4. 聊天消息表
CREATE TABLE chat_messages (
    id INT PRIMARY KEY AUTO_INCREMENT,
    room_id INT NOT NULL,
    sender_id INT NOT NULL,
    message_type ENUM('text', 'file', 'system') DEFAULT 'text',
    content TEXT,
    file_id INT NULL,
    ip_address VARCHAR(45),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (room_id) REFERENCES chat_rooms(id) ON DELETE CASCADE,
    FOREIGN KEY (sender_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 5. 共享文件表（类似QQ群文件）
CREATE TABLE shared_files (
    id INT PRIMARY KEY AUTO_INCREMENT,
    uploader_id INT NOT NULL,
    room_id INT NULL,
    original_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size BIGINT NOT NULL,
    file_hash VARCHAR(64),
    download_count INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (uploader_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (room_id) REFERENCES chat_rooms(id) ON DELETE CASCADE
);

-- 6. 操作日志表
CREATE TABLE operation_logs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT,
    operation_type VARCHAR(50) NOT NULL,
    operation_desc TEXT,
    ip_address VARCHAR(45),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
);
```

## 🔧 **核心API类设计**

### **1. UserManager - 用户管理**
```cpp
class UserManager : public QObject
{
public:
    struct LoginResult {
        bool success;
        int userId;
        QString userType;
        QString sessionToken;
        QString errorMessage;
    };

    // 核心方法
    LoginResult authenticateUser(const QString& username, const QString& password, const QString& ipAddress);
    bool logoutUser(const QString& sessionToken);
    int createUser(const QString& username, const QString& password, const QString& userType);
    bool updateUser(int userId, const QString& realName, const QString& status);
    bool deleteUser(int userId);
};
```

### **2. MessageManager - 消息管理**
```cpp
class MessageManager : public QObject
{
public:
    struct ChatMessage {
        int id;
        int roomId;
        int senderId;
        QString messageType;
        QString content;
        int fileId;
        QDateTime createdAt;
    };

    // 核心方法
    int sendMessage(int roomId, int senderId, const QString& content, const QString& messageType = "text");
    QList<ChatMessage> getRoomMessages(int roomId, int limit = 50);
    int createRoom(const QString& roomName, int createdBy);
    bool deleteMessage(int messageId, int userId);
};
```

### **3. SharedFileManager - 文件管理**
```cpp
class SharedFileManager : public QObject
{
public:
    struct FileInfo {
        int id;
        QString originalName;
        QString filePath;
        qint64 fileSize;
        int downloadCount;
        QDateTime createdAt;
    };

    // 核心方法
    int uploadFile(int uploaderId, int roomId, const QString& localFilePath);
    FileInfo getFileInfo(int fileId);
    QList<FileInfo> getRoomFiles(int roomId);
    bool recordDownload(int fileId, int downloaderId);
    bool deleteFile(int fileId, int userId);
};
```

## � **使用示例**

### **1. 用户登录**
```cpp
UserManager userManager(&dbHandler);
UserManager::LoginResult result = userManager.authenticateUser(username, password, clientIP);

if (result.success) {
    g_currentUserId = result.userId;
    g_sessionToken = result.sessionToken;
    accept();
} else {
    QMessageBox::critical(this, "登录失败", result.errorMessage);
}
```

### **2. 发送消息**
```cpp
MessageManager messageManager(&dbHandler);
int messageId = messageManager.sendMessage(roomId, g_currentUserId, messageText);
if (messageId > 0) {
    updateChatDisplay();
}
```

### **3. 上传文件**
```cpp
SharedFileManager fileManager(&dbHandler);
QString filePath = QFileDialog::getOpenFileName(this, "选择文件");
if (!filePath.isEmpty()) {
    int fileId = fileManager.uploadFile(g_currentUserId, roomId, filePath);
    if (fileId > 0) {
        // 发送文件消息
        messageManager.sendMessage(roomId, g_currentUserId,
                                 QString("分享了文件: %1").arg(QFileInfo(filePath).fileName()),
                                 "file");
    }
}
```

### **4. 下载文件**
```cpp
void onDownloadFile(int fileId) {
    SharedFileManager fileManager(&dbHandler);
    SharedFileManager::FileInfo fileInfo = fileManager.getFileInfo(fileId);

    QString savePath = QFileDialog::getSaveFileName(this, "保存文件", fileInfo.originalName);
    if (!savePath.isEmpty()) {
        QString sourceFile = QApplication::applicationDirPath() + "/files/" + fileInfo.filePath;
        if (QFile::copy(sourceFile, savePath)) {
            fileManager.recordDownload(fileId, g_currentUserId);
            QMessageBox::information(this, "成功", "文件下载完成！");
        }
    }
}
```

## 🚀 **实现建议**

### **文件存储结构**
```
应用程序目录/
├── files/
│   ├── rooms/
│   │   ├── room_1/2024/11/file1.jpg
│   │   └── room_2/2024/11/file2.pdf
│   └── private/
│       └── user_1/file3.doc
└── config/
    ├── database.ini
    └── system.ini
```

### **开发优先级**
1. **第一阶段**：扩展 `TpdatabaseHandler`，添加通用查询方法
2. **第二阶段**：实现 `UserManager`，完善用户认证
3. **第三阶段**：实现 `MessageManager`，支持基本聊天
4. **第四阶段**：实现 `SharedFileManager`，支持文件分享
5. **第五阶段**：添加日志记录和性能优化

### **核心SQL语句**
```sql
-- 用户登录
SELECT id, user_type FROM users WHERE username = ? AND password = ? AND status = 'active';

-- 发送消息
INSERT INTO chat_messages (room_id, sender_id, content, message_type) VALUES (?, ?, ?, ?);

-- 上传文件
INSERT INTO shared_files (uploader_id, room_id, original_name, file_path, file_size, file_hash)
VALUES (?, ?, ?, ?, ?, ?);

-- 获取聊天记录
SELECT cm.*, u.username FROM chat_messages cm
LEFT JOIN users u ON cm.sender_id = u.id
WHERE cm.room_id = ? ORDER BY cm.created_at DESC LIMIT ?;
```

---

**总结**：这个精简版API设计专注于核心功能，包含6个数据表和3个主要API类，提供了完整的用户管理、消息存储和文件分享功能。配置使用INI文件，数据库只存储动态业务数据，架构清晰易于实现。

    // 消息结构
    struct ChatMessage {
        int id;
        int roomId;
        int senderId;
        QString senderName;  // 冗余字段，便于显示
        QString messageType;
        QString content;
        int fileId;  // 关联文件ID
        int replyToId;  // 回复消息ID
        bool isDeleted;
        QString ipAddress;
        QDateTime createdAt;
        QDateTime updatedAt;
    };
    
    // 房间成员结构
    struct RoomMember {
        int id;
        int roomId;
        int userId;
        QString username;
        QString realName;
        QString role;
        int lastReadMessageId;
        bool isActive;
        QDateTime joinedAt;
    };
    
    // 聊天室管理
    int createRoom(const QString& roomName, const QString& roomType, int createdBy,
                   const QString& description = "", int maxMembers = 100);
    bool updateRoom(int roomId, const QString& roomName, const QString& description = "");
    bool deleteRoom(int roomId);
    ChatRoom getRoom(int roomId);
    QList<ChatRoom> getAllRooms();
    QList<ChatRoom> getUserRooms(int userId);
    
    // 房间成员管理
    bool addRoomMember(int roomId, int userId, const QString& role = "member");
    bool removeRoomMember(int roomId, int userId);
    bool updateMemberRole(int roomId, int userId, const QString& role);
    QList<RoomMember> getRoomMembers(int roomId);
    bool isRoomMember(int roomId, int userId);
    bool updateLastReadMessage(int roomId, int userId, int messageId);
    
    // 消息管理
    int sendMessage(const ChatMessage& message);
    bool deleteMessage(int messageId, int userId);  // 软删除，需要权限检查
    bool recallMessage(int messageId, int userId);  // 撤回消息
    ChatMessage getMessage(int messageId);
    QList<ChatMessage> getRoomMessages(int roomId, int limit = 50, int offset = 0);
    QList<ChatMessage> getMessagesByType(int roomId, const QString& messageType);
    QList<ChatMessage> searchMessages(int roomId, const QString& keyword);
    QList<ChatMessage> getUnreadMessages(int roomId, int userId);
    
    // 消息统计
    struct MessageStatistics {
        int totalMessages;
        int textMessages;
        int fileMessages;
        int imageMessages;
        int unreadCount;
        QDateTime lastMessageTime;
    };
    
    MessageStatistics getRoomStatistics(int roomId, int userId = 0);
    
    // 消息清理
    bool clearRoomMessages(int roomId);
    bool deleteOldMessages(int roomId, const QDateTime& beforeDate);

private:
    TpdatabaseHandler* m_dbHandler;
    bool hasPermission(int roomId, int userId, const QString& action);
};
```

### **API方法详细说明**

#### **1. sendMessage() - 发送消息**
```cpp
/**
 * @brief 发送聊天消息
 * @param message 消息信息
 * @return int 消息ID，失败返回-1
 */
int sendMessage(const ChatMessage& message);
```

**SQL实现:**
```sql
-- 发送普通消息
INSERT INTO chat_messages
(room_id, sender_id, message_type, content, file_id, reply_to_id, ip_address)
VALUES (?, ?, ?, ?, ?, ?, ?);

-- 同时更新发送者的最后阅读消息
UPDATE room_members
SET last_read_message_id = LAST_INSERT_ID()
WHERE room_id = ? AND user_id = ?;
```

#### **2. getRoomMessages() - 获取聊天记录**
```cpp
/**
 * @brief 获取聊天室消息
 * @param roomId 聊天室ID
 * @param limit 限制数量
 * @param offset 偏移量
 * @return QList<ChatMessage> 消息列表
 */
QList<ChatMessage> getRoomMessages(int roomId, int limit, int offset);
```

**SQL实现:**
```sql
SELECT cm.*, u.username, u.real_name,
       sf.original_name as file_name, sf.file_size,
       reply_msg.content as reply_content
FROM chat_messages cm
LEFT JOIN users u ON cm.sender_id = u.id
LEFT JOIN shared_files sf ON cm.file_id = sf.id
LEFT JOIN chat_messages reply_msg ON cm.reply_to_id = reply_msg.id
WHERE cm.room_id = ? AND cm.is_deleted = FALSE
ORDER BY cm.created_at DESC
LIMIT ? OFFSET ?;
```

#### **3. addRoomMember() - 添加房间成员**
```cpp
/**
 * @brief 添加用户到聊天室
 * @param roomId 聊天室ID
 * @param userId 用户ID
 * @param role 用户角色
 * @return bool 是否成功
 */
bool addRoomMember(int roomId, int userId, const QString& role);
```

**SQL实现:**
```sql
INSERT INTO room_members (room_id, user_id, role)
VALUES (?, ?, ?)
ON DUPLICATE KEY UPDATE
is_active = TRUE, role = VALUES(role);
```

## 📁 **文件信息管理API**

### **SharedFileManager 类设计**

```cpp
class SharedFileManager : public QObject
{
    Q_OBJECT

public:
    explicit SharedFileManager(TpdatabaseHandler* dbHandler, QObject* parent = nullptr);

    // 共享文件信息结构
    struct SharedFileInfo {
        int id;
        int uploaderId;
        QString uploaderName;
        int roomId;  // NULL表示私人文件
        QString roomName;
        QString originalName;
        QString storedName;
        QString filePath;  // 相对路径
        qint64 fileSize;
        QString mimeType;
        QString fileExtension;
        QString fileHash;
        int downloadCount;
        QString description;
        QStringList tags;
        bool isPublic;
        QString uploadIp;
        QDateTime createdAt;
        QDateTime updatedAt;
    };

    // 下载记录结构
    struct DownloadRecord {
        int id;
        int fileId;
        int downloaderId;
        QString downloaderName;
        QString downloadIp;
        QDateTime downloadTime;
    };

    // 文件上传（类似QQ群文件上传）
    int uploadFile(int uploaderId, int roomId, const QString& localFilePath,
                   const QString& description = "", const QStringList& tags = QStringList());

    // 文件查询
    SharedFileInfo getFileInfo(int fileId);
    SharedFileInfo getFileByHash(const QString& fileHash);
    QList<SharedFileInfo> getRoomFiles(int roomId, int limit = 50, int offset = 0);
    QList<SharedFileInfo> getUserFiles(int userId, int limit = 50, int offset = 0);
    QList<SharedFileInfo> getPublicFiles(int limit = 50, int offset = 0);
    QList<SharedFileInfo> searchFiles(const QString& keyword, int roomId = 0);
    QList<SharedFileInfo> getFilesByExtension(const QString& extension, int roomId = 0);

    // 文件下载管理
    bool recordDownload(int fileId, int downloaderId, const QString& downloadIp = "");
    QList<DownloadRecord> getFileDownloads(int fileId);
    QList<DownloadRecord> getUserDownloads(int userId);

    // 文件操作
    bool deleteFile(int fileId, int userId);  // 需要权限检查
    bool updateFileInfo(int fileId, const QString& description, const QStringList& tags, int userId);
    bool setFilePublic(int fileId, bool isPublic, int userId);

    // 文件统计
    struct FileStatistics {
        int totalFiles;
        qint64 totalSize;
        QMap<QString, int> extensionCount;
        QMap<QString, qint64> extensionSize;
        int totalDownloads;
        QDateTime lastUploadTime;
    };

    FileStatistics getRoomFileStatistics(int roomId);
    FileStatistics getUserFileStatistics(int userId);
    FileStatistics getSystemFileStatistics();

    // 文件清理和维护
    bool cleanupOrphanedFiles();  // 清理数据库中存在但文件系统中不存在的记录
    bool deleteOldFiles(const QDateTime& beforeDate);
    QStringList findDuplicateFiles();  // 根据hash查找重复文件
    bool validateFileIntegrity(int fileId);  // 验证文件完整性

private:
    TpdatabaseHandler* m_dbHandler;
    QString calculateFileHash(const QString& filePath);
    QString generateStoredName(const QString& originalName);
    QString getFileExtension(const QString& fileName);
    bool ensureDirectoryExists(const QString& dirPath);
    bool hasFilePermission(int fileId, int userId, const QString& action);
    QString getRelativeFilePath(int roomId, const QString& storedName);
};


### **6. 部署和维护建议**

#### **配置文件管理**
- **database.ini**: 数据库连接配置
- **system.ini**: 系统基础配置（文件上传限制、会话超时等）
- **camera.ini**: 相机默认配置



