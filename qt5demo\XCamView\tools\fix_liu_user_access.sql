-- 修复liu用户访问问题
-- 在MySQL服务器(*************)上执行此脚本

-- 方案1：为特定主机名创建用户（立即解决当前问题）
CREATE USER IF NOT EXISTS 'liu'@'DESKTOP-KDN5QHV' IDENTIFIED BY 'SecurePass123!';
GRANT ALL PRIVILEGES ON student.* TO 'liu'@'DESKTOP-KDN5QHV';

-- 方案2：为IP地址创建用户（如果禁用DNS解析后使用）
CREATE USER IF NOT EXISTS 'liu'@'*************' IDENTIFIED BY 'SecurePass123!';
GRANT ALL PRIVILEGES ON student.* TO 'liu'@'*************';

-- 方案3：为局域网段创建用户（推荐）
CREATE USER IF NOT EXISTS 'liu'@'192.168.6.%' IDENTIFIED BY 'SecurePass123!';
GRANT ALL PRIVILEGES ON student.* TO 'liu'@'192.168.6.%';

-- 方案4：允许从任何地方连接（最灵活但不太安全）
CREATE USER IF NOT EXISTS 'liu'@'%' IDENTIFIED BY 'SecurePass123!';
GRANT ALL PRIVILEGES ON student.* TO 'liu'@'%';

-- 刷新权限
FLUSH PRIVILEGES;

-- 查看liu用户的所有权限
SELECT User, Host FROM mysql.user WHERE User = 'liu';
SHOW GRANTS FOR 'liu'@'DESKTOP-KDN5QHV';
SHOW GRANTS FOR 'liu'@'*************';
SHOW GRANTS FOR 'liu'@'192.168.6.%';
SHOW GRANTS FOR 'liu'@'%';

-- 显示当前连接信息（用于验证）
SELECT USER(), @@hostname, CONNECTION_ID();
