#ifndef TPSAVELOADER_H
#define TPSAVELOADER_H

#include <QSettings>

class TpSaveLoader : public QObject
{
    Q_OBJECT
public:
    explicit TpSaveLoader(QObject *parent = nullptr);

    void SaveCalibration(const QList<Calibration>& caliList);
    void LoadCalibration(QList<Calibration>* pCaliList);
    void SaveCurCaliIndex(int index);
    int  LoadCurCaliIndex();

private:
    QSettings m_settings;
};

extern TpSaveLoader g_saveLoader;

#endif // TPSAVELOADER_H
