/****************************************************************************
** Meta object code from reading C++ file 'whitebalancewidget.h'
**
** Created by: The Qt Meta Object Compiler version 69 (Qt 6.10.0)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../../../panel/camerapanel/whitebalancewidget.h"
#include <QtGui/qtextcursor.h>
#include <QtCore/qmetatype.h>

#include <QtCore/qtmochelpers.h>

#include <memory>


#include <QtCore/qxptype_traits.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'whitebalancewidget.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 69
#error "This file was generated using the moc from 6.10.0. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {
struct qt_meta_tag_ZN18WhiteBalanceWidgetE_t {};
} // unnamed namespace

template <> constexpr inline auto WhiteBalanceWidget::qt_create_metaobjectdata<qt_meta_tag_ZN18WhiteBalanceWidgetE_t>()
{
    namespace QMC = QtMocConstants;
    QtMocHelpers::StringRefStorage qt_stringData {
        "WhiteBalanceWidget",
        "OnWBBGpClicked",
        "",
        "id",
        "OnWBRedChanged",
        "value",
        "OnWBGreenChanged",
        "OnWBBlueChanged",
        "OnDefBtnClicked",
        "OnCamEvent",
        "event",
        "OnIsHide",
        "bHide"
    };

    QtMocHelpers::UintData qt_methods {
        // Slot 'OnWBBGpClicked'
        QtMocHelpers::SlotData<void(int)>(1, 2, QMC::AccessProtected, QMetaType::Void, {{
            { QMetaType::Int, 3 },
        }}),
        // Slot 'OnWBRedChanged'
        QtMocHelpers::SlotData<void(int)>(4, 2, QMC::AccessProtected, QMetaType::Void, {{
            { QMetaType::Int, 5 },
        }}),
        // Slot 'OnWBGreenChanged'
        QtMocHelpers::SlotData<void(int)>(6, 2, QMC::AccessProtected, QMetaType::Void, {{
            { QMetaType::Int, 5 },
        }}),
        // Slot 'OnWBBlueChanged'
        QtMocHelpers::SlotData<void(int)>(7, 2, QMC::AccessProtected, QMetaType::Void, {{
            { QMetaType::Int, 5 },
        }}),
        // Slot 'OnDefBtnClicked'
        QtMocHelpers::SlotData<void()>(8, 2, QMC::AccessProtected, QMetaType::Void),
        // Slot 'OnCamEvent'
        QtMocHelpers::SlotData<void(int)>(9, 2, QMC::AccessProtected, QMetaType::Void, {{
            { QMetaType::Int, 10 },
        }}),
        // Slot 'OnIsHide'
        QtMocHelpers::SlotData<void(bool)>(11, 2, QMC::AccessProtected, QMetaType::Void, {{
            { QMetaType::Bool, 12 },
        }}),
    };
    QtMocHelpers::UintData qt_properties {
    };
    QtMocHelpers::UintData qt_enums {
    };
    return QtMocHelpers::metaObjectData<WhiteBalanceWidget, qt_meta_tag_ZN18WhiteBalanceWidgetE_t>(QMC::MetaObjectFlag{}, qt_stringData,
            qt_methods, qt_properties, qt_enums);
}
Q_CONSTINIT const QMetaObject WhiteBalanceWidget::staticMetaObject = { {
    QMetaObject::SuperData::link<CollapseWidget::staticMetaObject>(),
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN18WhiteBalanceWidgetE_t>.stringdata,
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN18WhiteBalanceWidgetE_t>.data,
    qt_static_metacall,
    nullptr,
    qt_staticMetaObjectRelocatingContent<qt_meta_tag_ZN18WhiteBalanceWidgetE_t>.metaTypes,
    nullptr
} };

void WhiteBalanceWidget::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    auto *_t = static_cast<WhiteBalanceWidget *>(_o);
    if (_c == QMetaObject::InvokeMetaMethod) {
        switch (_id) {
        case 0: _t->OnWBBGpClicked((*reinterpret_cast<std::add_pointer_t<int>>(_a[1]))); break;
        case 1: _t->OnWBRedChanged((*reinterpret_cast<std::add_pointer_t<int>>(_a[1]))); break;
        case 2: _t->OnWBGreenChanged((*reinterpret_cast<std::add_pointer_t<int>>(_a[1]))); break;
        case 3: _t->OnWBBlueChanged((*reinterpret_cast<std::add_pointer_t<int>>(_a[1]))); break;
        case 4: _t->OnDefBtnClicked(); break;
        case 5: _t->OnCamEvent((*reinterpret_cast<std::add_pointer_t<int>>(_a[1]))); break;
        case 6: _t->OnIsHide((*reinterpret_cast<std::add_pointer_t<bool>>(_a[1]))); break;
        default: ;
        }
    }
}

const QMetaObject *WhiteBalanceWidget::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *WhiteBalanceWidget::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_staticMetaObjectStaticContent<qt_meta_tag_ZN18WhiteBalanceWidgetE_t>.strings))
        return static_cast<void*>(this);
    return CollapseWidget::qt_metacast(_clname);
}

int WhiteBalanceWidget::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = CollapseWidget::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 7)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 7;
    }
    if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 7)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 7;
    }
    return _id;
}
QT_WARNING_POP
