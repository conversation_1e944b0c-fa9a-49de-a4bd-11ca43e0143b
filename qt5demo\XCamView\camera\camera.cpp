﻿#include "camera.h"

Camera::Camera(QObject *parent)
    : QObject(parent)
{

}

bool Camera::InitLoader()
{
    return true;
}

QList<CameraSt> Camera::Enum()
{
    return QList<CameraSt>();
}

bool Camera::Open(const CameraSt &info)
{
    Q_UNUSED(info);
    return false;
}

const CameraSt &Camera::GetCamInfo() const
{
    return m_camInfo;
}

void Camera::Close()
{

}

void Camera::Stop()
{

}

HRESULT Camera::Start()
{
    return 0;
}

bool Camera::IsOpened()
{
    return false;
}

bool Camera::IsRunning()
{
    return false;
}

HRESULT Camera::PutPara(unsigned nId, int val)
{
    Q_UNUSED(nId);
    Q_UNUSED(val);
    return 0;
}

HRESULT Camera::GetPara(unsigned nId, int *pVal)
{
    Q_UNUSED(nId);
    Q_UNUSED(pVal);
    return 0;
}

HRESULT Camera::GetParaRange(unsigned nId, int *pMin, int *pMax, int *pDef)
{
    Q_UNUSED(nId);
    Q_UNUSED(pMin);
    Q_UNUSED(pMax);
    Q_UNUSED(pDef);
    return 0;
}

int Camera::GetImageWidth()
{
    return 0;
}

int Camera::GetImageHeight()
{
    return 0;
}

void Camera::GetImage(void* pFrameBuffer)
{
    Q_UNUSED(pFrameBuffer);
}
