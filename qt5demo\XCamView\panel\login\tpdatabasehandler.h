#ifndef TPDATABASEHANDLER_H
#define TPDATABASEHANDLER_H

#include <QObject>
#include <QSqlDatabase>
#include <QSqlQuery>
#include <QSqlError>
#include <QSettings>
#include <QDir>
#include <QApplication>
#include <QFile>
#include <QDebug>

// 数据库连接状态枚举
enum class DatabaseConnectionState {
    NotConnected,
    Connecting,
    Connected,
    ConnectionFailed
};

// 数据库配置结构体
struct DatabaseConfig {
    QString hostName = "*************";
    int port = 3306;
    QString databaseName = "student";
    QString userName = "pan";
    QString password = "pan123456@";

    bool isValid() const {
        return !hostName.isEmpty() && port > 0 &&
               !databaseName.isEmpty() && !userName.isEmpty();
    }
};

class TpdatabaseHandler : public QObject
{
    Q_OBJECT

public:
    explicit TpdatabaseHandler(QObject *parent = nullptr);
    ~TpdatabaseHandler();

    // 主要接口
    bool connectToDatabase();
    void disconnectFromDatabase();
    bool isConnected() const;
    bool validateUser(const QString &username, const QString &password);

    // 配置管理
    bool loadConfig(const QString &configPath = "");
    DatabaseConfig getConfig() const { return m_config; }
    void setConfig(const DatabaseConfig &config);

    // 错误处理
    QString lastError() const { return m_lastError; }
    DatabaseConnectionState connectionState() const { return m_connectionState; }

    // 数据库访问
    QSqlDatabase& database() { return m_db; }
    QSqlDatabase getDatabase() const { return m_db; }

signals:
    void connectionStateChanged(DatabaseConnectionState state);
    void errorOccurred(const QString &error);

private:
    // 配置相关
    bool loadConfigFromFile(const QString &filePath);
    QStringList getConfigSearchPaths() const;

    // 连接相关
    bool createConnection();
    bool testConnection();
    void handleConnectionError(const QSqlError &error);

    // 工具方法
    QString formatConnectionString() const;
    void logConnectionAttempt() const;
    void logConnectionResult(bool success) const;

private:
    DatabaseConfig m_config;
    QSqlDatabase m_db;
    DatabaseConnectionState m_connectionState;
    QString m_lastError;
    QString m_connectionName;

    static int s_connectionCounter; // 用于生成唯一连接名
};

#endif // TPDATABASEHANDLER_H
