//#ifndef LOGINDIALOG_H
//#define LOGINDIALOG_H

//#include <QDialog>
//#include <QLabel>
//#include <QLineEdit>
//#include <QPushButton>

//class LoginDialog : public QDialog
//{
//    Q_OBJECT
//public:
//    explicit LoginDialog(QWidget *parent = nullptr);

//private:
//    QLabel* m_pNameLabel;
//    QLineEdit* m_pNameLineEdit;
//    QLabel* m_pPasswordLabel;
//    QLineEdit* m_pPasswordLineEdit;
//    QPushButton* m_pOKBtn;
//    QPushButton* m_pCancelBtn;
//};

//#endif // LOGINDIALOG_H




#ifndef LOGINDIALOG_H
#define LOGINDIALOG_H

#include <QDialog>
#include <QLabel>
#include <QLineEdit>
#include <QPushButton>
#include <QRadioButton>
#include <QButtonGroup>
#include "tpdatabasehandler.h"   // 包含数据库处理类

class LoginDialog : public QDialog
{
    Q_OBJECT
public:
    explicit LoginDialog(QWidget *parent = nullptr);
    
    // 获取用户类型：0-教师端，1-学生端
    int getUserType() const;

signals:
    void loginSuccess();  // 新增登录成功信号

private slots:
    void onLogin();       // 新增登录处理槽函数

private:
    QLabel* m_pNameLabel;
    QLineEdit* m_pNameLineEdit;
    QLabel* m_pPasswordLabel;
    QLineEdit* m_pPasswordLineEdit;
    QPushButton* m_pOKBtn;
    QPushButton* m_pCancelBtn;
    
    // 新增：用户类型选择
    QLabel* m_pUserTypeLabel;
    QRadioButton* m_pTeacherRadio;
    QRadioButton* m_pStudentRadio;
    QButtonGroup* m_pUserTypeGroup;

    TpdatabaseHandler m_dbHandler;  // 新增数据库处理器对象
};

#endif // LOGINDIALOG_H
