﻿#ifndef CAMERAMANAGER_H
#define CAMERAMANAGER_H

#include <QObject>
#include <QString>
#include <QList>
#include "cameradef.h"

class Camera;

class CameraManager : public QObject
{
    Q_OBJECT

public:
    explicit CameraManager(QObject* parent = nullptr);

    bool InitLoader();

    QList<CameraSt> Enum();

    bool Open(const CameraSt& info);
    const CameraSt& GetCamInfo() const;
    void Close();
    void Stop();
    bool Start();
    bool IsOpened();
    bool IsRunning();

    HRESULT PutPara(unsigned nId, int val);
    HRESULT GetPara(unsigned nId, int* pVal);
    HRESULT GetParaRange(unsigned nId, int* pMin, int* pMax, int* pDef);

    int  GetImageWidth();
    int  GetImageHeight();
    void GetImage(void* pFrameBuffer);

signals:
    void Notify(int notify);
    void CamEvent(int event);

private:
    Camera*  m_pCamera;
};

extern CameraManager* g_pCameraManager;
extern QList<CameraSt> g_cameras;

#endif
