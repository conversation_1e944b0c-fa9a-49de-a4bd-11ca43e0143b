#ifndef TPDATABASERESULT_H
#define TPDATABASERESULT_H

#include <QString>
#include <QVariant>

#if defined(TPDATABASE_LIBRARY)
#define TPDATABASE_EXPORT Q_DECL_EXPORT
#else
#define TPDATABASE_EXPORT Q_DECL_IMPORT
#endif

/**
 * @brief 数据库操作错误码枚举
 */
enum class TpDatabaseErrorCode {
    Success = 0,                // 操作成功
    ConnectionFailed,           // 连接失败
    QueryFailed,               // 查询失败
    TransactionFailed,         // 事务失败
    ConfigError,               // 配置错误
    ValidationError,           // 验证错误
    PermissionDenied,          // 权限拒绝
    TableNotFound,             // 表不存在
    DuplicateKey,              // 主键重复
    ForeignKeyConstraint,      // 外键约束
    DataTooLong,               // 数据过长
    InvalidParameter,          // 无效参数
    TimeoutError,              // 超时错误
    UnknownError               // 未知错误
};

/**
 * @brief 数据库操作结果封装类
 * 
 * 提供统一的错误码机制，封装操作结果和错误信息
 */
class TPDATABASE_EXPORT TpDatabaseResult
{
public:
    /**
     * @brief 构造成功结果
     */
    TpDatabaseResult();
    
    /**
     * @brief 构造带数据的成功结果
     * @param data 返回的数据
     */
    explicit TpDatabaseResult(const QVariant& data);
    
    /**
     * @brief 构造错误结果
     * @param errorCode 错误码
     * @param errorMessage 错误信息
     */
    TpDatabaseResult(TpDatabaseErrorCode errorCode, const QString& errorMessage);
    
    /**
     * @brief 拷贝构造函数
     */
    TpDatabaseResult(const TpDatabaseResult& other);
    
    /**
     * @brief 赋值操作符
     */
    TpDatabaseResult& operator=(const TpDatabaseResult& other);
    
    /**
     * @brief 析构函数
     */
    ~TpDatabaseResult();

    // 结果查询方法
    
    /**
     * @brief 检查操作是否成功
     * @return true表示成功，false表示失败
     */
    bool isSuccess() const;
    
    /**
     * @brief 获取错误码
     * @return 错误码枚举值
     */
    TpDatabaseErrorCode errorCode() const;
    
    /**
     * @brief 获取错误信息
     * @return 错误信息字符串
     */
    QString errorMessage() const;
    
    /**
     * @brief 获取返回数据
     * @return 操作返回的数据（如插入的ID、影响的行数等）
     */
    QVariant data() const;
    
    /**
     * @brief 获取影响的行数
     * @return 影响的行数，-1表示未知
     */
    int affectedRows() const;

    // 静态工厂方法
    
    /**
     * @brief 创建成功结果
     * @param data 可选的返回数据
     * @return 成功的结果对象
     */
    static TpDatabaseResult success(const QVariant& data = QVariant());
    
    /**
     * @brief 创建错误结果
     * @param errorCode 错误码
     * @param errorMessage 错误信息
     * @return 错误的结果对象
     */
    static TpDatabaseResult error(TpDatabaseErrorCode errorCode, const QString& errorMessage);
    
    /**
     * @brief 从SQL错误创建结果
     * @param sqlError SQL错误对象
     * @return 错误的结果对象
     */
    static TpDatabaseResult fromSqlError(const class QSqlError& sqlError);

    // 工具方法
    
    /**
     * @brief 错误码转字符串
     * @param errorCode 错误码
     * @return 错误码对应的字符串描述
     */
    static QString errorCodeToString(TpDatabaseErrorCode errorCode);

private:
    TpDatabaseErrorCode m_errorCode;
    QString m_errorMessage;
    QVariant m_data;
    int m_affectedRows;
};

#endif // TPDATABASERESULT_H
