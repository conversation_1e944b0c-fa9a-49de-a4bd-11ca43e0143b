﻿#ifndef CAMERADEF_H
#define CAMERADEF_H

#include <QString>

/* 值根据uvcham.h中的来定义 */
#define CAM_PARA_AEXPO          0x00000013
#define CAM_PARA_AEXPOTARGET    0x00000004
#define CAM_PARA_EXPOTIME       0x00000011
#define CAM_PARA_EXPOGAIN       0x00000084
#define CAM_PARA_WBMODE         0x00000010
#define CAM_PARA_WBRED          0x0000001c
#define CAM_PARA_WBGREEN        0x0000001d
#define CAM_PARA_WBBLUE         0x0000001e
#define CAM_PARA_WBROILEFT      0x00000006
#define CAM_PARA_WBROIWIDTH     0x00000007
#define CAM_PARA_WBROITOP       0x00000008
#define CAM_PARA_WBROIHEIGHT    0x0000000a
#define CAM_PARA_SATURATION     0x00000017
#define CAM_PARA_GAMMA          0x00000018
#define CAM_PARA_CONTRAST       0x00000019
#define CAM_PARA_BRIGHTNESS     0x0000001a
#define CAM_PARA_HUE            0x0000001f
#define CAM_PARA_HZ             0x0000001b
#define CAM_PARA_RES            0x10000000
#define CAM_PARA_WIDTH          0x40000000
#define CAM_PARA_HEIGHT         0x80000000

#define CAM_EVENT_IMAGE         0x0001
#define CAM_EVENT_DISCONNECT    0x0002
#define CAM_EVENT_ERROR         0x0004
/* 以下为自定义 */
#define CAM_EVENT_WB_CHANGED    0x0005
#define CAM_EVENT_EXP_CHANGED   0x0006

#define WB_MANUAL 0
#define WB_AUTO   1
#define WB_ROI    2

#define HZ_AC60 0
#define HZ_AC50 1
#define HZ_DC   2

struct CameraSt
{
    QString  name;
    QString  id;

    void Clear()
    {
        name.clear();
        id.clear();
    }
};

#endif
