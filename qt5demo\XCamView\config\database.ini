[Database]
host=*************
port=3306
database=student
username=liu
password=SecurePass123!

[Network]
# 此配置文件用于连接到 ************* 上的MySQL数据库
# 确保MySQL服务器允许远程连接

[Troubleshooting]
# MySQL远程连接问题解决方案：
# 1. 在MySQL服务器上执行以下命令允许远程连接：
#    CREATE USER 'root'@'%' IDENTIFIED BY '123456@mysql';
#    GRANT ALL PRIVILEGES ON *.* TO 'root'@'%' WITH GRANT OPTION;
#    FLUSH PRIVILEGES;
# 
# 2. 或者创建特定网段用户：
#    CREATE USER 'root'@'192.168.6.%' IDENTIFIED BY '123456@mysql';
#    GRANT ALL PRIVILEGES ON *.* TO 'root'@'192.168.6.%' WITH GRANT OPTION;
#    FLUSH PRIVILEGES;
