# Generated by CMake. Changes will be overwritten.
D:/work/XCamView/XCamView/global.h
D:/work/XCamView/XCamView/panel/leftpanel.cpp
D:/work/XCamView/XCamView/panel/camerapanel/whitebalancewidget.cpp
D:/work/XCamView/XCamView/mainwindow.h
 mmc:Q_OBJECT
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/algorithm
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/array
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/atomic
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cassert
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/chrono
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/climits
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cmath
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/concurrencysal.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cstddef
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cstdint
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cstring
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/functional
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/initializer_list
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/iterator
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/limits
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/limits.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/list
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/map
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/memory
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/new
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/numeric
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/optional
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/sal.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/set
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/stdarg.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/stdbool.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/string
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/string_view
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/tuple
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/type_traits
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/unordered_map
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/unordered_set
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/utility
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vadefs.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/variant
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vcruntime.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vcruntime_string.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vector
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/version
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/yvals.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/yvals_core.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/assert.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_malloc.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_memcpy_s.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_memory.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_search.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_stdio_config.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_wstdio.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_wstdlib.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_wstring.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/errno.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/stddef.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/stdio.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/stdlib.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/string.h
 mdp:D:/work/XCamView/XCamView/mainwindow.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q17memory.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20functional.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20iterator.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20memory.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20type_traits.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20utility.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q23utility.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qabstractitemmodel.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qalgorithms.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qalloc.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qanystringview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qarraydata.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qarraydataops.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qarraydatapointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qassert.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qatomic.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qatomic_cxx11.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbasicatomic.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbindingstorage.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbytearray.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbytearrayalgorithms.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbytearraylist.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbytearrayview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qchar.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcheckedint_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcompare.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcompare_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcomparehelpers.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcompilerdetection.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qconfig.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qconstructormacros.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcontainerfwd.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcontainerinfo.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcontainertools_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcontiguouscache.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qdarwinhelpers.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qdatastream.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qdebug.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qendian.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qexceptionhandling.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qflags.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qfloat16.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qforeach.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qfunctionaltools_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qfunctionpointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qgenericatomic.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qglobal.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qglobalstatic.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qhash.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qhashfunctions.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qiodevicebase.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qiterable.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qiterator.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qlatin1stringview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qline.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qlist.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qlocale.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qlogging.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmalloc.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmap.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmargins.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmath.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmetacontainer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmetatype.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qminmax.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qnamespace.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qnumeric.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qobject.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qobject_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qobjectdefs.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qobjectdefs_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qoverload.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qpair.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qpoint.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qprocessordetection.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qrect.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qrefcount.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qregularexpression.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qscopedpointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qscopeguard.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qset.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qshareddata.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qshareddata_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsharedpointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsharedpointer_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsize.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qspan.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstdlibdetection.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstring.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringalgorithms.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringbuilder.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringconverter.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringconverter_base.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringfwd.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringlist.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringliteral.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringmatcher.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringtokenizer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qswap.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsysinfo.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsystemdetection.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtaggedpointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtclasshelpermacros.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtconfiginclude.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtconfigmacros.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtcore-config.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtcoreexports.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtcoreglobal.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtdeprecationdefinitions.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtdeprecationmarkers.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtenvironmentvariables.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtextstream.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtformat_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtmetamacros.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtnoop.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtpreprocessorsupport.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtresource.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qttranslation.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qttypetraits.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtversion.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtversionchecks.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtypeinfo.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtypes.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qutf8stringview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qvariant.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qvarlengtharray.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qversiontagging.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qxptype_traits.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qyieldcpu.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/QAction
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/QActionGroup
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qaction.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qactiongroup.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qbitmap.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qbrush.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qcolor.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qcursor.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qfont.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qfontinfo.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qfontmetrics.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qfontvariableaxis.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qicon.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qimage.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qkeysequence.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpaintdevice.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpalette.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpixelformat.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpixmap.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpolygon.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qregion.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qrgb.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qrgba64.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qtgui-config.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qtguiexports.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qtguiglobal.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qtransform.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qvalidator.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qwindowdefs.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qwindowdefs_win.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/QComboBox
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/QDockWidget
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/QMainWindow
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/QMenu
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/QToolBar
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/QToolButton
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qabstractbutton.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qabstractitemdelegate.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qabstractslider.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qabstractspinbox.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qcombobox.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qdockwidget.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qframe.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qmainwindow.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qmenu.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qrubberband.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qsizepolicy.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qslider.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qstyle.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qstyleoption.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qtabbar.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qtabwidget.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qtoolbar.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qtoolbutton.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qtwidgets-config.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qtwidgetsexports.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qtwidgetsglobal.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qwidget.h
D:/work/XCamView/XCamView/panel/centralpanel/view.h
 mmc:Q_OBJECT
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/algorithm
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/array
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/atomic
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cassert
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/chrono
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/climits
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cmath
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/concurrencysal.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cstddef
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cstdint
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cstring
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/functional
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/initializer_list
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/iterator
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/limits
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/limits.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/list
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/map
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/memory
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/new
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/numeric
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/optional
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/sal.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/set
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/stdarg.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/stdbool.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/string
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/string_view
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/tuple
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/type_traits
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/unordered_map
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/unordered_set
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/utility
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vadefs.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/variant
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vcruntime.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vcruntime_string.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vector
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/version
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/yvals.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/yvals_core.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/assert.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_malloc.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_memcpy_s.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_memory.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_search.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_stdio_config.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_wstdio.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_wstdlib.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_wstring.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/errno.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/stddef.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/stdio.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/stdlib.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/string.h
 mdp:D:/work/XCamView/XCamView/panel/centralpanel/view.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/QSize
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q17memory.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20functional.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20iterator.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20memory.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20type_traits.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20utility.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q23utility.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qalgorithms.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qalloc.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qanystringview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qarraydata.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qarraydataops.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qarraydatapointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qassert.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qatomic.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qatomic_cxx11.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbasicatomic.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbindingstorage.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbytearray.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbytearrayalgorithms.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbytearraylist.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbytearrayview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qchar.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcheckedint_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcompare.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcompare_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcomparehelpers.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcompilerdetection.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qconfig.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qconstructormacros.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcontainerfwd.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcontainerinfo.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcontainertools_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcontiguouscache.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qdarwinhelpers.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qdatastream.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qdebug.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qendian.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qexceptionhandling.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qflags.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qfloat16.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qforeach.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qfunctionaltools_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qfunctionpointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qgenericatomic.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qglobal.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qglobalstatic.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qhash.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qhashfunctions.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qiodevicebase.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qiterable.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qiterator.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qlatin1stringview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qline.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qlist.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qlogging.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmalloc.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmap.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmargins.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmath.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmetacontainer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmetatype.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qminmax.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qnamespace.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qnumeric.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qobject.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qobject_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qobjectdefs.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qobjectdefs_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qoverload.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qpair.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qpoint.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qprocessordetection.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qrect.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qrefcount.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qscopedpointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qscopeguard.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qset.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qshareddata.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qshareddata_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsharedpointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsharedpointer_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsize.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qspan.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstdlibdetection.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstring.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringalgorithms.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringbuilder.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringconverter.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringconverter_base.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringfwd.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringlist.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringliteral.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringmatcher.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringtokenizer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qswap.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsysinfo.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsystemdetection.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtaggedpointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtclasshelpermacros.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtconfiginclude.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtconfigmacros.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtcore-config.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtcoreexports.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtcoreglobal.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtdeprecationdefinitions.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtdeprecationmarkers.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtenvironmentvariables.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtextstream.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtformat_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtmetamacros.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtnoop.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtpreprocessorsupport.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtresource.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qttranslation.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qttypetraits.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtversion.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtversionchecks.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtypeinfo.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtypes.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qutf8stringview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qvariant.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qvarlengtharray.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qversiontagging.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qxptype_traits.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qyieldcpu.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/QImage
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qaction.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qbitmap.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qbrush.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qcolor.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qcursor.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qfont.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qfontinfo.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qfontmetrics.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qfontvariableaxis.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qicon.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qimage.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qkeysequence.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpaintdevice.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpalette.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpixelformat.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpixmap.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpolygon.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qregion.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qrgb.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qrgba64.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qtgui-config.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qtguiexports.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qtguiglobal.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qtransform.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qwindowdefs.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qwindowdefs_win.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/QScrollBar
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/QWidget
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qabstractslider.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qscrollbar.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qsizepolicy.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qtwidgets-config.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qtwidgetsexports.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qtwidgetsglobal.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qwidget.h
D:/work/XCamView/XCamView/panel/camerapanel/camerapanel.h
 mmc:Q_OBJECT
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/algorithm
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/array
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/atomic
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cassert
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/chrono
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/climits
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cmath
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/concurrencysal.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cstddef
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cstdint
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cstring
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/functional
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/initializer_list
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/iterator
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/limits
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/limits.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/list
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/map
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/memory
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/new
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/numeric
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/optional
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/sal.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/set
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/stdarg.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/stdbool.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/string
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/string_view
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/tuple
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/type_traits
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/unordered_map
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/unordered_set
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/utility
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vadefs.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/variant
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vcruntime.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vcruntime_string.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vector
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/version
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/yvals.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/yvals_core.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/assert.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_malloc.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_memcpy_s.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_memory.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_search.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_stdio_config.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_wstdio.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_wstdlib.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_wstring.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/errno.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/stddef.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/stdio.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/stdlib.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/string.h
 mdp:D:/work/XCamView/XCamView/panel/camerapanel/camerapanel.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q17memory.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20functional.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20iterator.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20memory.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20type_traits.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20utility.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q23utility.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qalgorithms.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qalloc.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qanystringview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qarraydata.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qarraydataops.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qarraydatapointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qassert.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qatomic.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qatomic_cxx11.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbasicatomic.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbindingstorage.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbytearray.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbytearrayalgorithms.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbytearraylist.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbytearrayview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qchar.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcheckedint_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcompare.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcompare_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcomparehelpers.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcompilerdetection.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qconfig.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qconstructormacros.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcontainerfwd.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcontainerinfo.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcontainertools_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcontiguouscache.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qdarwinhelpers.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qdatastream.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qdebug.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qendian.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qexceptionhandling.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qflags.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qfloat16.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qforeach.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qfunctionaltools_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qfunctionpointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qgenericatomic.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qglobal.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qglobalstatic.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qhash.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qhashfunctions.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qiodevicebase.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qiterable.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qiterator.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qlatin1stringview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qline.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qlist.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qlogging.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmalloc.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmap.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmargins.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmath.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmetacontainer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmetatype.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qminmax.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qnamespace.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qnumeric.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qobject.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qobject_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qobjectdefs.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qobjectdefs_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qoverload.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qpair.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qpoint.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qprocessordetection.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qrect.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qrefcount.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qscopedpointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qscopeguard.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qset.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qshareddata.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qshareddata_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsharedpointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsharedpointer_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsize.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qspan.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstdlibdetection.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstring.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringalgorithms.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringbuilder.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringconverter.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringconverter_base.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringfwd.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringlist.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringliteral.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringmatcher.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringtokenizer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qswap.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsysinfo.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsystemdetection.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtaggedpointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtclasshelpermacros.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtconfiginclude.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtconfigmacros.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtcore-config.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtcoreexports.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtcoreglobal.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtdeprecationdefinitions.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtdeprecationmarkers.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtenvironmentvariables.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtextstream.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtformat_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtmetamacros.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtnoop.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtpreprocessorsupport.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtresource.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qttranslation.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qttypetraits.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtversion.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtversionchecks.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtypeinfo.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtypes.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qutf8stringview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qvariant.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qvarlengtharray.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qversiontagging.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qxptype_traits.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qyieldcpu.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qaction.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qbitmap.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qbrush.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qcolor.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qcursor.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qfont.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qfontinfo.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qfontmetrics.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qfontvariableaxis.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qicon.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qimage.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qkeysequence.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpaintdevice.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpalette.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpixelformat.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpixmap.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpolygon.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qregion.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qrgb.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qrgba64.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qtgui-config.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qtguiexports.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qtguiglobal.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qtransform.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qwindowdefs.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qwindowdefs_win.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/QScrollArea
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qabstractscrollarea.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qframe.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qscrollarea.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qsizepolicy.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qtwidgets-config.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qtwidgetsexports.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qtwidgetsglobal.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qwidget.h
D:/work/XCamView/XCamView/module/TpSaveLoader/tpsaveloader.h
 mmc:Q_OBJECT
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/algorithm
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/array
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/atomic
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/chrono
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/climits
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cmath
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/concurrencysal.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cstddef
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cstdint
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cstring
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/functional
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/initializer_list
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/iterator
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/limits
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/limits.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/list
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/map
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/memory
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/new
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/numeric
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/optional
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/sal.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/set
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/stdarg.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/stdbool.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/string
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/string_view
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/tuple
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/type_traits
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/unordered_map
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/unordered_set
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/utility
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vadefs.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/variant
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vcruntime.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vcruntime_string.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vector
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/version
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/yvals.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/yvals_core.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/assert.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_malloc.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_memcpy_s.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_memory.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_search.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_stdio_config.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_wctype.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_wstdio.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_wstdlib.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_wstring.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/ctype.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/errno.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/stddef.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/stdio.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/stdlib.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/string.h
 mdp:D:/work/XCamView/XCamView/module/TpSaveLoader/tpsaveloader.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/QSettings
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q17memory.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20functional.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20memory.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20type_traits.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20utility.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q23utility.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qalgorithms.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qalloc.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qanystringview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qarraydata.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qarraydataops.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qarraydatapointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qassert.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qatomic.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qatomic_cxx11.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbasicatomic.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbindingstorage.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbytearray.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbytearrayalgorithms.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbytearraylist.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbytearrayview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qchar.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcompare.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcompare_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcomparehelpers.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcompilerdetection.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qconfig.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qconstructormacros.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcontainerfwd.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcontainerinfo.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcontainertools_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcontiguouscache.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qdarwinhelpers.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qdatastream.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qdebug.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qexceptionhandling.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qflags.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qfloat16.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qforeach.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qfunctionaltools_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qfunctionpointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qgenericatomic.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qglobal.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qglobalstatic.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qhash.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qhashfunctions.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qiodevicebase.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qiterable.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qiterator.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qlatin1stringview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qlist.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qlogging.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmalloc.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmap.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmath.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmetacontainer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmetatype.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qminmax.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qnamespace.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qnumeric.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qobject.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qobject_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qobjectdefs.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qobjectdefs_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qoverload.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qpair.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qprocessordetection.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qrefcount.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qscopedpointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qscopeguard.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qset.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsettings.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qshareddata.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qshareddata_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsharedpointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsharedpointer_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstdlibdetection.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstring.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringalgorithms.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringbuilder.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringconverter.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringconverter_base.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringfwd.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringlist.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringliteral.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringmatcher.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringtokenizer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qswap.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsysinfo.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsystemdetection.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtaggedpointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtclasshelpermacros.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtconfiginclude.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtconfigmacros.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtcore-config.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtcoreexports.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtcoreglobal.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtdeprecationdefinitions.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtdeprecationmarkers.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtenvironmentvariables.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtextstream.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtformat_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtmetamacros.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtnoop.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtpreprocessorsupport.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtresource.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qttranslation.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qttypetraits.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtversion.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtversionchecks.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtypeinfo.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtypes.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qutf8stringview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qvariant.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qvarlengtharray.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qversiontagging.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qxptype_traits.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qyieldcpu.h
D:/work/XCamView/XCamView/module/camera/camera.h
 mmc:Q_OBJECT
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/algorithm
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/array
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/atomic
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/chrono
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cmath
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/concurrencysal.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cstddef
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cstdint
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cstring
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/functional
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/initializer_list
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/iterator
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/limits
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/limits.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/list
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/map
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/memory
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/new
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/numeric
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/optional
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/sal.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/stdarg.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/stdbool.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/string
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/string_view
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/tuple
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/type_traits
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/utility
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vadefs.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/variant
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vcruntime.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vcruntime_string.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vector
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/version
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/yvals.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/yvals_core.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/assert.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_malloc.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_memcpy_s.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_memory.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_search.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_wstdlib.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_wstring.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/errno.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/stddef.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/stdlib.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/string.h
 mdp:D:/work/XCamView/XCamView/module/camera/camera.h
 mdp:D:/work/XCamView/XCamView/module/camera/cameradef.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/QList
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/QObject
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/QString
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q17memory.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20functional.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20memory.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20type_traits.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qalgorithms.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qanystringview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qarraydata.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qarraydataops.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qarraydatapointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qassert.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qatomic.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qatomic_cxx11.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbasicatomic.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbindingstorage.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbytearray.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbytearrayalgorithms.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbytearraylist.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbytearrayview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qchar.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcompare.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcompare_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcomparehelpers.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcompilerdetection.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qconfig.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qconstructormacros.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcontainerfwd.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcontainerinfo.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcontainertools_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qdarwinhelpers.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qdatastream.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qexceptionhandling.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qflags.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qfloat16.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qforeach.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qfunctionaltools_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qfunctionpointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qgenericatomic.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qglobal.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qglobalstatic.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qhashfunctions.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qiodevicebase.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qiterable.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qiterator.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qlatin1stringview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qlist.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qlogging.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmalloc.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmath.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmetacontainer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmetatype.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qminmax.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qnamespace.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qnumeric.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qobject.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qobject_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qobjectdefs.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qobjectdefs_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qoverload.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qpair.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qprocessordetection.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qrefcount.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qscopedpointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qscopeguard.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstdlibdetection.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstring.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringalgorithms.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringbuilder.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringconverter.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringconverter_base.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringfwd.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringlist.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringliteral.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringmatcher.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringtokenizer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qswap.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsysinfo.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsystemdetection.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtaggedpointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtclasshelpermacros.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtconfiginclude.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtconfigmacros.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtcore-config.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtcoreexports.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtcoreglobal.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtdeprecationdefinitions.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtdeprecationmarkers.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtenvironmentvariables.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtformat_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtmetamacros.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtnoop.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtpreprocessorsupport.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtresource.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qttranslation.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qttypetraits.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtversion.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtversionchecks.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtypeinfo.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtypes.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qutf8stringview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qversiontagging.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qxptype_traits.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qyieldcpu.h
D:/work/XCamView/XCamView/panel/camerapanel/exposuregainwidget.cpp
D:/work/XCamView/XCamView/panel/centralpanel/displayview/imageview.cpp
D:/work/XCamView/XCamView/module/TpSignalConnect/TpSignalConnect.h
 mmc:Q_OBJECT
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/algorithm
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/array
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/atomic
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/chrono
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/climits
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cmath
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/concurrencysal.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cstddef
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cstdint
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cstring
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/functional
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/initializer_list
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/iterator
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/limits
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/limits.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/list
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/map
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/memory
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/new
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/numeric
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/optional
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/sal.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/set
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/stdarg.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/stdbool.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/string
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/string_view
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/tuple
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/type_traits
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/unordered_map
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/unordered_set
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/utility
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vadefs.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/variant
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vcruntime.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vcruntime_string.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vector
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/version
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/yvals.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/yvals_core.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/assert.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_malloc.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_memcpy_s.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_memory.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_search.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_stdio_config.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_wctype.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_wstdio.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_wstdlib.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_wstring.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/ctype.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/errno.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/stddef.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/stdio.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/stdlib.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/string.h
 mdp:D:/work/XCamView/XCamView/module/TpSignalConnect/TpSignalConnect.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/QSettings
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q17memory.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20functional.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20memory.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20type_traits.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20utility.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q23utility.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qalgorithms.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qalloc.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qanystringview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qarraydata.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qarraydataops.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qarraydatapointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qassert.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qatomic.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qatomic_cxx11.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbasicatomic.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbindingstorage.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbytearray.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbytearrayalgorithms.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbytearraylist.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbytearrayview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qchar.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcompare.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcompare_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcomparehelpers.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcompilerdetection.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qconfig.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qconstructormacros.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcontainerfwd.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcontainerinfo.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcontainertools_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcontiguouscache.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qdarwinhelpers.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qdatastream.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qdebug.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qexceptionhandling.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qflags.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qfloat16.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qforeach.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qfunctionaltools_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qfunctionpointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qgenericatomic.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qglobal.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qglobalstatic.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qhash.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qhashfunctions.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qiodevicebase.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qiterable.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qiterator.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qlatin1stringview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qlist.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qlogging.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmalloc.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmap.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmath.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmetacontainer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmetatype.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qminmax.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qnamespace.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qnumeric.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qobject.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qobject_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qobjectdefs.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qobjectdefs_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qoverload.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qpair.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qprocessordetection.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qrefcount.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qscopedpointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qscopeguard.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qset.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsettings.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qshareddata.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qshareddata_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsharedpointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsharedpointer_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstdlibdetection.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstring.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringalgorithms.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringbuilder.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringconverter.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringconverter_base.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringfwd.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringlist.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringliteral.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringmatcher.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringtokenizer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qswap.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsysinfo.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsystemdetection.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtaggedpointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtclasshelpermacros.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtconfiginclude.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtconfigmacros.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtcore-config.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtcoreexports.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtcoreglobal.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtdeprecationdefinitions.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtdeprecationmarkers.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtenvironmentvariables.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtextstream.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtformat_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtmetamacros.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtnoop.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtpreprocessorsupport.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtresource.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qttranslation.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qttypetraits.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtversion.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtversionchecks.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtypeinfo.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtypes.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qutf8stringview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qvariant.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qvarlengtharray.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qversiontagging.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qxptype_traits.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qyieldcpu.h
D:/work/XCamView/XCamView/module/auxrect/auxrect.h
 mmc:Q_OBJECT
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/algorithm
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/array
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/atomic
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cassert
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/chrono
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/climits
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cmath
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/concurrencysal.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cstddef
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cstdint
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cstring
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/functional
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/initializer_list
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/iterator
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/limits
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/limits.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/list
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/map
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/memory
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/new
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/numeric
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/optional
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/sal.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/set
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/stdarg.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/stdbool.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/string
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/string_view
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/tuple
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/type_traits
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/unordered_map
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/unordered_set
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/utility
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vadefs.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/variant
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vcruntime.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vcruntime_string.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vector
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/version
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/yvals.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/yvals_core.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/assert.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_malloc.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_memcpy_s.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_memory.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_search.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_stdio_config.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_wstdio.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_wstdlib.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_wstring.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/errno.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/stddef.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/stdio.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/stdlib.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/string.h
 mdp:D:/work/XCamView/XCamView/module/auxrect/auxrect.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/QTimer
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q17memory.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20functional.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20iterator.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20memory.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20type_traits.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20utility.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q23utility.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qabstracteventdispatcher.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qalgorithms.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qalloc.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qanystringview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qarraydata.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qarraydataops.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qarraydatapointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qassert.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qatomic.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qatomic_cxx11.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbasicatomic.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbasictimer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbindingstorage.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbytearray.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbytearrayalgorithms.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbytearraylist.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbytearrayview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qchar.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcheckedint_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcompare.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcompare_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcomparehelpers.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcompilerdetection.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qconfig.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qconstructormacros.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcontainerfwd.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcontainerinfo.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcontainertools_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcontiguouscache.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qdarwinhelpers.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qdatastream.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qdeadlinetimer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qdebug.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qendian.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qeventloop.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qexceptionhandling.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qflags.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qfloat16.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qforeach.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qfunctionaltools_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qfunctionpointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qgenericatomic.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qglobal.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qglobalstatic.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qhash.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qhashfunctions.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qiodevicebase.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qiterable.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qiterator.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qlatin1stringview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qline.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qlist.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qlogging.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmalloc.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmap.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmargins.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmath.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmetacontainer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmetatype.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qminmax.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qnamespace.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qnumeric.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qobject.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qobject_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qobjectdefs.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qobjectdefs_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qoverload.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qpair.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qpoint.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qprocessordetection.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qrect.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qrefcount.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qscopedpointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qscopeguard.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qset.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qshareddata.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qshareddata_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsharedpointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsharedpointer_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsize.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qspan.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstdlibdetection.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstring.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringalgorithms.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringbuilder.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringconverter.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringconverter_base.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringfwd.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringlist.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringliteral.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringmatcher.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringtokenizer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qswap.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsysinfo.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsystemdetection.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtaggedpointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtclasshelpermacros.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtconfiginclude.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtconfigmacros.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtcore-config.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtcoreexports.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtcoreglobal.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtdeprecationdefinitions.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtdeprecationmarkers.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtenvironmentvariables.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtextstream.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtformat_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtimer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtmetamacros.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtnoop.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtpreprocessorsupport.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtresource.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qttranslation.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qttypetraits.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtversion.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtversionchecks.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtypeinfo.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtypes.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qutf8stringview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qvariant.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qvarlengtharray.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qversiontagging.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qxptype_traits.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qyieldcpu.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/QImage
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/QPen
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qaction.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qbitmap.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qbrush.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qcolor.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qcursor.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qfont.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qfontinfo.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qfontmetrics.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qfontvariableaxis.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qicon.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qimage.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qkeysequence.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpaintdevice.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpalette.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpen.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpixelformat.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpixmap.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpolygon.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qregion.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qrgb.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qrgba64.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qtgui-config.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qtguiexports.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qtguiglobal.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qtransform.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qwindowdefs.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qwindowdefs_win.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/QWidget
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qsizepolicy.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qtwidgets-config.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qtwidgetsexports.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qtwidgetsglobal.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qwidget.h
D:/work/XCamView/XCamView/module/camera/uvchamcam/uvchamcam.cpp
D:/work/XCamView/XCamView/panel/login/logindialog.cpp
D:/work/XCamView/XCamView/module/camera/cameramanager.h
 mmc:Q_OBJECT
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/algorithm
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/array
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/atomic
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/chrono
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cmath
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/concurrencysal.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cstddef
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cstdint
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cstring
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/functional
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/initializer_list
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/iterator
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/limits
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/limits.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/list
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/map
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/memory
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/new
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/numeric
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/optional
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/sal.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/stdarg.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/stdbool.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/string
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/string_view
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/tuple
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/type_traits
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/utility
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vadefs.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/variant
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vcruntime.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vcruntime_string.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vector
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/version
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/yvals.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/yvals_core.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/assert.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_malloc.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_memcpy_s.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_memory.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_search.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_wstdlib.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_wstring.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/errno.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/stddef.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/stdlib.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/string.h
 mdp:D:/work/XCamView/XCamView/module/camera/cameradef.h
 mdp:D:/work/XCamView/XCamView/module/camera/cameramanager.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/QList
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/QObject
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/QString
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q17memory.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20functional.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20memory.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20type_traits.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qalgorithms.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qanystringview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qarraydata.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qarraydataops.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qarraydatapointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qassert.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qatomic.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qatomic_cxx11.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbasicatomic.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbindingstorage.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbytearray.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbytearrayalgorithms.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbytearraylist.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbytearrayview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qchar.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcompare.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcompare_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcomparehelpers.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcompilerdetection.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qconfig.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qconstructormacros.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcontainerfwd.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcontainerinfo.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcontainertools_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qdarwinhelpers.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qdatastream.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qexceptionhandling.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qflags.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qfloat16.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qforeach.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qfunctionaltools_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qfunctionpointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qgenericatomic.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qglobal.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qglobalstatic.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qhashfunctions.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qiodevicebase.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qiterable.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qiterator.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qlatin1stringview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qlist.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qlogging.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmalloc.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmath.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmetacontainer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmetatype.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qminmax.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qnamespace.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qnumeric.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qobject.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qobject_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qobjectdefs.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qobjectdefs_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qoverload.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qpair.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qprocessordetection.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qrefcount.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qscopedpointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qscopeguard.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstdlibdetection.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstring.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringalgorithms.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringbuilder.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringconverter.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringconverter_base.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringfwd.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringlist.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringliteral.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringmatcher.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringtokenizer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qswap.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsysinfo.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsystemdetection.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtaggedpointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtclasshelpermacros.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtconfiginclude.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtconfigmacros.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtcore-config.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtcoreexports.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtcoreglobal.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtdeprecationdefinitions.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtdeprecationmarkers.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtenvironmentvariables.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtformat_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtmetamacros.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtnoop.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtpreprocessorsupport.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtresource.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qttranslation.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qttypetraits.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtversion.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtversionchecks.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtypeinfo.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtypes.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qutf8stringview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qversiontagging.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qxptype_traits.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qyieldcpu.h
D:/work/XCamView/XCamView/module/camera/cameradef.h
D:/work/XCamView/XCamView/panel/monitorpanel/monitordialog.cpp
D:/work/XCamView/XCamView/module/camera/uvchamcam/uvcham.h
D:/work/XCamView/XCamView/module/camera/uvchamcam/uvchamcam.h
 mmc:Q_OBJECT
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/algorithm
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/array
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/atomic
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/chrono
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cmath
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/concurrencysal.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cstddef
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cstdint
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cstring
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/functional
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/initializer_list
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/iterator
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/limits
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/limits.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/list
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/map
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/memory
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/new
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/numeric
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/optional
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/sal.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/stdarg.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/stdbool.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/string
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/string_view
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/tuple
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/type_traits
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/utility
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vadefs.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/variant
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vcruntime.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vcruntime_string.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vector
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/version
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/yvals.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/yvals_core.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/assert.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_malloc.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_memcpy_s.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_memory.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_search.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_wstdlib.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_wstring.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/errno.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/stddef.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/stdlib.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/string.h
 mdp:D:/work/XCamView/XCamView/module/camera/camera.h
 mdp:D:/work/XCamView/XCamView/module/camera/cameradef.h
 mdp:D:/work/XCamView/XCamView/module/camera/uvchamcam/uvcham.h
 mdp:D:/work/XCamView/XCamView/module/camera/uvchamcam/uvchamcam.h
 mdp:D:/work/XCamView/XCamView/module/camera/uvchamcam/uvchamloader.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/QLibrary
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/QList
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/QMutex
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/QObject
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/QString
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/QTimer
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q17memory.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20functional.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20memory.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20type_traits.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qabstracteventdispatcher.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qalgorithms.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qanystringview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qarraydata.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qarraydataops.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qarraydatapointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qassert.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qatomic.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qatomic_cxx11.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbasicatomic.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbasictimer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbindingstorage.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbytearray.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbytearrayalgorithms.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbytearraylist.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbytearrayview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qchar.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcompare.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcompare_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcomparehelpers.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcompilerdetection.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qconfig.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qconstructormacros.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcontainerfwd.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcontainerinfo.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcontainertools_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qdarwinhelpers.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qdatastream.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qdeadlinetimer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qeventloop.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qexceptionhandling.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qflags.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qfloat16.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qforeach.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qfunctionaltools_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qfunctionpointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qgenericatomic.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qglobal.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qglobalstatic.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qhashfunctions.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qiodevicebase.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qiterable.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qiterator.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qlatin1stringview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qlibrary.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qlist.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qlogging.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmalloc.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmath.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmetacontainer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmetatype.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qminmax.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmutex.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qnamespace.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qnumeric.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qobject.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qobject_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qobjectdefs.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qobjectdefs_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qoverload.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qpair.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qprocessordetection.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qrefcount.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qscopedpointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qscopeguard.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstdlibdetection.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstring.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringalgorithms.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringbuilder.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringconverter.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringconverter_base.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringfwd.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringlist.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringliteral.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringmatcher.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringtokenizer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qswap.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsysinfo.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsystemdetection.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtaggedpointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtclasshelpermacros.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtconfiginclude.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtconfigmacros.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtcore-config.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtcoreexports.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtcoreglobal.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtdeprecationdefinitions.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtdeprecationmarkers.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtenvironmentvariables.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtformat_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtimer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtmetamacros.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtnoop.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtpreprocessorsupport.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtresource.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtsan_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qttranslation.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qttypetraits.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtversion.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtversionchecks.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtypeinfo.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtypes.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qutf8stringview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qversiontagging.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qxptype_traits.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qyieldcpu.h
D:/work/XCamView/XCamView/panel/login/logindialog.h
 mmc:Q_OBJECT
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/algorithm
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/array
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/atomic
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cassert
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/chrono
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/climits
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cmath
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/concurrencysal.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cstddef
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cstdint
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cstring
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/functional
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/initializer_list
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/iterator
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/limits
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/limits.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/list
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/map
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/memory
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/new
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/numeric
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/optional
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/sal.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/set
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/stdarg.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/stdbool.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/string
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/string_view
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/tuple
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/type_traits
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/unordered_map
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/unordered_set
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/utility
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vadefs.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/variant
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vcruntime.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vcruntime_string.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vector
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/version
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/yvals.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/yvals_core.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/assert.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_malloc.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_memcpy_s.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_memory.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_search.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_stdio_config.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_wstdio.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_wstdlib.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_wstring.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/errno.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/stddef.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/stdio.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/stdlib.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/string.h
 mdp:D:/work/XCamView/XCamView/panel/login/logindialog.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q17memory.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20functional.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20iterator.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20memory.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20type_traits.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20utility.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q23utility.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qalgorithms.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qalloc.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qanystringview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qarraydata.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qarraydataops.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qarraydatapointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qassert.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qatomic.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qatomic_cxx11.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbasicatomic.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbindingstorage.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbytearray.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbytearrayalgorithms.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbytearraylist.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbytearrayview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qchar.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcheckedint_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcompare.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcompare_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcomparehelpers.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcompilerdetection.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qconfig.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qconstructormacros.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcontainerfwd.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcontainerinfo.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcontainertools_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcontiguouscache.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qdarwinhelpers.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qdatastream.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qdebug.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qendian.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qexceptionhandling.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qflags.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qfloat16.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qforeach.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qfunctionaltools_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qfunctionpointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qgenericatomic.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qglobal.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qglobalstatic.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qhash.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qhashfunctions.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qiodevice.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qiodevicebase.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qiterable.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qiterator.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qlatin1stringview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qline.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qlist.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qlogging.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmalloc.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmap.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmargins.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmath.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmetacontainer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmetatype.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qminmax.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qnamespace.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qnumeric.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qobject.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qobject_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qobjectdefs.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qobjectdefs_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qoverload.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qpair.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qpoint.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qprocessordetection.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qrect.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qrefcount.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qscopedpointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qscopeguard.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qset.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qshareddata.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qshareddata_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsharedpointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsharedpointer_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsize.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qspan.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstdlibdetection.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstring.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringalgorithms.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringbuilder.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringconverter.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringconverter_base.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringfwd.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringlist.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringliteral.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringmatcher.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringtokenizer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qswap.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsysinfo.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsystemdetection.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtaggedpointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtclasshelpermacros.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtconfiginclude.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtconfigmacros.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtcore-config.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtcoreexports.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtcoreglobal.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtdeprecationdefinitions.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtdeprecationmarkers.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtenvironmentvariables.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtextstream.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtformat_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtmetamacros.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtnoop.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtpreprocessorsupport.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtresource.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qttranslation.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qttypetraits.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtversion.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtversionchecks.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtypeinfo.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtypes.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qurl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qutf8stringview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qvariant.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qvarlengtharray.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qversiontagging.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qxptype_traits.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qyieldcpu.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qaction.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qbitmap.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qbrush.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qcolor.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qcursor.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qfont.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qfontinfo.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qfontmetrics.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qfontvariableaxis.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qicon.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qimage.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qkeysequence.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpaintdevice.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpalette.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpen.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpicture.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpixelformat.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpixmap.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpolygon.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qregion.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qrgb.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qrgba64.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qtextcursor.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qtextdocument.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qtextformat.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qtextoption.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qtgui-config.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qtguiexports.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qtguiglobal.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qtransform.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qwindowdefs.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qwindowdefs_win.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/QDialog
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/QLabel
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/QLineEdit
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/QPushButton
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qabstractbutton.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qdialog.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qframe.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qlabel.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qlineedit.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qpushbutton.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qsizepolicy.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qtwidgets-config.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qtwidgetsexports.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qtwidgetsglobal.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qwidget.h
D:/work/XCamView/XCamView/panel/camerapanel/resolutionwidget.cpp
D:/work/XCamView/XCamView/module/camera/uvchamcam/uvchamloader.h
D:/work/XCamView/XCamView/panel/centralpanel/tptabwidget.h
 mmc:Q_OBJECT
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/algorithm
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/array
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/atomic
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cassert
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/chrono
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/climits
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cmath
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/concurrencysal.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cstddef
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cstdint
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cstring
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/functional
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/initializer_list
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/iterator
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/limits
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/limits.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/list
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/map
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/memory
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/new
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/numeric
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/optional
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/sal.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/set
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/stdarg.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/stdbool.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/string
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/string_view
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/tuple
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/type_traits
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/unordered_map
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/unordered_set
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/utility
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vadefs.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/variant
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vcruntime.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vcruntime_string.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vector
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/version
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/yvals.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/yvals_core.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/assert.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_malloc.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_memcpy_s.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_memory.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_search.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_stdio_config.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_wstdio.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_wstdlib.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_wstring.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/errno.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/stddef.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/stdio.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/stdlib.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/string.h
 mdp:D:/work/XCamView/XCamView/panel/centralpanel/tptabwidget.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q17memory.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20functional.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20iterator.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20memory.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20type_traits.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20utility.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q23utility.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qalgorithms.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qalloc.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qanystringview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qarraydata.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qarraydataops.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qarraydatapointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qassert.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qatomic.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qatomic_cxx11.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbasicatomic.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbindingstorage.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbytearray.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbytearrayalgorithms.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbytearraylist.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbytearrayview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qchar.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcheckedint_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcompare.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcompare_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcomparehelpers.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcompilerdetection.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qconfig.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qconstructormacros.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcontainerfwd.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcontainerinfo.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcontainertools_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcontiguouscache.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qdarwinhelpers.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qdatastream.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qdebug.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qendian.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qexceptionhandling.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qflags.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qfloat16.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qforeach.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qfunctionaltools_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qfunctionpointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qgenericatomic.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qglobal.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qglobalstatic.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qhash.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qhashfunctions.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qiodevicebase.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qiterable.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qiterator.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qlatin1stringview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qline.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qlist.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qlogging.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmalloc.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmap.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmargins.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmath.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmetacontainer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmetatype.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qminmax.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qnamespace.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qnumeric.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qobject.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qobject_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qobjectdefs.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qobjectdefs_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qoverload.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qpair.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qpoint.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qprocessordetection.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qrect.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qrefcount.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qscopedpointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qscopeguard.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qset.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qshareddata.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qshareddata_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsharedpointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsharedpointer_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsize.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qspan.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstdlibdetection.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstring.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringalgorithms.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringbuilder.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringconverter.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringconverter_base.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringfwd.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringlist.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringliteral.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringmatcher.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringtokenizer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qswap.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsysinfo.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsystemdetection.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtaggedpointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtclasshelpermacros.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtconfiginclude.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtconfigmacros.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtcore-config.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtcoreexports.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtcoreglobal.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtdeprecationdefinitions.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtdeprecationmarkers.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtenvironmentvariables.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtextstream.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtformat_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtmetamacros.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtnoop.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtpreprocessorsupport.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtresource.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qttranslation.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qttypetraits.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtversion.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtversionchecks.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtypeinfo.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtypes.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qutf8stringview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qvariant.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qvarlengtharray.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qversiontagging.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qxptype_traits.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qyieldcpu.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qaction.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qbitmap.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qbrush.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qcolor.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qcursor.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qfont.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qfontinfo.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qfontmetrics.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qfontvariableaxis.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qicon.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qimage.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qkeysequence.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpaintdevice.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpalette.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpixelformat.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpixmap.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpolygon.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qregion.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qrgb.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qrgba64.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qtgui-config.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qtguiexports.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qtguiglobal.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qtransform.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qwindowdefs.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qwindowdefs_win.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/QTabWidget
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qsizepolicy.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qtabwidget.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qtwidgets-config.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qtwidgetsexports.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qtwidgetsglobal.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qwidget.h
D:/work/XCamView/XCamView/module/i18n/i18n.h
D:/work/XCamView/XCamView/module/tutils/tutils.h
D:/work/XCamView/XCamView/predef.h
D:/work/XCamView/XCamView/panel/centralpanel/displayview/previewview.h
 mmc:Q_OBJECT
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/algorithm
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/array
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/atomic
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cassert
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/chrono
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/climits
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cmath
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/concurrencysal.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cstddef
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cstdint
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cstring
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/functional
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/initializer_list
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/iterator
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/limits
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/limits.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/list
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/map
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/memory
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/new
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/numeric
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/optional
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/sal.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/set
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/stdarg.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/stdbool.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/string
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/string_view
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/tuple
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/type_traits
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/unordered_map
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/unordered_set
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/utility
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vadefs.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/variant
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vcruntime.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vcruntime_string.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vector
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/version
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/yvals.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/yvals_core.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/assert.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_malloc.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_memcpy_s.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_memory.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_search.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_stdio_config.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_wstdio.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_wstdlib.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_wstring.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/errno.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/stddef.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/stdio.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/stdlib.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/string.h
 mdp:D:/work/XCamView/XCamView/panel/centralpanel/displayview/displayview.h
 mdp:D:/work/XCamView/XCamView/panel/centralpanel/displayview/previewview.h
 mdp:D:/work/XCamView/XCamView/panel/centralpanel/view.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/QSize
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q17memory.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20functional.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20iterator.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20memory.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20type_traits.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20utility.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q23utility.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qalgorithms.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qalloc.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qanystringview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qarraydata.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qarraydataops.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qarraydatapointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qassert.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qatomic.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qatomic_cxx11.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbasicatomic.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbindingstorage.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbytearray.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbytearrayalgorithms.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbytearraylist.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbytearrayview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qchar.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcheckedint_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcompare.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcompare_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcomparehelpers.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcompilerdetection.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qconfig.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qconstructormacros.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcontainerfwd.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcontainerinfo.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcontainertools_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcontiguouscache.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qdarwinhelpers.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qdatastream.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qdebug.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qendian.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qexceptionhandling.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qflags.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qfloat16.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qforeach.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qfunctionaltools_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qfunctionpointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qgenericatomic.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qglobal.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qglobalstatic.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qhash.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qhashfunctions.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qiodevicebase.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qiterable.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qiterator.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qlatin1stringview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qline.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qlist.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qlogging.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmalloc.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmap.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmargins.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmath.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmetacontainer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmetatype.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qminmax.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qnamespace.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qnumeric.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qobject.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qobject_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qobjectdefs.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qobjectdefs_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qoverload.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qpair.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qpoint.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qprocessordetection.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qrect.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qrefcount.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qscopedpointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qscopeguard.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qset.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qshareddata.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qshareddata_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsharedpointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsharedpointer_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsize.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qspan.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstdlibdetection.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstring.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringalgorithms.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringbuilder.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringconverter.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringconverter_base.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringfwd.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringlist.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringliteral.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringmatcher.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringtokenizer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qswap.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsysinfo.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsystemdetection.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtaggedpointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtclasshelpermacros.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtconfiginclude.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtconfigmacros.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtcore-config.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtcoreexports.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtcoreglobal.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtdeprecationdefinitions.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtdeprecationmarkers.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtenvironmentvariables.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtextstream.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtformat_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtmetamacros.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtnoop.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtpreprocessorsupport.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtresource.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qttranslation.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qttypetraits.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtversion.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtversionchecks.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtypeinfo.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtypes.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qutf8stringview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qvariant.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qvarlengtharray.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qversiontagging.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qxptype_traits.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qyieldcpu.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/QImage
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qaction.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qbitmap.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qbrush.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qcolor.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qcursor.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qfont.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qfontinfo.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qfontmetrics.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qfontvariableaxis.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qicon.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qimage.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qkeysequence.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpaintdevice.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpalette.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpixelformat.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpixmap.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpolygon.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qregion.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qrgb.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qrgba64.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qtgui-config.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qtguiexports.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qtguiglobal.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qtransform.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qwindowdefs.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qwindowdefs_win.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/QScrollBar
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/QWidget
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qabstractslider.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qscrollbar.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qsizepolicy.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qtwidgets-config.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qtwidgetsexports.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qtwidgetsglobal.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qwidget.h
D:/work/XCamView/XCamView/panel/camerapanel/cameralistwidget.h
 mmc:Q_OBJECT
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/algorithm
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/array
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/atomic
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cassert
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/chrono
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/climits
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cmath
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/concurrencysal.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cstddef
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cstdint
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cstring
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/functional
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/initializer_list
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/iterator
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/limits
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/limits.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/list
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/map
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/memory
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/new
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/numeric
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/optional
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/sal.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/set
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/stdarg.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/stdbool.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/string
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/string_view
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/tuple
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/type_traits
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/unordered_map
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/unordered_set
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/utility
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vadefs.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/variant
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vcruntime.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vcruntime_string.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vector
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/version
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/yvals.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/yvals_core.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/assert.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_malloc.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_memcpy_s.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_memory.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_search.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_stdio_config.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_wstdio.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_wstdlib.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_wstring.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/errno.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/stddef.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/stdio.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/stdlib.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/string.h
 mdp:D:/work/XCamView/XCamView/panel/camerapanel/cameralistwidget.h
 mdp:D:/work/XCamView/XCamView/panel/collapsewidget.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q17memory.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20functional.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20iterator.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20memory.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20type_traits.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20utility.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q23utility.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qabstracteventdispatcher.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qalgorithms.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qalloc.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qanystringview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qarraydata.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qarraydataops.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qarraydatapointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qassert.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qatomic.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qatomic_cxx11.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbasicatomic.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbasictimer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbindingstorage.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbytearray.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbytearrayalgorithms.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbytearraylist.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbytearrayview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qchar.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcheckedint_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcompare.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcompare_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcomparehelpers.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcompilerdetection.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qconfig.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qconstructormacros.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcontainerfwd.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcontainerinfo.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcontainertools_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcontiguouscache.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcoreapplication.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcoreapplication_platform.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcoreevent.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qdarwinhelpers.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qdatastream.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qdeadlinetimer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qdebug.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qendian.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qeventloop.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qexceptionhandling.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qflags.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qfloat16.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qforeach.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qfunctionaltools_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qfunctionpointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qgenericatomic.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qglobal.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qglobalstatic.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qhash.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qhashfunctions.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qiodevice.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qiodevicebase.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qiterable.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qiterator.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qlatin1stringview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qline.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qlist.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qlocale.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qlogging.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmalloc.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmap.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmargins.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmath.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmetacontainer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmetatype.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qminmax.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qnamespace.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qnativeinterface.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qnumeric.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qobject.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qobject_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qobjectdefs.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qobjectdefs_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qoverload.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qpair.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qpoint.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qprocessordetection.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qrect.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qrefcount.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qscopedpointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qscopeguard.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qset.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qshareddata.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qshareddata_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsharedpointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsharedpointer_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsize.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qspan.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstdlibdetection.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstring.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringalgorithms.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringbuilder.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringconverter.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringconverter_base.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringfwd.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringlist.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringliteral.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringmatcher.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringtokenizer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qswap.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsysinfo.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsystemdetection.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtaggedpointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtclasshelpermacros.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtconfiginclude.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtconfigmacros.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtcore-config.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtcoreexports.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtcoreglobal.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtdeprecationdefinitions.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtdeprecationmarkers.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtenvironmentvariables.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtextstream.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtformat_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtmetamacros.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtnoop.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtpreprocessorsupport.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtresource.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qttranslation.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qttypetraits.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtversion.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtversionchecks.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtypeinfo.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtypes.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qurl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qutf8stringview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qvariant.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qvarlengtharray.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qversiontagging.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qxptype_traits.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qyieldcpu.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qaction.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qbitmap.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qbrush.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qcolor.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qcursor.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qfont.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qfontinfo.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qfontmetrics.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qfontvariableaxis.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qguiapplication.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qguiapplication_platform.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qicon.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qimage.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qinputmethod.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qkeysequence.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpaintdevice.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpalette.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpicture.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpixelformat.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpixmap.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpolygon.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qregion.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qrgb.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qrgba64.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qtextdocument.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qtgui-config.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qtguiexports.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qtguiglobal.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qtransform.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qwindowdefs.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qwindowdefs_win.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/QApplication
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/QButtonGroup
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/QFrame
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/QLabel
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/QPushButton
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/QVBoxLayout
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/QWidget
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qabstractbutton.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qapplication.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qboxlayout.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qbuttongroup.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qframe.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qgridlayout.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qlabel.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qlayout.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qlayoutitem.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qpushbutton.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qsizepolicy.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qtwidgets-config.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qtwidgetsexports.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qtwidgetsglobal.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qwidget.h
D:/work/XCamView/XCamView/panel/camerapanel/coloradjustwidget.h
 mmc:Q_OBJECT
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/algorithm
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/array
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/atomic
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cassert
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/chrono
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/climits
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cmath
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/concurrencysal.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cstddef
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cstdint
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cstring
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/functional
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/initializer_list
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/iterator
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/limits
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/limits.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/list
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/map
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/memory
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/new
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/numeric
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/optional
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/sal.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/set
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/stdarg.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/stdbool.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/string
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/string_view
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/tuple
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/type_traits
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/unordered_map
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/unordered_set
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/utility
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vadefs.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/variant
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vcruntime.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vcruntime_string.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vector
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/version
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/yvals.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/yvals_core.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/assert.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_malloc.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_memcpy_s.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_memory.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_search.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_stdio_config.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_wstdio.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_wstdlib.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_wstring.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/errno.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/stddef.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/stdio.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/stdlib.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/string.h
 mdp:D:/work/XCamView/XCamView/panel/camerapanel/coloradjustwidget.h
 mdp:D:/work/XCamView/XCamView/panel/collapsewidget.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q17memory.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20functional.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20iterator.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20memory.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20type_traits.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20utility.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q23utility.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qabstracteventdispatcher.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qalgorithms.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qalloc.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qanystringview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qarraydata.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qarraydataops.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qarraydatapointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qassert.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qatomic.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qatomic_cxx11.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbasicatomic.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbasictimer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbindingstorage.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbytearray.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbytearrayalgorithms.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbytearraylist.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbytearrayview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qchar.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcheckedint_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcompare.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcompare_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcomparehelpers.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcompilerdetection.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qconfig.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qconstructormacros.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcontainerfwd.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcontainerinfo.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcontainertools_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcontiguouscache.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcoreapplication.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcoreapplication_platform.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcoreevent.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qdarwinhelpers.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qdatastream.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qdeadlinetimer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qdebug.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qendian.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qeventloop.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qexceptionhandling.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qflags.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qfloat16.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qforeach.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qfunctionaltools_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qfunctionpointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qgenericatomic.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qglobal.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qglobalstatic.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qhash.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qhashfunctions.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qiodevice.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qiodevicebase.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qiterable.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qiterator.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qlatin1stringview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qline.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qlist.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qlocale.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qlogging.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmalloc.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmap.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmargins.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmath.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmetacontainer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmetatype.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qminmax.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qnamespace.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qnativeinterface.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qnumeric.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qobject.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qobject_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qobjectdefs.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qobjectdefs_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qoverload.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qpair.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qpoint.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qprocessordetection.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qrect.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qrefcount.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qscopedpointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qscopeguard.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qset.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qshareddata.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qshareddata_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsharedpointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsharedpointer_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsize.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qspan.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstdlibdetection.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstring.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringalgorithms.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringbuilder.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringconverter.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringconverter_base.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringfwd.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringlist.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringliteral.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringmatcher.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringtokenizer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qswap.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsysinfo.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsystemdetection.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtaggedpointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtclasshelpermacros.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtconfiginclude.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtconfigmacros.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtcore-config.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtcoreexports.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtcoreglobal.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtdeprecationdefinitions.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtdeprecationmarkers.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtenvironmentvariables.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtextstream.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtformat_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtmetamacros.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtnoop.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtpreprocessorsupport.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtresource.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qttranslation.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qttypetraits.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtversion.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtversionchecks.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtypeinfo.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtypes.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qurl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qutf8stringview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qvariant.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qvarlengtharray.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qversiontagging.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qxptype_traits.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qyieldcpu.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qaction.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qbitmap.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qbrush.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qcolor.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qcursor.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qfont.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qfontinfo.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qfontmetrics.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qfontvariableaxis.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qguiapplication.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qguiapplication_platform.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qicon.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qimage.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qinputmethod.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qkeysequence.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpaintdevice.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpalette.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpicture.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpixelformat.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpixmap.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpolygon.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qregion.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qrgb.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qrgba64.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qtextdocument.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qtgui-config.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qtguiexports.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qtguiglobal.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qtransform.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qwindowdefs.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qwindowdefs_win.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/QApplication
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/QFrame
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/QLabel
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/QPushButton
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/QSlider
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/QWidget
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qabstractbutton.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qabstractslider.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qapplication.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qframe.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qlabel.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qpushbutton.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qsizepolicy.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qslider.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qtwidgets-config.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qtwidgetsexports.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qtwidgetsglobal.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qwidget.h
D:/work/XCamView/XCamView/panel/collapsewidget.cpp
D:/work/XCamView/XCamView/panel/chatpanel/chatwidget.cpp
D:/work/XCamView/XCamView/panel/camerapanel/exposuregainwidget.h
 mmc:Q_OBJECT
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/algorithm
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/array
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/atomic
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cassert
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/chrono
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/climits
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cmath
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/concurrencysal.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cstddef
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cstdint
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cstring
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/functional
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/initializer_list
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/iterator
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/limits
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/limits.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/list
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/map
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/memory
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/new
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/numeric
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/optional
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/sal.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/set
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/stdarg.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/stdbool.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/string
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/string_view
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/tuple
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/type_traits
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/unordered_map
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/unordered_set
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/utility
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vadefs.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/variant
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vcruntime.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vcruntime_string.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vector
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/version
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/yvals.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/yvals_core.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/assert.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_malloc.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_memcpy_s.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_memory.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_search.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_stdio_config.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_wstdio.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_wstdlib.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_wstring.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/errno.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/stddef.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/stdio.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/stdlib.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/string.h
 mdp:D:/work/XCamView/XCamView/panel/camerapanel/exposuregainwidget.h
 mdp:D:/work/XCamView/XCamView/panel/collapsewidget.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q17memory.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20functional.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20iterator.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20memory.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20type_traits.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20utility.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q23utility.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qabstracteventdispatcher.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qalgorithms.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qalloc.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qanystringview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qarraydata.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qarraydataops.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qarraydatapointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qassert.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qatomic.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qatomic_cxx11.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbasicatomic.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbasictimer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbindingstorage.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbytearray.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbytearrayalgorithms.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbytearraylist.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbytearrayview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qchar.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcheckedint_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcompare.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcompare_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcomparehelpers.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcompilerdetection.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qconfig.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qconstructormacros.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcontainerfwd.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcontainerinfo.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcontainertools_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcontiguouscache.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcoreapplication.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcoreapplication_platform.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcoreevent.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qdarwinhelpers.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qdatastream.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qdeadlinetimer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qdebug.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qendian.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qeventloop.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qexceptionhandling.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qflags.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qfloat16.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qforeach.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qfunctionaltools_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qfunctionpointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qgenericatomic.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qglobal.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qglobalstatic.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qhash.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qhashfunctions.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qiodevice.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qiodevicebase.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qiterable.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qiterator.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qlatin1stringview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qline.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qlist.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qlocale.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qlogging.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmalloc.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmap.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmargins.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmath.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmetacontainer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmetatype.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qminmax.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qnamespace.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qnativeinterface.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qnumeric.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qobject.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qobject_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qobjectdefs.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qobjectdefs_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qoverload.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qpair.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qpoint.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qprocessordetection.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qrect.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qrefcount.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qscopedpointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qscopeguard.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qset.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qshareddata.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qshareddata_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsharedpointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsharedpointer_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsize.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qspan.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstdlibdetection.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstring.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringalgorithms.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringbuilder.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringconverter.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringconverter_base.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringfwd.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringlist.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringliteral.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringmatcher.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringtokenizer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qswap.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsysinfo.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsystemdetection.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtaggedpointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtclasshelpermacros.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtconfiginclude.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtconfigmacros.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtcore-config.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtcoreexports.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtcoreglobal.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtdeprecationdefinitions.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtdeprecationmarkers.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtenvironmentvariables.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtextstream.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtformat_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtmetamacros.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtnoop.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtpreprocessorsupport.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtresource.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qttranslation.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qttypetraits.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtversion.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtversionchecks.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtypeinfo.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtypes.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qurl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qutf8stringview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qvariant.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qvarlengtharray.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qversiontagging.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qxptype_traits.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qyieldcpu.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qaction.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qbitmap.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qbrush.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qcolor.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qcursor.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qfont.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qfontinfo.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qfontmetrics.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qfontvariableaxis.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qguiapplication.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qguiapplication_platform.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qicon.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qimage.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qinputmethod.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qkeysequence.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpaintdevice.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpalette.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpicture.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpixelformat.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpixmap.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpolygon.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qregion.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qrgb.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qrgba64.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qtextdocument.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qtgui-config.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qtguiexports.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qtguiglobal.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qtransform.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qwindowdefs.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qwindowdefs_win.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/QApplication
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/QCheckBox
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/QFrame
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/QLabel
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/QPushButton
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/QSlider
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/QWidget
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qabstractbutton.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qabstractslider.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qapplication.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qcheckbox.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qframe.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qlabel.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qpushbutton.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qsizepolicy.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qslider.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qtwidgets-config.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qtwidgetsexports.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qtwidgetsglobal.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qwidget.h
D:/work/XCamView/XCamView/panel/centralpanel/displayview/imageview.h
 mmc:Q_OBJECT
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/algorithm
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/array
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/atomic
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cassert
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/chrono
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/climits
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cmath
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/concurrencysal.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cstddef
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cstdint
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cstring
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/functional
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/initializer_list
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/iterator
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/limits
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/limits.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/list
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/map
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/memory
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/new
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/numeric
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/optional
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/sal.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/set
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/stdarg.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/stdbool.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/string
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/string_view
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/tuple
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/type_traits
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/unordered_map
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/unordered_set
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/utility
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vadefs.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/variant
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vcruntime.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vcruntime_string.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vector
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/version
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/yvals.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/yvals_core.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/assert.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_malloc.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_memcpy_s.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_memory.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_search.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_stdio_config.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_wstdio.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_wstdlib.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_wstring.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/errno.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/stddef.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/stdio.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/stdlib.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/string.h
 mdp:D:/work/XCamView/XCamView/panel/centralpanel/displayview/displayview.h
 mdp:D:/work/XCamView/XCamView/panel/centralpanel/displayview/imageview.h
 mdp:D:/work/XCamView/XCamView/panel/centralpanel/view.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/QSize
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q17memory.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20functional.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20iterator.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20memory.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20type_traits.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20utility.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q23utility.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qalgorithms.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qalloc.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qanystringview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qarraydata.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qarraydataops.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qarraydatapointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qassert.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qatomic.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qatomic_cxx11.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbasicatomic.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbindingstorage.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbytearray.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbytearrayalgorithms.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbytearraylist.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbytearrayview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qchar.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcheckedint_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcompare.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcompare_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcomparehelpers.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcompilerdetection.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qconfig.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qconstructormacros.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcontainerfwd.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcontainerinfo.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcontainertools_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcontiguouscache.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qdarwinhelpers.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qdatastream.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qdebug.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qendian.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qexceptionhandling.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qflags.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qfloat16.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qforeach.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qfunctionaltools_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qfunctionpointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qgenericatomic.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qglobal.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qglobalstatic.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qhash.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qhashfunctions.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qiodevicebase.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qiterable.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qiterator.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qlatin1stringview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qline.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qlist.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qlogging.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmalloc.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmap.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmargins.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmath.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmetacontainer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmetatype.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qminmax.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qnamespace.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qnumeric.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qobject.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qobject_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qobjectdefs.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qobjectdefs_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qoverload.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qpair.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qpoint.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qprocessordetection.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qrect.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qrefcount.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qscopedpointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qscopeguard.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qset.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qshareddata.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qshareddata_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsharedpointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsharedpointer_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsize.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qspan.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstdlibdetection.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstring.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringalgorithms.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringbuilder.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringconverter.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringconverter_base.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringfwd.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringlist.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringliteral.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringmatcher.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringtokenizer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qswap.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsysinfo.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsystemdetection.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtaggedpointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtclasshelpermacros.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtconfiginclude.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtconfigmacros.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtcore-config.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtcoreexports.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtcoreglobal.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtdeprecationdefinitions.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtdeprecationmarkers.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtenvironmentvariables.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtextstream.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtformat_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtmetamacros.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtnoop.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtpreprocessorsupport.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtresource.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qttranslation.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qttypetraits.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtversion.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtversionchecks.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtypeinfo.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtypes.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qutf8stringview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qvariant.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qvarlengtharray.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qversiontagging.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qxptype_traits.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qyieldcpu.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/QImage
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qaction.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qbitmap.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qbrush.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qcolor.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qcursor.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qfont.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qfontinfo.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qfontmetrics.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qfontvariableaxis.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qicon.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qimage.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qkeysequence.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpaintdevice.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpalette.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpixelformat.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpixmap.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpolygon.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qregion.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qrgb.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qrgba64.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qtgui-config.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qtguiexports.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qtguiglobal.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qtransform.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qwindowdefs.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qwindowdefs_win.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/QScrollBar
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/QWidget
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qabstractslider.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qscrollbar.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qsizepolicy.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qtwidgets-config.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qtwidgetsexports.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qtwidgetsglobal.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qwidget.h
D:/work/XCamView/XCamView/panel/measure/tptexteditdlg.h
 mmc:Q_OBJECT
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/algorithm
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/array
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/atomic
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cassert
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/chrono
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/climits
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cmath
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/concurrencysal.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cstddef
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cstdint
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cstring
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/functional
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/initializer_list
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/iterator
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/limits
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/limits.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/list
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/map
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/memory
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/new
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/numeric
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/optional
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/sal.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/set
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/stdarg.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/stdbool.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/string
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/string_view
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/tuple
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/type_traits
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/unordered_map
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/unordered_set
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/utility
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vadefs.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/variant
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vcruntime.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vcruntime_string.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vector
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/version
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/yvals.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/yvals_core.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/assert.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_malloc.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_memcpy_s.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_memory.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_search.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_stdio_config.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_wstdio.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_wstdlib.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_wstring.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/errno.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/stddef.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/stdio.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/stdlib.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/string.h
 mdp:D:/work/XCamView/XCamView/panel/measure/tptexteditdlg.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q17memory.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20functional.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20iterator.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20memory.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20type_traits.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20utility.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q23utility.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qalgorithms.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qalloc.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qanystringview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qarraydata.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qarraydataops.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qarraydatapointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qassert.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qatomic.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qatomic_cxx11.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbasicatomic.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbindingstorage.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbytearray.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbytearrayalgorithms.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbytearraylist.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbytearrayview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qchar.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcheckedint_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcompare.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcompare_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcomparehelpers.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcompilerdetection.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qconfig.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qconstructormacros.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcontainerfwd.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcontainerinfo.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcontainertools_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcontiguouscache.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qdarwinhelpers.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qdatastream.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qdebug.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qendian.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qexceptionhandling.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qflags.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qfloat16.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qforeach.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qfunctionaltools_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qfunctionpointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qgenericatomic.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qglobal.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qglobalstatic.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qhash.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qhashfunctions.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qiodevicebase.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qiterable.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qiterator.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qlatin1stringview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qline.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qlist.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qlogging.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmalloc.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmap.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmargins.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmath.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmetacontainer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmetatype.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qminmax.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qnamespace.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qnumeric.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qobject.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qobject_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qobjectdefs.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qobjectdefs_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qoverload.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qpair.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qpoint.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qprocessordetection.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qrect.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qrefcount.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qscopedpointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qscopeguard.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qset.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qshareddata.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qshareddata_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsharedpointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsharedpointer_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsize.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qspan.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstdlibdetection.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstring.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringalgorithms.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringbuilder.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringconverter.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringconverter_base.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringfwd.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringlist.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringliteral.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringmatcher.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringtokenizer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qswap.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsysinfo.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsystemdetection.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtaggedpointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtclasshelpermacros.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtconfiginclude.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtconfigmacros.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtcore-config.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtcoreexports.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtcoreglobal.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtdeprecationdefinitions.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtdeprecationmarkers.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtenvironmentvariables.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtextstream.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtformat_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtmetamacros.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtnoop.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtpreprocessorsupport.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtresource.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qttranslation.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qttypetraits.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtversion.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtversionchecks.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtypeinfo.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtypes.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qurl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qutf8stringview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qvariant.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qvarlengtharray.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qversiontagging.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qxptype_traits.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qyieldcpu.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qaction.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qbitmap.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qbrush.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qcolor.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qcursor.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qfont.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qfontinfo.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qfontmetrics.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qfontvariableaxis.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qicon.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qimage.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qkeysequence.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpaintdevice.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpalette.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpen.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpixelformat.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpixmap.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpolygon.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qregion.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qrgb.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qrgba64.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qtextcursor.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qtextdocument.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qtextformat.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qtextoption.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qtgui-config.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qtguiexports.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qtguiglobal.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qtransform.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qwindowdefs.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qwindowdefs_win.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/QDialog
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/QTextEdit
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qabstractscrollarea.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qdialog.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qframe.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qsizepolicy.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qtextedit.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qtwidgets-config.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qtwidgetsexports.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qtwidgetsglobal.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qwidget.h
D:/work/XCamView/XCamView/panel/camerapanel/powerfrequencywidget.h
 mmc:Q_OBJECT
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/algorithm
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/array
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/atomic
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cassert
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/chrono
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/climits
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cmath
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/concurrencysal.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cstddef
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cstdint
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cstring
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/functional
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/initializer_list
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/iterator
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/limits
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/limits.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/list
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/map
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/memory
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/new
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/numeric
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/optional
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/sal.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/set
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/stdarg.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/stdbool.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/string
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/string_view
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/tuple
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/type_traits
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/unordered_map
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/unordered_set
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/utility
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vadefs.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/variant
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vcruntime.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vcruntime_string.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vector
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/version
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/yvals.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/yvals_core.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/assert.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_malloc.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_memcpy_s.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_memory.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_search.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_stdio_config.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_wstdio.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_wstdlib.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_wstring.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/errno.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/stddef.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/stdio.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/stdlib.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/string.h
 mdp:D:/work/XCamView/XCamView/panel/camerapanel/powerfrequencywidget.h
 mdp:D:/work/XCamView/XCamView/panel/collapsewidget.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q17memory.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20functional.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20iterator.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20memory.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20type_traits.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20utility.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q23utility.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qabstracteventdispatcher.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qalgorithms.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qalloc.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qanystringview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qarraydata.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qarraydataops.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qarraydatapointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qassert.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qatomic.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qatomic_cxx11.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbasicatomic.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbasictimer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbindingstorage.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbytearray.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbytearrayalgorithms.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbytearraylist.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbytearrayview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qchar.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcheckedint_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcompare.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcompare_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcomparehelpers.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcompilerdetection.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qconfig.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qconstructormacros.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcontainerfwd.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcontainerinfo.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcontainertools_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcontiguouscache.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcoreapplication.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcoreapplication_platform.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcoreevent.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qdarwinhelpers.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qdatastream.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qdeadlinetimer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qdebug.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qendian.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qeventloop.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qexceptionhandling.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qflags.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qfloat16.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qforeach.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qfunctionaltools_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qfunctionpointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qgenericatomic.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qglobal.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qglobalstatic.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qhash.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qhashfunctions.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qiodevice.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qiodevicebase.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qiterable.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qiterator.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qlatin1stringview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qline.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qlist.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qlocale.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qlogging.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmalloc.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmap.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmargins.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmath.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmetacontainer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmetatype.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qminmax.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qnamespace.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qnativeinterface.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qnumeric.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qobject.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qobject_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qobjectdefs.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qobjectdefs_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qoverload.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qpair.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qpoint.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qprocessordetection.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qrect.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qrefcount.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qscopedpointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qscopeguard.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qset.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qshareddata.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qshareddata_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsharedpointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsharedpointer_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsize.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qspan.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstdlibdetection.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstring.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringalgorithms.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringbuilder.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringconverter.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringconverter_base.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringfwd.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringlist.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringliteral.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringmatcher.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringtokenizer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qswap.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsysinfo.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsystemdetection.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtaggedpointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtclasshelpermacros.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtconfiginclude.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtconfigmacros.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtcore-config.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtcoreexports.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtcoreglobal.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtdeprecationdefinitions.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtdeprecationmarkers.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtenvironmentvariables.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtextstream.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtformat_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtmetamacros.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtnoop.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtpreprocessorsupport.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtresource.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qttranslation.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qttypetraits.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtversion.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtversionchecks.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtypeinfo.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtypes.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qurl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qutf8stringview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qvariant.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qvarlengtharray.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qversiontagging.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qxptype_traits.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qyieldcpu.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qaction.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qbitmap.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qbrush.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qcolor.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qcursor.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qfont.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qfontinfo.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qfontmetrics.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qfontvariableaxis.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qguiapplication.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qguiapplication_platform.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qicon.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qimage.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qinputmethod.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qkeysequence.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpaintdevice.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpalette.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpicture.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpixelformat.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpixmap.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpolygon.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qregion.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qrgb.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qrgba64.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qtextdocument.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qtgui-config.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qtguiexports.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qtguiglobal.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qtransform.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qwindowdefs.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qwindowdefs_win.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/QApplication
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/QButtonGroup
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/QFrame
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/QLabel
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/QPushButton
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/QRadioButton
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/QWidget
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qabstractbutton.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qapplication.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qbuttongroup.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qframe.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qlabel.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qpushbutton.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qradiobutton.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qsizepolicy.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qtwidgets-config.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qtwidgetsexports.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qtwidgetsglobal.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qwidget.h
D:/work/XCamView/XCamView/panel/camerapanel/resolutionwidget.h
 mmc:Q_OBJECT
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/algorithm
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/array
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/atomic
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cassert
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/chrono
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/climits
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cmath
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/concurrencysal.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cstddef
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cstdint
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cstring
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/functional
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/initializer_list
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/iterator
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/limits
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/limits.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/list
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/map
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/memory
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/new
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/numeric
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/optional
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/sal.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/set
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/stdarg.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/stdbool.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/string
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/string_view
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/tuple
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/type_traits
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/unordered_map
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/unordered_set
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/utility
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vadefs.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/variant
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vcruntime.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vcruntime_string.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vector
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/version
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/yvals.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/yvals_core.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/assert.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_malloc.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_memcpy_s.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_memory.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_search.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_stdio_config.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_wstdio.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_wstdlib.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_wstring.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/errno.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/stddef.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/stdio.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/stdlib.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/string.h
 mdp:D:/work/XCamView/XCamView/panel/camerapanel/resolutionwidget.h
 mdp:D:/work/XCamView/XCamView/panel/collapsewidget.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q17memory.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20functional.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20iterator.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20memory.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20type_traits.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20utility.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q23utility.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qabstracteventdispatcher.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qabstractitemmodel.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qalgorithms.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qalloc.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qanystringview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qarraydata.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qarraydataops.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qarraydatapointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qassert.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qatomic.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qatomic_cxx11.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbasicatomic.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbasictimer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbindingstorage.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbytearray.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbytearrayalgorithms.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbytearraylist.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbytearrayview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qchar.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcheckedint_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcompare.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcompare_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcomparehelpers.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcompilerdetection.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qconfig.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qconstructormacros.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcontainerfwd.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcontainerinfo.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcontainertools_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcontiguouscache.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcoreapplication.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcoreapplication_platform.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcoreevent.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qdarwinhelpers.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qdatastream.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qdeadlinetimer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qdebug.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qendian.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qeventloop.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qexceptionhandling.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qflags.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qfloat16.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qforeach.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qfunctionaltools_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qfunctionpointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qgenericatomic.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qglobal.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qglobalstatic.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qhash.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qhashfunctions.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qiodevice.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qiodevicebase.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qiterable.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qiterator.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qlatin1stringview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qline.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qlist.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qlocale.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qlogging.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmalloc.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmap.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmargins.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmath.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmetacontainer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmetatype.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qminmax.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qnamespace.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qnativeinterface.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qnumeric.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qobject.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qobject_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qobjectdefs.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qobjectdefs_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qoverload.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qpair.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qpoint.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qprocessordetection.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qrect.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qrefcount.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qregularexpression.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qscopedpointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qscopeguard.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qset.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qshareddata.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qshareddata_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsharedpointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsharedpointer_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsize.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qspan.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstdlibdetection.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstring.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringalgorithms.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringbuilder.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringconverter.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringconverter_base.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringfwd.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringlist.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringliteral.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringmatcher.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringtokenizer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qswap.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsysinfo.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsystemdetection.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtaggedpointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtclasshelpermacros.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtconfiginclude.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtconfigmacros.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtcore-config.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtcoreexports.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtcoreglobal.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtdeprecationdefinitions.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtdeprecationmarkers.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtenvironmentvariables.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtextstream.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtformat_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtmetamacros.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtnoop.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtpreprocessorsupport.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtresource.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qttranslation.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qttypetraits.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtversion.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtversionchecks.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtypeinfo.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtypes.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qurl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qutf8stringview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qvariant.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qvarlengtharray.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qversiontagging.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qxptype_traits.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qyieldcpu.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qaction.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qbitmap.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qbrush.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qcolor.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qcursor.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qfont.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qfontinfo.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qfontmetrics.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qfontvariableaxis.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qguiapplication.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qguiapplication_platform.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qicon.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qimage.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qinputmethod.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qkeysequence.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpaintdevice.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpalette.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpicture.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpixelformat.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpixmap.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpolygon.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qregion.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qrgb.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qrgba64.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qtextdocument.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qtgui-config.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qtguiexports.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qtguiglobal.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qtransform.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qvalidator.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qwindowdefs.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qwindowdefs_win.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/QApplication
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/QComboBox
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/QFrame
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/QLabel
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/QPushButton
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/QWidget
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qabstractbutton.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qabstractitemdelegate.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qabstractslider.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qabstractspinbox.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qapplication.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qcombobox.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qframe.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qlabel.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qpushbutton.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qrubberband.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qsizepolicy.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qslider.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qstyle.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qstyleoption.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qtabbar.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qtabwidget.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qtwidgets-config.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qtwidgetsexports.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qtwidgetsglobal.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qwidget.h
D:/work/XCamView/XCamView/panel/leftpanel.h
 mmc:Q_OBJECT
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/algorithm
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/array
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/atomic
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cassert
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/chrono
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/climits
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cmath
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/concurrencysal.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cstddef
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cstdint
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cstring
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/functional
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/initializer_list
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/iterator
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/limits
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/limits.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/list
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/map
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/memory
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/new
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/numeric
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/optional
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/sal.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/set
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/stdarg.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/stdbool.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/string
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/string_view
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/tuple
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/type_traits
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/unordered_map
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/unordered_set
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/utility
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vadefs.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/variant
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vcruntime.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vcruntime_string.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vector
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/version
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/yvals.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/yvals_core.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/assert.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_malloc.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_memcpy_s.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_memory.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_search.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_stdio_config.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_wstdio.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_wstdlib.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_wstring.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/errno.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/stddef.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/stdio.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/stdlib.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/string.h
 mdp:D:/work/XCamView/XCamView/panel/leftpanel.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q17memory.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20functional.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20iterator.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20memory.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20type_traits.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20utility.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q23utility.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qalgorithms.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qalloc.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qanystringview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qarraydata.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qarraydataops.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qarraydatapointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qassert.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qatomic.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qatomic_cxx11.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbasicatomic.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbindingstorage.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbytearray.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbytearrayalgorithms.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbytearraylist.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbytearrayview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qchar.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcheckedint_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcompare.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcompare_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcomparehelpers.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcompilerdetection.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qconfig.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qconstructormacros.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcontainerfwd.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcontainerinfo.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcontainertools_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcontiguouscache.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qdarwinhelpers.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qdatastream.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qdebug.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qendian.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qexceptionhandling.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qflags.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qfloat16.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qforeach.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qfunctionaltools_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qfunctionpointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qgenericatomic.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qglobal.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qglobalstatic.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qhash.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qhashfunctions.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qiodevicebase.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qiterable.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qiterator.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qlatin1stringview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qline.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qlist.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qlogging.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmalloc.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmap.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmargins.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmath.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmetacontainer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmetatype.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qminmax.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qnamespace.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qnumeric.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qobject.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qobject_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qobjectdefs.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qobjectdefs_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qoverload.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qpair.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qpoint.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qprocessordetection.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qrect.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qrefcount.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qscopedpointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qscopeguard.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qset.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qshareddata.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qshareddata_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsharedpointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsharedpointer_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsize.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qspan.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstdlibdetection.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstring.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringalgorithms.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringbuilder.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringconverter.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringconverter_base.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringfwd.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringlist.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringliteral.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringmatcher.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringtokenizer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qswap.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsysinfo.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsystemdetection.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtaggedpointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtclasshelpermacros.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtconfiginclude.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtconfigmacros.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtcore-config.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtcoreexports.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtcoreglobal.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtdeprecationdefinitions.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtdeprecationmarkers.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtenvironmentvariables.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtextstream.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtformat_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtmetamacros.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtnoop.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtpreprocessorsupport.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtresource.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qttranslation.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qttypetraits.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtversion.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtversionchecks.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtypeinfo.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtypes.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qutf8stringview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qvariant.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qvarlengtharray.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qversiontagging.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qxptype_traits.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qyieldcpu.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qaction.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qbitmap.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qbrush.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qcolor.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qcursor.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qfont.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qfontinfo.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qfontmetrics.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qfontvariableaxis.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qicon.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qimage.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qkeysequence.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpaintdevice.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpalette.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpixelformat.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpixmap.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpolygon.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qregion.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qrgb.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qrgba64.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qtgui-config.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qtguiexports.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qtguiglobal.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qtransform.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qwindowdefs.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qwindowdefs_win.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/QDockWidget
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/QTabWidget
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qdockwidget.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qsizepolicy.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qtabwidget.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qtwidgets-config.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qtwidgetsexports.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qtwidgetsglobal.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qwidget.h
D:/work/XCamView/XCamView/panel/camerapanel/whitebalancewidget.h
 mmc:Q_OBJECT
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/algorithm
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/array
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/atomic
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cassert
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/chrono
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/climits
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cmath
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/concurrencysal.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cstddef
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cstdint
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cstring
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/functional
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/initializer_list
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/iterator
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/limits
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/limits.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/list
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/map
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/memory
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/new
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/numeric
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/optional
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/sal.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/set
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/stdarg.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/stdbool.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/string
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/string_view
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/tuple
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/type_traits
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/unordered_map
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/unordered_set
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/utility
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vadefs.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/variant
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vcruntime.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vcruntime_string.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vector
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/version
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/yvals.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/yvals_core.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/assert.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_malloc.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_memcpy_s.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_memory.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_search.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_stdio_config.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_wstdio.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_wstdlib.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_wstring.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/errno.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/stddef.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/stdio.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/stdlib.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/string.h
 mdp:D:/work/XCamView/XCamView/panel/camerapanel/whitebalancewidget.h
 mdp:D:/work/XCamView/XCamView/panel/collapsewidget.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q17memory.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20functional.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20iterator.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20memory.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20type_traits.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20utility.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q23utility.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qabstracteventdispatcher.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qalgorithms.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qalloc.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qanystringview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qarraydata.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qarraydataops.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qarraydatapointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qassert.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qatomic.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qatomic_cxx11.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbasicatomic.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbasictimer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbindingstorage.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbytearray.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbytearrayalgorithms.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbytearraylist.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbytearrayview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qchar.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcheckedint_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcompare.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcompare_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcomparehelpers.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcompilerdetection.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qconfig.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qconstructormacros.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcontainerfwd.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcontainerinfo.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcontainertools_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcontiguouscache.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcoreapplication.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcoreapplication_platform.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcoreevent.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qdarwinhelpers.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qdatastream.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qdeadlinetimer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qdebug.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qendian.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qeventloop.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qexceptionhandling.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qflags.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qfloat16.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qforeach.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qfunctionaltools_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qfunctionpointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qgenericatomic.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qglobal.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qglobalstatic.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qhash.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qhashfunctions.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qiodevice.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qiodevicebase.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qiterable.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qiterator.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qlatin1stringview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qline.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qlist.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qlocale.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qlogging.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmalloc.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmap.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmargins.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmath.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmetacontainer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmetatype.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qminmax.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qnamespace.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qnativeinterface.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qnumeric.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qobject.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qobject_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qobjectdefs.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qobjectdefs_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qoverload.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qpair.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qpoint.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qprocessordetection.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qrect.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qrefcount.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qscopedpointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qscopeguard.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qset.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qshareddata.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qshareddata_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsharedpointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsharedpointer_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsize.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qspan.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstdlibdetection.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstring.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringalgorithms.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringbuilder.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringconverter.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringconverter_base.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringfwd.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringlist.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringliteral.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringmatcher.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringtokenizer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qswap.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsysinfo.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsystemdetection.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtaggedpointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtclasshelpermacros.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtconfiginclude.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtconfigmacros.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtcore-config.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtcoreexports.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtcoreglobal.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtdeprecationdefinitions.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtdeprecationmarkers.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtenvironmentvariables.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtextstream.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtformat_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtmetamacros.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtnoop.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtpreprocessorsupport.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtresource.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qttranslation.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qttypetraits.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtversion.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtversionchecks.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtypeinfo.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtypes.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qurl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qutf8stringview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qvariant.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qvarlengtharray.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qversiontagging.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qxptype_traits.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qyieldcpu.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qaction.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qbitmap.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qbrush.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qcolor.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qcursor.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qfont.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qfontinfo.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qfontmetrics.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qfontvariableaxis.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qguiapplication.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qguiapplication_platform.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qicon.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qimage.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qinputmethod.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qkeysequence.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpaintdevice.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpalette.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpicture.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpixelformat.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpixmap.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpolygon.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qregion.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qrgb.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qrgba64.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qtextdocument.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qtgui-config.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qtguiexports.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qtguiglobal.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qtransform.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qwindowdefs.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qwindowdefs_win.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/QApplication
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/QButtonGroup
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/QFrame
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/QLabel
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/QPushButton
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/QRadioButton
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/QSlider
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/QWidget
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qabstractbutton.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qabstractslider.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qapplication.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qbuttongroup.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qframe.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qlabel.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qpushbutton.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qradiobutton.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qsizepolicy.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qslider.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qtwidgets-config.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qtwidgetsexports.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qtwidgetsglobal.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qwidget.h
D:/work/XCamView/XCamView/panel/centralpanel/displayview/displayview.h
 mmc:Q_OBJECT
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/algorithm
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/array
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/atomic
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cassert
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/chrono
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/climits
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cmath
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/concurrencysal.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cstddef
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cstdint
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cstring
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/functional
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/initializer_list
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/iterator
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/limits
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/limits.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/list
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/map
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/memory
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/new
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/numeric
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/optional
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/sal.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/set
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/stdarg.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/stdbool.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/string
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/string_view
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/tuple
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/type_traits
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/unordered_map
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/unordered_set
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/utility
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vadefs.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/variant
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vcruntime.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vcruntime_string.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vector
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/version
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/yvals.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/yvals_core.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/assert.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_malloc.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_memcpy_s.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_memory.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_search.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_stdio_config.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_wstdio.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_wstdlib.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_wstring.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/errno.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/stddef.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/stdio.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/stdlib.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/string.h
 mdp:D:/work/XCamView/XCamView/panel/centralpanel/displayview/displayview.h
 mdp:D:/work/XCamView/XCamView/panel/centralpanel/view.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/QSize
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q17memory.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20functional.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20iterator.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20memory.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20type_traits.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20utility.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q23utility.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qalgorithms.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qalloc.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qanystringview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qarraydata.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qarraydataops.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qarraydatapointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qassert.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qatomic.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qatomic_cxx11.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbasicatomic.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbindingstorage.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbytearray.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbytearrayalgorithms.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbytearraylist.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbytearrayview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qchar.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcheckedint_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcompare.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcompare_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcomparehelpers.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcompilerdetection.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qconfig.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qconstructormacros.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcontainerfwd.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcontainerinfo.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcontainertools_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcontiguouscache.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qdarwinhelpers.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qdatastream.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qdebug.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qendian.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qexceptionhandling.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qflags.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qfloat16.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qforeach.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qfunctionaltools_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qfunctionpointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qgenericatomic.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qglobal.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qglobalstatic.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qhash.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qhashfunctions.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qiodevicebase.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qiterable.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qiterator.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qlatin1stringview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qline.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qlist.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qlogging.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmalloc.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmap.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmargins.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmath.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmetacontainer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmetatype.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qminmax.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qnamespace.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qnumeric.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qobject.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qobject_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qobjectdefs.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qobjectdefs_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qoverload.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qpair.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qpoint.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qprocessordetection.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qrect.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qrefcount.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qscopedpointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qscopeguard.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qset.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qshareddata.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qshareddata_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsharedpointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsharedpointer_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsize.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qspan.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstdlibdetection.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstring.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringalgorithms.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringbuilder.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringconverter.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringconverter_base.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringfwd.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringlist.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringliteral.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringmatcher.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringtokenizer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qswap.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsysinfo.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsystemdetection.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtaggedpointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtclasshelpermacros.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtconfiginclude.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtconfigmacros.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtcore-config.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtcoreexports.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtcoreglobal.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtdeprecationdefinitions.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtdeprecationmarkers.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtenvironmentvariables.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtextstream.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtformat_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtmetamacros.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtnoop.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtpreprocessorsupport.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtresource.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qttranslation.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qttypetraits.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtversion.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtversionchecks.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtypeinfo.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtypes.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qutf8stringview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qvariant.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qvarlengtharray.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qversiontagging.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qxptype_traits.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qyieldcpu.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/QImage
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qaction.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qbitmap.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qbrush.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qcolor.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qcursor.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qfont.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qfontinfo.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qfontmetrics.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qfontvariableaxis.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qicon.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qimage.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qkeysequence.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpaintdevice.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpalette.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpixelformat.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpixmap.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpolygon.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qregion.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qrgb.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qrgba64.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qtgui-config.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qtguiexports.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qtguiglobal.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qtransform.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qwindowdefs.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qwindowdefs_win.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/QScrollBar
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/QWidget
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qabstractslider.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qscrollbar.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qsizepolicy.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qtwidgets-config.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qtwidgetsexports.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qtwidgetsglobal.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qwidget.h
D:/work/XCamView/XCamView/panel/monitorpanel/monitorwidget.h
 mmc:Q_OBJECT
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/algorithm
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/array
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/atomic
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cassert
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/chrono
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/climits
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cmath
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/concurrencysal.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cstddef
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cstdint
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cstring
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/functional
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/initializer_list
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/iterator
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/limits
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/limits.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/list
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/map
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/memory
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/new
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/numeric
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/optional
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/sal.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/set
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/stdarg.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/stdbool.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/string
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/string_view
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/tuple
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/type_traits
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/unordered_map
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/unordered_set
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/utility
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vadefs.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/variant
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vcruntime.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vcruntime_string.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vector
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/version
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/yvals.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/yvals_core.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/assert.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_malloc.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_memcpy_s.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_memory.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_search.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_stdio_config.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_wstdio.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_wstdlib.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_wstring.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/errno.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/stddef.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/stdio.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/stdlib.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/string.h
 mdp:D:/work/XCamView/XCamView/panel/monitorpanel/monitorwidget.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/QList
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q17memory.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20functional.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20iterator.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20memory.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20type_traits.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20utility.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q23utility.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qabstractitemmodel.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qalgorithms.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qalloc.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qanystringview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qarraydata.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qarraydataops.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qarraydatapointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qassert.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qatomic.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qatomic_cxx11.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbasicatomic.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbindingstorage.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbytearray.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbytearrayalgorithms.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbytearraylist.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbytearrayview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qchar.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcheckedint_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcompare.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcompare_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcomparehelpers.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcompilerdetection.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qconfig.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qconstructormacros.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcontainerfwd.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcontainerinfo.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcontainertools_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcontiguouscache.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qdarwinhelpers.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qdatastream.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qdebug.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qendian.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qexceptionhandling.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qflags.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qfloat16.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qforeach.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qfunctionaltools_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qfunctionpointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qgenericatomic.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qglobal.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qglobalstatic.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qhash.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qhashfunctions.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qiodevicebase.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qitemselectionmodel.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qiterable.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qiterator.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qlatin1stringview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qline.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qlist.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qlocale.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qlogging.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmalloc.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmap.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmargins.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmath.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmetacontainer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmetatype.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qminmax.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qnamespace.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qnumeric.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qobject.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qobject_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qobjectdefs.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qobjectdefs_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qoverload.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qpair.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qpoint.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qprocessordetection.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qrect.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qrefcount.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qregularexpression.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qscopedpointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qscopeguard.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qset.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qshareddata.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qshareddata_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsharedpointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsharedpointer_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsize.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qspan.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstdlibdetection.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstring.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringalgorithms.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringbuilder.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringconverter.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringconverter_base.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringfwd.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringlist.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringliteral.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringmatcher.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringtokenizer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qswap.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsysinfo.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsystemdetection.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtaggedpointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtclasshelpermacros.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtconfiginclude.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtconfigmacros.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtcore-config.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtcoreexports.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtcoreglobal.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtdeprecationdefinitions.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtdeprecationmarkers.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtenvironmentvariables.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtextstream.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtformat_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtmetamacros.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtnoop.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtpreprocessorsupport.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtresource.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qttranslation.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qttypetraits.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtversion.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtversionchecks.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtypeinfo.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtypes.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qutf8stringview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qvariant.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qvarlengtharray.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qversiontagging.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qxptype_traits.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qyieldcpu.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qaction.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qbitmap.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qbrush.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qcolor.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qcursor.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qfont.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qfontinfo.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qfontmetrics.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qfontvariableaxis.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qicon.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qimage.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qkeysequence.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpaintdevice.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpalette.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpixelformat.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpixmap.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpolygon.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qregion.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qrgb.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qrgba64.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qtgui-config.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qtguiexports.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qtguiglobal.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qtransform.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qvalidator.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qwindowdefs.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qwindowdefs_win.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/QTreeWidget
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/QWidget
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qabstractitemdelegate.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qabstractitemview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qabstractscrollarea.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qabstractslider.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qabstractspinbox.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qframe.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qrubberband.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qsizepolicy.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qslider.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qstyle.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qstyleoption.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qtabbar.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qtabwidget.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qtreeview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qtreewidget.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qtreewidgetitemiterator.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qtwidgets-config.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qtwidgetsexports.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qtwidgetsglobal.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qwidget.h
D:/work/XCamView/XCamView/panel/centralpanel/monitorview/monitorview.h
 mmc:Q_OBJECT
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/algorithm
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/array
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/atomic
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cassert
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/chrono
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/climits
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cmath
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/concurrencysal.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cstddef
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cstdint
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cstring
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/functional
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/initializer_list
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/iterator
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/limits
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/limits.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/list
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/map
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/memory
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/new
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/numeric
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/optional
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/sal.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/set
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/stdarg.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/stdbool.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/string
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/string_view
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/tuple
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/type_traits
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/unordered_map
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/unordered_set
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/utility
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vadefs.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/variant
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vcruntime.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vcruntime_string.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vector
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/version
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/yvals.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/yvals_core.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/assert.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_malloc.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_memcpy_s.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_memory.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_search.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_stdio_config.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_wstdio.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_wstdlib.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_wstring.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/errno.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/stddef.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/stdio.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/stdlib.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/string.h
 mdp:D:/work/XCamView/XCamView/panel/centralpanel/monitorview/monitorview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/QList
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q17memory.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20functional.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20iterator.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20memory.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20type_traits.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20utility.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q23utility.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qalgorithms.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qalloc.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qanystringview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qarraydata.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qarraydataops.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qarraydatapointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qassert.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qatomic.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qatomic_cxx11.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbasicatomic.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbindingstorage.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbytearray.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbytearrayalgorithms.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbytearraylist.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbytearrayview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qchar.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcheckedint_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcompare.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcompare_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcomparehelpers.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcompilerdetection.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qconfig.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qconstructormacros.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcontainerfwd.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcontainerinfo.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcontainertools_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcontiguouscache.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qdarwinhelpers.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qdatastream.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qdebug.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qendian.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qexceptionhandling.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qflags.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qfloat16.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qforeach.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qfunctionaltools_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qfunctionpointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qgenericatomic.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qglobal.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qglobalstatic.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qhash.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qhashfunctions.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qiodevicebase.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qiterable.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qiterator.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qlatin1stringview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qline.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qlist.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qlogging.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmalloc.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmap.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmargins.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmath.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmetacontainer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmetatype.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qminmax.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qnamespace.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qnumeric.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qobject.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qobject_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qobjectdefs.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qobjectdefs_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qoverload.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qpair.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qpoint.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qprocessordetection.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qrect.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qrefcount.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qscopedpointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qscopeguard.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qset.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qshareddata.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qshareddata_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsharedpointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsharedpointer_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsize.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qspan.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstdlibdetection.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstring.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringalgorithms.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringbuilder.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringconverter.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringconverter_base.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringfwd.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringlist.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringliteral.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringmatcher.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringtokenizer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qswap.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsysinfo.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsystemdetection.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtaggedpointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtclasshelpermacros.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtconfiginclude.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtconfigmacros.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtcore-config.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtcoreexports.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtcoreglobal.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtdeprecationdefinitions.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtdeprecationmarkers.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtenvironmentvariables.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtextstream.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtformat_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtmetamacros.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtnoop.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtpreprocessorsupport.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtresource.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qttranslation.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qttypetraits.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtversion.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtversionchecks.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtypeinfo.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtypes.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qutf8stringview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qvariant.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qvarlengtharray.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qversiontagging.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qxptype_traits.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qyieldcpu.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qaction.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qbitmap.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qbrush.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qcolor.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qcursor.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qfont.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qfontinfo.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qfontmetrics.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qfontvariableaxis.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qicon.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qimage.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qkeysequence.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpaintdevice.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpalette.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpixelformat.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpixmap.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpolygon.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qregion.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qrgb.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qrgba64.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qtgui-config.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qtguiexports.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qtguiglobal.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qtransform.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qwindowdefs.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qwindowdefs_win.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/QWidget
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qsizepolicy.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qtwidgets-config.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qtwidgetsexports.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qtwidgetsglobal.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qwidget.h
D:/work/XCamView/XCamView/panel/collapsewidget.h
 mmc:Q_OBJECT
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/algorithm
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/array
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/atomic
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cassert
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/chrono
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/climits
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cmath
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/concurrencysal.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cstddef
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cstdint
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cstring
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/functional
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/initializer_list
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/iterator
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/limits
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/limits.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/list
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/map
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/memory
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/new
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/numeric
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/optional
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/sal.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/set
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/stdarg.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/stdbool.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/string
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/string_view
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/tuple
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/type_traits
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/unordered_map
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/unordered_set
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/utility
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vadefs.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/variant
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vcruntime.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vcruntime_string.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vector
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/version
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/yvals.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/yvals_core.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/assert.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_malloc.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_memcpy_s.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_memory.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_search.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_stdio_config.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_wstdio.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_wstdlib.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_wstring.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/errno.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/stddef.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/stdio.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/stdlib.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/string.h
 mdp:D:/work/XCamView/XCamView/panel/collapsewidget.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q17memory.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20functional.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20iterator.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20memory.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20type_traits.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20utility.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q23utility.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qabstracteventdispatcher.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qalgorithms.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qalloc.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qanystringview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qarraydata.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qarraydataops.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qarraydatapointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qassert.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qatomic.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qatomic_cxx11.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbasicatomic.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbasictimer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbindingstorage.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbytearray.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbytearrayalgorithms.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbytearraylist.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbytearrayview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qchar.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcheckedint_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcompare.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcompare_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcomparehelpers.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcompilerdetection.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qconfig.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qconstructormacros.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcontainerfwd.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcontainerinfo.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcontainertools_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcontiguouscache.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcoreapplication.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcoreapplication_platform.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcoreevent.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qdarwinhelpers.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qdatastream.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qdeadlinetimer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qdebug.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qendian.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qeventloop.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qexceptionhandling.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qflags.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qfloat16.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qforeach.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qfunctionaltools_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qfunctionpointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qgenericatomic.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qglobal.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qglobalstatic.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qhash.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qhashfunctions.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qiodevice.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qiodevicebase.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qiterable.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qiterator.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qlatin1stringview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qline.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qlist.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qlocale.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qlogging.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmalloc.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmap.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmargins.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmath.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmetacontainer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmetatype.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qminmax.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qnamespace.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qnativeinterface.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qnumeric.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qobject.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qobject_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qobjectdefs.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qobjectdefs_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qoverload.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qpair.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qpoint.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qprocessordetection.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qrect.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qrefcount.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qscopedpointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qscopeguard.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qset.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qshareddata.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qshareddata_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsharedpointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsharedpointer_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsize.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qspan.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstdlibdetection.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstring.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringalgorithms.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringbuilder.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringconverter.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringconverter_base.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringfwd.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringlist.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringliteral.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringmatcher.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringtokenizer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qswap.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsysinfo.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsystemdetection.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtaggedpointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtclasshelpermacros.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtconfiginclude.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtconfigmacros.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtcore-config.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtcoreexports.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtcoreglobal.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtdeprecationdefinitions.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtdeprecationmarkers.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtenvironmentvariables.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtextstream.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtformat_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtmetamacros.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtnoop.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtpreprocessorsupport.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtresource.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qttranslation.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qttypetraits.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtversion.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtversionchecks.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtypeinfo.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtypes.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qurl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qutf8stringview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qvariant.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qvarlengtharray.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qversiontagging.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qxptype_traits.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qyieldcpu.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qaction.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qbitmap.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qbrush.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qcolor.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qcursor.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qfont.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qfontinfo.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qfontmetrics.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qfontvariableaxis.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qguiapplication.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qguiapplication_platform.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qicon.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qimage.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qinputmethod.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qkeysequence.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpaintdevice.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpalette.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpicture.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpixelformat.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpixmap.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpolygon.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qregion.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qrgb.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qrgba64.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qtextdocument.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qtgui-config.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qtguiexports.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qtguiglobal.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qtransform.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qwindowdefs.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qwindowdefs_win.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/QApplication
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/QFrame
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/QLabel
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/QPushButton
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/QWidget
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qabstractbutton.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qapplication.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qframe.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qlabel.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qpushbutton.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qsizepolicy.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qtwidgets-config.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qtwidgetsexports.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qtwidgetsglobal.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qwidget.h
D:/work/XCamView/XCamView/panel/chatpanel/chatwidget.h
 mmc:Q_OBJECT
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/algorithm
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/array
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/atomic
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cassert
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/chrono
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/climits
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cmath
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/concurrencysal.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cstddef
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cstdint
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cstring
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/functional
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/initializer_list
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/iterator
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/limits
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/limits.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/list
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/map
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/memory
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/new
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/numeric
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/optional
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/sal.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/set
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/stdarg.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/stdbool.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/string
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/string_view
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/tuple
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/type_traits
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/unordered_map
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/unordered_set
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/utility
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vadefs.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/variant
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vcruntime.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vcruntime_string.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vector
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/version
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/yvals.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/yvals_core.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/assert.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_malloc.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_memcpy_s.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_memory.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_search.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_stdio_config.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_wstdio.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_wstdlib.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_wstring.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/errno.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/stddef.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/stdio.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/stdlib.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/string.h
 mdp:D:/work/XCamView/XCamView/panel/chatpanel/chatwidget.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q17memory.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20functional.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20iterator.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20memory.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20type_traits.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20utility.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q23utility.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qalgorithms.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qalloc.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qanystringview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qarraydata.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qarraydataops.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qarraydatapointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qassert.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qatomic.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qatomic_cxx11.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbasicatomic.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbindingstorage.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbytearray.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbytearrayalgorithms.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbytearraylist.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbytearrayview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qchar.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcheckedint_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcompare.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcompare_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcomparehelpers.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcompilerdetection.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qconfig.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qconstructormacros.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcontainerfwd.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcontainerinfo.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcontainertools_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcontiguouscache.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qdarwinhelpers.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qdatastream.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qdebug.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qendian.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qexceptionhandling.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qflags.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qfloat16.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qforeach.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qfunctionaltools_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qfunctionpointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qgenericatomic.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qglobal.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qglobalstatic.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qhash.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qhashfunctions.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qiodevicebase.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qiterable.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qiterator.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qlatin1stringview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qline.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qlist.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qlogging.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmalloc.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmap.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmargins.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmath.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmetacontainer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmetatype.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qminmax.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qnamespace.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qnumeric.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qobject.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qobject_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qobjectdefs.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qobjectdefs_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qoverload.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qpair.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qpoint.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qprocessordetection.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qrect.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qrefcount.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qscopedpointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qscopeguard.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qset.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qshareddata.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qshareddata_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsharedpointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsharedpointer_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsize.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qspan.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstdlibdetection.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstring.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringalgorithms.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringbuilder.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringconverter.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringconverter_base.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringfwd.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringlist.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringliteral.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringmatcher.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringtokenizer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qswap.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsysinfo.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsystemdetection.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtaggedpointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtclasshelpermacros.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtconfiginclude.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtconfigmacros.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtcore-config.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtcoreexports.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtcoreglobal.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtdeprecationdefinitions.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtdeprecationmarkers.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtenvironmentvariables.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtextstream.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtformat_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtmetamacros.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtnoop.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtpreprocessorsupport.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtresource.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qttranslation.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qttypetraits.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtversion.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtversionchecks.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtypeinfo.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtypes.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qurl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qutf8stringview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qvariant.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qvarlengtharray.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qversiontagging.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qxptype_traits.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qyieldcpu.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qaction.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qbitmap.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qbrush.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qcolor.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qcursor.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qfont.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qfontinfo.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qfontmetrics.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qfontvariableaxis.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qicon.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qimage.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qkeysequence.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpaintdevice.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpalette.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpen.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpixelformat.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpixmap.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpolygon.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qregion.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qrgb.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qrgba64.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qtextcursor.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qtextdocument.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qtextformat.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qtextoption.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qtgui-config.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qtguiexports.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qtguiglobal.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qtransform.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qwindowdefs.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qwindowdefs_win.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/QPushButton
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/QTextEdit
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/QWidget
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qabstractbutton.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qabstractscrollarea.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qframe.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qpushbutton.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qsizepolicy.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qtextedit.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qtwidgets-config.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qtwidgetsexports.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qtwidgetsglobal.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qwidget.h
D:/work/XCamView/XCamView/panel/centralpanel/monitorview/monitorview.cpp
D:/work/XCamView/XCamView/panel/monitorpanel/monitorwidget.cpp
D:/work/XCamView/XCamView/panel/centralpanel/displayview/displayview.cpp
D:/work/XCamView/XCamView/panel/measure/tpcalibrationdlg.h
 mmc:Q_OBJECT
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/algorithm
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/array
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/atomic
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cassert
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/chrono
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/climits
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cmath
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/concurrencysal.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cstddef
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cstdint
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cstring
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/functional
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/initializer_list
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/iterator
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/limits
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/limits.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/list
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/map
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/memory
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/new
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/numeric
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/optional
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/sal.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/set
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/stdarg.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/stdbool.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/string
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/string_view
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/tuple
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/type_traits
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/unordered_map
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/unordered_set
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/utility
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vadefs.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/variant
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vcruntime.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vcruntime_string.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vector
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/version
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/yvals.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/yvals_core.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/assert.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_malloc.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_memcpy_s.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_memory.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_search.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_stdio_config.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_wstdio.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_wstdlib.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_wstring.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/errno.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/stddef.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/stdio.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/stdlib.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/string.h
 mdp:D:/work/XCamView/XCamView/panel/measure/tpcalibrationdlg.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/QString
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q17memory.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20functional.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20iterator.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20memory.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20type_traits.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20utility.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q23utility.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qabstractitemmodel.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qalgorithms.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qalloc.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qanystringview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qarraydata.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qarraydataops.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qarraydatapointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qassert.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qatomic.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qatomic_cxx11.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbasicatomic.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbindingstorage.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbytearray.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbytearrayalgorithms.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbytearraylist.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbytearrayview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qchar.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcheckedint_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcompare.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcompare_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcomparehelpers.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcompilerdetection.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qconfig.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qconstructormacros.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcontainerfwd.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcontainerinfo.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcontainertools_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcontiguouscache.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qdarwinhelpers.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qdatastream.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qdebug.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qendian.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qexceptionhandling.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qflags.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qfloat16.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qforeach.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qfunctionaltools_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qfunctionpointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qgenericatomic.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qglobal.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qglobalstatic.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qhash.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qhashfunctions.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qiodevicebase.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qiterable.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qiterator.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qlatin1stringview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qline.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qlist.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qlocale.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qlogging.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmalloc.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmap.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmargins.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmath.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmetacontainer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmetatype.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qminmax.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qnamespace.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qnumeric.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qobject.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qobject_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qobjectdefs.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qobjectdefs_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qoverload.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qpair.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qpoint.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qprocessordetection.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qrect.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qrefcount.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qregularexpression.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qscopedpointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qscopeguard.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qset.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qshareddata.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qshareddata_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsharedpointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsharedpointer_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsize.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qspan.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstdlibdetection.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstring.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringalgorithms.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringbuilder.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringconverter.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringconverter_base.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringfwd.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringlist.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringliteral.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringmatcher.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringtokenizer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qswap.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsysinfo.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsystemdetection.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtaggedpointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtclasshelpermacros.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtconfiginclude.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtconfigmacros.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtcore-config.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtcoreexports.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtcoreglobal.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtdeprecationdefinitions.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtdeprecationmarkers.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtenvironmentvariables.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtextstream.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtformat_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtmetamacros.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtnoop.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtpreprocessorsupport.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtresource.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qttranslation.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qttypetraits.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtversion.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtversionchecks.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtypeinfo.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtypes.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qurl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qutf8stringview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qvariant.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qvarlengtharray.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qversiontagging.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qxptype_traits.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qyieldcpu.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qaction.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qbitmap.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qbrush.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qcolor.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qcursor.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qfont.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qfontinfo.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qfontmetrics.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qfontvariableaxis.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qicon.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qimage.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qkeysequence.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpaintdevice.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpalette.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpen.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpixelformat.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpixmap.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpolygon.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qregion.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qrgb.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qrgba64.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qtextcursor.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qtextdocument.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qtextformat.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qtextoption.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qtgui-config.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qtguiexports.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qtguiglobal.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qtransform.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qvalidator.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qwindowdefs.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qwindowdefs_win.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/QComboBox
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/QDialog
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/QLineEdit
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/QPushButton
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qabstractbutton.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qabstractitemdelegate.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qabstractslider.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qabstractspinbox.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qcombobox.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qdialog.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qframe.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qlineedit.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qpushbutton.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qrubberband.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qsizepolicy.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qslider.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qstyle.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qstyleoption.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qtabbar.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qtabwidget.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qtwidgets-config.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qtwidgetsexports.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qtwidgetsglobal.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qwidget.h
D:/work/XCamView/XCamView/panel/monitorpanel/monitordialog.h
 mmc:Q_OBJECT
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/algorithm
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/array
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/atomic
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cassert
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/chrono
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/climits
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cmath
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/concurrencysal.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cstddef
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cstdint
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/cstring
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/functional
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/initializer_list
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/iterator
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/limits
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/limits.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/list
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/map
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/memory
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/new
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/numeric
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/optional
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/sal.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/set
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/stdarg.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/stdbool.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/string
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/string_view
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/tuple
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/type_traits
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/unordered_map
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/unordered_set
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/utility
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vadefs.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/variant
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vcruntime.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vcruntime_string.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/vector
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/version
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/yvals.h
 mdp:D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/include/yvals_core.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/assert.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_malloc.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_memcpy_s.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_memory.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_search.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_stdio_config.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_wstdio.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_wstdlib.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/corecrt_wstring.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/errno.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/stddef.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/stdio.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/stdlib.h
 mdp:D:/Windows Kits/10/Include/10.0.26100.0/ucrt/string.h
 mdp:D:/work/XCamView/XCamView/panel/monitorpanel/monitordialog.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q17memory.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20functional.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20iterator.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20memory.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20type_traits.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q20utility.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/q23utility.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qalgorithms.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qalloc.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qanystringview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qarraydata.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qarraydataops.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qarraydatapointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qassert.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qatomic.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qatomic_cxx11.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbasicatomic.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbindingstorage.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbytearray.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbytearrayalgorithms.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbytearraylist.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qbytearrayview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qchar.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcheckedint_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcompare.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcompare_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcomparehelpers.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcompilerdetection.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qconfig.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qconstructormacros.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcontainerfwd.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcontainerinfo.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcontainertools_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qcontiguouscache.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qdarwinhelpers.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qdatastream.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qdebug.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qendian.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qexceptionhandling.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qflags.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qfloat16.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qforeach.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qfunctionaltools_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qfunctionpointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qgenericatomic.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qglobal.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qglobalstatic.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qhash.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qhashfunctions.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qiodevicebase.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qiterable.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qiterator.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qlatin1stringview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qline.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qlist.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qlogging.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmalloc.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmap.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmargins.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmath.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmetacontainer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qmetatype.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qminmax.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qnamespace.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qnumeric.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qobject.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qobject_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qobjectdefs.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qobjectdefs_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qoverload.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qpair.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qpoint.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qprocessordetection.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qrect.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qrefcount.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qscopedpointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qscopeguard.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qset.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qshareddata.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qshareddata_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsharedpointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsharedpointer_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsize.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qspan.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstdlibdetection.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstring.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringalgorithms.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringbuilder.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringconverter.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringconverter_base.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringfwd.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringlist.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringliteral.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringmatcher.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringtokenizer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qstringview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qswap.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsysinfo.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qsystemdetection.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtaggedpointer.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtclasshelpermacros.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtconfiginclude.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtconfigmacros.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtcore-config.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtcoreexports.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtcoreglobal.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtdeprecationdefinitions.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtdeprecationmarkers.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtenvironmentvariables.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtextstream.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtformat_impl.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtmetamacros.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtnoop.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtpreprocessorsupport.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtresource.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qttranslation.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qttypetraits.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtversion.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtversionchecks.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtypeinfo.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qtypes.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qutf8stringview.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qvariant.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qvarlengtharray.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qversiontagging.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qxptype_traits.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtCore/qyieldcpu.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qaction.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qbitmap.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qbrush.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qcolor.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qcursor.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qfont.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qfontinfo.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qfontmetrics.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qfontvariableaxis.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qicon.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qimage.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qkeysequence.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpaintdevice.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpalette.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpixelformat.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpixmap.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qpolygon.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qregion.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qrgb.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qrgba64.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qtgui-config.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qtguiexports.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qtguiglobal.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qtransform.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qwindowdefs.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtGui/qwindowdefs_win.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/QDialog
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qdialog.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qsizepolicy.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qtwidgets-config.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qtwidgetsexports.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qtwidgetsglobal.h
 mdp:E:/Qt/6.10.0/msvc2022_64/include/QtWidgets/qwidget.h
D:/work/XCamView/XCamView/global.cpp
D:/work/XCamView/XCamView/main.cpp
D:/work/XCamView/XCamView/mainwindow.cpp
D:/work/XCamView/XCamView/panel/centralpanel/view.cpp
D:/work/XCamView/XCamView/panel/camerapanel/camerapanel.cpp
D:/work/XCamView/XCamView/module/TpSaveLoader/tpsaveloader.cpp
D:/work/XCamView/XCamView/module/TpSignalConnect/TpSignalConnect.cpp
D:/work/XCamView/XCamView/module/auxrect/auxrect.cpp
D:/work/XCamView/XCamView/module/camera/camera.cpp
D:/work/XCamView/XCamView/module/camera/cameramanager.cpp
D:/work/XCamView/XCamView/module/camera/uvchamcam/uvchamloader.cpp
D:/work/XCamView/XCamView/panel/centralpanel/tptabwidget.cpp
D:/work/XCamView/XCamView/module/i18n/i18n.cpp
D:/work/XCamView/XCamView/panel/centralpanel/displayview/previewview.cpp
D:/work/XCamView/XCamView/module/tutils/tutils.cpp
D:/work/XCamView/XCamView/panel/camerapanel/cameralistwidget.cpp
D:/work/XCamView/XCamView/panel/camerapanel/coloradjustwidget.cpp
D:/work/XCamView/XCamView/panel/camerapanel/powerfrequencywidget.cpp
D:/work/XCamView/XCamView/panel/measure/tpcalibrationdlg.cpp
D:/work/XCamView/XCamView/panel/measure/tptexteditdlg.cpp
