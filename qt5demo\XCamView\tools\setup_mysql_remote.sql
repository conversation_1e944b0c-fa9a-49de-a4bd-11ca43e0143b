-- MySQL远程访问配置脚本
-- 在您的MySQL服务器(*************)上执行此脚本

-- 方案1：允许root用户远程连接（简单但不太安全）
-- 1. 允许root从局域网连接（推荐）
CREATE USER IF NOT EXISTS 'root'@'192.168.6.%' IDENTIFIED BY '123456@mysql';
GRANT ALL PRIVILEGES ON *.* TO 'root'@'192.168.6.%' WITH GRANT OPTION;

-- 2. 或者允许root从任何IP连接（不推荐，但更灵活）
-- CREATE USER IF NOT EXISTS 'root'@'%' IDENTIFIED BY '123456@mysql';
-- GRANT ALL PRIVILEGES ON *.* TO 'root'@'%' WITH GRANT OPTION;

-- 方案2：创建专用用户（更安全，推荐）
-- 删除现有用户（如果存在）
DROP USER IF EXISTS 'xcamview'@'%';
DROP USER IF EXISTS 'xcamview'@'192.168.6.%';

-- 创建专用用户
CREATE USER 'xcamview'@'192.168.6.%' IDENTIFIED BY 'XCamView2024!';
GRANT ALL PRIVILEGES ON student.* TO 'xcamview'@'192.168.6.%';

-- 3. 刷新权限
FLUSH PRIVILEGES;

-- 4. 查看创建的用户
SELECT user, host FROM mysql.user WHERE user IN ('root', 'xcamview');

-- 5. 测试权限
SHOW GRANTS FOR 'root'@'192.168.6.%';
-- SHOW GRANTS FOR 'xcamview'@'192.168.6.%';

-- 6. 检查数据库是否存在
SHOW DATABASES LIKE 'student';

-- 7. 检查admin表是否存在
USE student;
SHOW TABLES LIKE 'admin';

-- 8. 查看admin表结构
DESCRIBE admin;

-- 9. 查看admin表数据
SELECT id, username FROM admin;

-- 完成！现在其他电脑应该可以连接到此MySQL服务器了
