#ifndef EXPOSUREGAINWIDGET_H
#define EXPOSUREGAINWIDGET_H

#include <QPushButton>
#include <QCheckBox>
#include <QLabel>
#include <QSlider>
#include "../collapsewidget.h"

class ExposureGainWidget : public CollapseWidget
{
    Q_OBJECT
public:
    explicit ExposureGainWidget(QWidget* parent = nullptr);

    void Notify(int notify);

protected slots:
    void OnAECkbChanged(int state);
    void OnExpTargetChanged(int value);
    void OnExpTimeChanged(int value);
    void OnExpTimeValueLabelClicked();
    void OnExpGainChanged(int value);
    void OnExpGainValueLabelClicked();
    void OnDefBtnClicked();
    void OnCamEvent(int event);

private:
    void EnableBtns(bool bEnabled);
    void UpdateBtnsEnabled();
    void SetExpTargetLabel(int val);
    void SetExpTimeLabel(int val);
    void SetExpGainLabel(int val);
    int ConvertExpTimeToSlider(int time) const;
    int ConvertExpTimeToCamera(int time) const;
    qreal ConvertExpTimeToSliderV2(qreal time) const;
    qreal ConvertExpTimeToCameraV2(qreal time) const;

    QCheckBox*      m_aexpCkb;
    QLabel*         m_expTargetLbl;
    QLabel*         m_expTargetValueLbl;
    QSlider*        m_expTargetSlider;
    QLabel*         m_expTimeLbl;
    QPushButton*    m_expTimeValueLbl;
    QSlider*        m_expTimeSlider;
    QLabel*         m_expGainLbl;
    QPushButton*    m_expGainValueLbl;
    QSlider*        m_expGainSlider;
    QPushButton*    m_defBtn;
};

#endif
