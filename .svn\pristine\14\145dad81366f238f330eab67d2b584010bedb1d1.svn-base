#include "powerfrequencywidget.h"
#include "../../mainwindow.h"
#include <QVBoxLayout>

PowerFrequencyWidget::PowerFrequencyWidget(QWidget* parent)
    : CollapseWidget(QIcon(":/images/light.png"), g_i18n->value("ids_power_frequency"), parent)
{
    m_pDCRb = new QRadioButton(g_i18n->value("ids_dc"));
    m_pAC50Rb = new QRadioButton(g_i18n->value("ids_ac50"));
    m_pAC60Rb = new QRadioButton(g_i18n->value("ids_ac60"));
    m_pHzBGp = new QButtonGroup(this);
    m_pHzBGp->addButton(m_pAC60Rb, HZ_AC60);
    m_pHzBGp->addButton(m_pAC50Rb, HZ_AC50);
    m_pHzBGp->addButton(m_pDCRb, HZ_DC);

    QVBoxLayout *mainLyt = new QVBoxLayout();
    mainLyt->addWidget(m_pAC50Rb);
    mainLyt->addWidget(m_pAC60Rb);
    mainLyt->addWidget(m_pDCRb);
    setLayout(mainLyt);

    connect(m_pHzBGp, SIGNAL(idClicked(int)), this, SLOT(OnHzBGpClicked(int)));

    EnableBtns(false);
}

void PowerFrequencyWidget::Notify(int notify)
{
    if(NOTIFY_CAM_OPEN == notify)
    {
        EnableBtns(true);
        m_pHzBGp->blockSignals(true);
        int val = 0;
        g_pCameraManager->GetPara(CAM_PARA_HZ, &val);
        if(m_pHzBGp->button(val))
            m_pHzBGp->button(val)->setChecked(true);
        m_pHzBGp->blockSignals(false);
    }
    else if(NOTIFY_CAM_CLOSE == notify)
    {
        EnableBtns(false);
    }
}

void PowerFrequencyWidget::OnHzBGpClicked(int id)
{
    int cur = 0;
    g_pCameraManager->GetPara(CAM_PARA_HZ, &cur);
    if(cur != id)
    {
        g_pCameraManager->PutPara(CAM_PARA_HZ, id);
    }
}

void PowerFrequencyWidget::EnableBtns(bool bEnabled)
{
    m_pDCRb->setEnabled(bEnabled);
    m_pAC50Rb->setEnabled(bEnabled);
    m_pAC60Rb->setEnabled(bEnabled);
}
