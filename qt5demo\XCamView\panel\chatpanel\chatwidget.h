﻿// chatwidget.h
#ifndef CHATWIDGET_H
#define CHATWIDGET_H

#include <QWidget>
#include <QTextEdit>
#include <QPushButton>
#include <QLineEdit>
#include <QLabel>
#include <QFrame>
#include <QUdpSocket>
#include <QDataStream>
#include <QHostInfo>
#include <QKeyEvent>
#include <QStackedWidget>
#include <QTableWidget>
#include <QProgressBar>
#include <QFileDialog>
#include <QMessageBox>
#include <QTimer>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QHeaderView>
#include <QSpinBox>
#include <QDialog>
#include <QScrollBar>
#include <QDateTime>
#include <QFileInfo>
#include <QDir>
#include "../login/tpdatabasehandler.h"
#include "../../global.h"  // 包含全局定义
#include "filereceiver.h"
#include "filesender.h"

class TpdatabaseHandler;// 前向声明，用于复用login界面的数据库初始化操作

// 定义模式枚举
enum ChatMode {
    ChatMode_Chat = 0,      // 聊天模式
    ChatMode_FileTransfer   // 文件传输模式
};

// 文件传输记录结构
struct FileTransferRecord {
    QString fileName;       // 文件名
    QString peerName;       // 对方用户名
    QString peerIP;         // 对方IP
    qint64 fileSize;        // 文件大小
    QDateTime startTime;    // 开始时间
    QDateTime endTime;      // 结束时间
    bool isSuccess;         // 是否成功
    bool isSender;          // 是否为发送方
    QString errorMessage;   // 错误信息（如果失败）
    double avgSpeed;        // 平均传输速度 (MB/s)
};

class ChatWidget : public QWidget
{
    Q_OBJECT

public:
    explicit ChatWidget(TpdatabaseHandler* dbHandler, QWidget *parent = nullptr);
    explicit ChatWidget(QWidget *parent = nullptr);
    ~ChatWidget();

    void Notify(int notify);

protected:
    bool eventFilter(QObject *obj, QEvent *event) override;

private:
    // UI Setup Methods
    void setupUI();
    void setupTitleBar();
    void setupChatArea();              // 聊天区域设置
    void setupMessageArea();           // 消息显示区域设置
    void setupInputArea();             // 输入区域设置
    void setupFileTransferArea();      // 文件传输区域设置
    void setupStatusBar();

    // Mode switching
    void switchToMode(ChatMode mode);

    // Chat UI Components
    QFrame*      m_pTitleFrame;        // 标题框架
    QPushButton* m_pChatModeBtn;       // 聊天模式按钮
    QPushButton* m_pFileTransferModeBtn; // 文件传输模式按钮
    QStackedWidget* m_pStackedWidget;  // 堆叠窗口用于模式切换

    // Chat Mode Components
    QWidget*     m_pChatWidget;        // 聊天模式主窗口
    QFrame*      m_pMessageFrame;      // 消息区域框架
    QTextEdit*   m_pMsgTEdit;          // 消息显示区域
    QFrame*      m_pInputFrame;        // 输入区域框架
    QLineEdit*   m_pInputEdit;         // 输入框
    QPushButton* m_pSendBtn;           // 发送按钮

    // File Transfer Mode Components
    QWidget*     m_pFileTransferWidget; // 文件传输模式主窗口
    QFrame*      m_pUserListFrame;     // 用户列表框架
    QTableWidget* m_pUserListTable;    // 在线用户列表
    QFrame*      m_pTransferFrame;     // 传输进度框架
    QProgressBar* m_pProgressBar;      // 传输进度条
    QLabel*      m_pTransferStatusLabel; // 传输状态标签
    QPushButton* m_pCancelTransferBtn; // 取消传输按钮

    // Transfer History Components
    QFrame*      m_pHistoryFrame;      // 传输历史框架
    QTableWidget* m_pHistoryTable;     // 传输历史表格
    QPushButton* m_pClearHistoryBtn;   // 清空历史按钮
    QPushButton* m_pSettingsBtn;       // 设置按钮

    // Status Bar Components
    QFrame*      m_pStatusFrame;       // 状态栏框架
    QLabel*      m_pStatusIcon;        // 状态图标
    QLabel*      m_pStatusLabel;       // 状态文本
    QLabel*      m_pOnlineCountLabel;  // 在线人数标签

    // Current Mode
    ChatMode m_currentMode;

    // UDP Components
    QUdpSocket*  m_pUdpSocket;
    quint16      m_uPort = 12315;  // 聊天端口（与监控端口12312区分）

    // User Info
    QString m_sUserName;
    QString m_sLocalIp;
    int     m_nOnlineCount;        // 在线用户数量
    QStringList m_onlineUsers;     // 在线用户列表（用IP标识）
    QMap<QString, QString> m_userNameMap; // IP到用户名的映射

    //数据库处理器
    TpdatabaseHandler* m_dbHandler; // 数据库处理器

    // File Transfer Components
    FileReceiver* m_pFileReceiver;
    FileSender*   m_pFileSender;
    QString       m_currentTransferFile;
    QString       m_transferTargetIP;
    QTimer*       m_pRequestTimeoutTimer;  // 传输请求超时定时器
    QDateTime     m_transferStartTime;     // 传输开始时间
    qint64        m_lastTransferredBytes;  // 上次传输的字节数
    QDateTime     m_lastProgressTime;      // 上次进度更新时间

    // Transfer History
    QList<FileTransferRecord> m_transferHistory; // 传输历史记录

    // Transfer Settings
    int m_transferPort;          // 传输端口
    int m_requestTimeoutSec;     // 请求超时时间（秒）
    qint64 m_maxFileSize;        // 最大文件大小限制（字节）

    // Message Processing
    void processPendingDatagrams();
    void sendMessage(MessageType type);
    void saveMessageToDatabase(const QString&, const QString&);
    void sendTextMessage();        // 发送文本消息的专用方法
    void sendUserListResponse(const QString& targetIP);  // 发送用户列表响应

    // File Transfer Methods
    void sendFileTransferRequest(const QString& targetIP, const QString& filePath);
    void handleFileTransferRequest(const QString& senderIP, const QString& fileName, qint64 fileSize);
    void acceptFileTransfer(const QString& senderIP, const QString& savePath);
    void rejectFileTransfer(const QString& senderIP);
    void updateUserList();
    void startFileTransfer(const QString& filePath, const QString& targetIP);
    bool isTransferInProgress() const;

    // Transfer History Methods
    void addTransferRecord(const FileTransferRecord& record);
    void updateHistoryTable();
    void setupHistoryArea();

    // Utility function;
    QString getIP();
    QString getUserName();
    void updateStatus(const QString& message);
    void updateOnlineCount(int count);
    void appendFormattedMessage(const QString& time, const QString& sender, const QString& message, bool isSystem = false);
    QString getFileTypeIcon(const QString& fileName) const;

private slots:
    void onSendButtonClicked();
    void onChatModeClicked();
    void onFileTransferModeClicked();
    void onUserListDoubleClicked(int row, int column);
    void onCancelTransferClicked();
    void onRequestTimeout(); // 传输请求超时处理
    void onClearHistoryClicked(); // 清空历史记录
    void onSettingsClicked(); // 设置按钮点击

    // File Transfer Slots
    void onFileTransferProgress(qint64 sent, qint64 total, double speedMBs);
    void onFileTransferFinished();
    void onFileTransferFinished(const QString& filePath); // 重载版本，用于FileReceiver
    void onFileTransferError(const QString& error);

};

#endif
