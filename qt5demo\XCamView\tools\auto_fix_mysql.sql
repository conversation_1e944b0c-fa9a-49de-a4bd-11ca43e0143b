-- 自动修复MySQL主机名问题的脚本
-- 根据您的错误信息：Access denied for user 'root'@'DESKTOP-KDN5QHV'
-- 这个脚本会创建所有可能需要的用户

-- 1. 创建通配符用户（最简单的解决方案）
CREATE USER IF NOT EXISTS 'root'@'%' IDENTIFIED BY '123456@mysql';
GRANT ALL PRIVILEGES ON *.* TO 'root'@'%' WITH GRANT OPTION;

-- 2. 创建特定主机名用户（针对您的电脑）
CREATE USER IF NOT EXISTS 'root'@'DESKTOP-KDN5QHV' IDENTIFIED BY '123456@mysql';
GRANT ALL PRIVILEGES ON *.* TO 'root'@'DESKTOP-KDN5QHV' WITH GRANT OPTION;

-- 3. 创建IP网段用户
CREATE USER IF NOT EXISTS 'root'@'192.168.6.%' IDENTIFIED BY '123456@mysql';
GRANT ALL PRIVILEGES ON *.* TO 'root'@'192.168.6.%' WITH GRANT OPTION;

-- 4. 创建本地IP用户
CREATE USER IF NOT EXISTS 'root'@'127.0.0.1' IDENTIFIED BY '123456@mysql';
GRANT ALL PRIVILEGES ON *.* TO 'root'@'127.0.0.1' WITH GRANT OPTION;

-- 5. 确保localhost用户存在
CREATE USER IF NOT EXISTS 'root'@'localhost' IDENTIFIED BY '123456@mysql';
GRANT ALL PRIVILEGES ON *.* TO 'root'@'localhost' WITH GRANT OPTION;

-- 6. 刷新权限
FLUSH PRIVILEGES;

-- 7. 验证创建的用户
SELECT user, host FROM mysql.user WHERE user = 'root' ORDER BY host;

-- 8. 显示所有root用户的权限
SHOW GRANTS FOR 'root'@'%';
SHOW GRANTS FOR 'root'@'DESKTOP-KDN5QHV';
SHOW GRANTS FOR 'root'@'192.168.6.%';
SHOW GRANTS FOR 'root'@'127.0.0.1';
SHOW GRANTS FOR 'root'@'localhost';

-- 完成！现在应该可以从任何位置连接了
