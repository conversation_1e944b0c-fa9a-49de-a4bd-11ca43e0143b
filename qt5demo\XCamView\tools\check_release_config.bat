@echo off
echo Checking Release configuration...
echo.

set RELEASE_CONFIG="D:\work\XCamView\build-XCamView-Desktop_Qt_5_14_2_MSVC2017_64bit-Release\release\config\database.ini"

echo Release config file: %RELEASE_CONFIG%
echo.

if exist %RELEASE_CONFIG% (
    echo Config file exists. Contents:
    echo ==========================================
    type %RELEASE_CONFIG%
    echo ==========================================
) else (
    echo ERROR: Config file does not exist!
)

echo.
echo Current working directory: %CD%
echo Release directory: D:\work\XCamView\build-XCamView-Desktop_Qt_5_14_2_MSVC2017_64bit-Release\release\
echo.
pause
