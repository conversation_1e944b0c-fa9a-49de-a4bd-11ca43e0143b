﻿#include "monitorwidget.h"
#include "../../mainwindow.h"
#include <QCloseEvent>
#include <QVBoxLayout>

MonitorWidget::MonitorWidget(QWidget* parent)
    : QWidget(parent)
{
    m_pTreeWidget = new QTreeWidget(this);
    m_pTreeWidget->setHeaderLabels({""});
    QTreeWidgetItem *rootItem = new QTreeWidgetItem(m_pTreeWidget);
    rootItem->setText(0, "1~9");
    for(int i = 0; i < 9; ++i)
    {
        QTreeWidgetItem *childItem = new QTreeWidgetItem(rootItem);
        childItem->setText(0, QString("%1").arg(i + 1));
    }

    QTreeWidgetItem *rootItem2 = new QTreeWidgetItem(m_pTreeWidget);
    rootItem2->setText(0, "10~18");
    for(int i = 0; i < 9; ++i)
    {
        QTreeWidgetItem *childItem = new QTreeWidgetItem(rootItem2);
        childItem->setText(0, QString("%1").arg(i + 10));
    }

    QVBoxLayout* mainLyt = new QVBoxLayout();
    mainLyt->addWidget(m_pTreeWidget);
    setLayout(mainLyt);

    connect(m_pTreeWidget, &QTreeWidget::itemDoubleClicked, this, &MonitorWidget::OnItemDoubleClicked);
}

void MonitorWidget::Notify(int notify)
{

}

void MonitorWidget::OnItemDoubleClicked(QTreeWidgetItem *item, int column)
{
    if (item->parent() != nullptr && item->parent()->parent() == nullptr)
        g_pMainWindow->ShowMonitorDialog(item->text(column));
}

