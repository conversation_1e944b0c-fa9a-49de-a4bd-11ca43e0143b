// 测试IP连接的简单程序
#include <QApplication>
#include <QSqlDatabase>
#include <QSqlQuery>
#include <QSqlError>
#include <QDebug>

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);
    
    qDebug() << "Testing IP-based MySQL connection...";
    
    // 创建数据库连接
    QSqlDatabase db = QSqlDatabase::addDatabase("QMYSQL", "TEST_CONNECTION");
    db.setHostName("127.0.0.1");  // 强制使用IP而不是localhost
    db.setPort(3306);
    db.setDatabaseName("student");
    db.setUserName("root");
    db.setPassword("123456@mysql");
    
    // 设置连接选项强制使用TCP和IP
    QString options = "MYSQL_OPT_PROTOCOL=TCP;";
    options += "CLIENT_IGNORE_SPACE=1;";
    db.setConnectOptions(options);
    
    qDebug() << "Connection parameters:";
    qDebug() << "Host:" << db.hostName();
    qDebug() << "Port:" << db.port();
    qDebug() << "Database:" << db.databaseName();
    qDebug() << "User:" << db.userName();
    qDebug() << "Options:" << db.connectOptions();
    
    if (db.open()) {
        qDebug() << "SUCCESS: Connected to MySQL using IP address!";
        
        // 测试查询
        QSqlQuery query(db);
        if (query.exec("SELECT 1 as test")) {
            qDebug() << "SUCCESS: Test query executed successfully!";
        } else {
            qDebug() << "WARNING: Test query failed:" << query.lastError().text();
        }
        
        db.close();
    } else {
        qDebug() << "FAILED: Connection failed!";
        qDebug() << "Error:" << db.lastError().text();
        qDebug() << "Error type:" << db.lastError().type();
        qDebug() << "Error number:" << db.lastError().nativeErrorCode();
    }
    
    return 0;
}
