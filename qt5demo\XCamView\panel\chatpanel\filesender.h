#ifndef FILESENDER_H
#define FILESENDER_H

#include <QObject>
#include <QTcpServer>
#include <QTcpSocket>
#include <QFile>
#include <QTime>
#include <QHostAddress>

class FileSender : public QObject
{
    Q_OBJECT
public:
    explicit FileSender(QObject *parent = nullptr);
    ~FileSender();

    void startSending(const QString &filePath, quint16 port = 6688);
    void stopSending();

signals:
    // 传输进度 (已发送字节, 总字节, 传输速度MB/s)
    void progressUpdated(qint64 sent, qint64 total, double speedMBs);

    // 传输完成
    void transferFinished();

    // 错误信息
    void errorOccurred(const QString &errorMessage);

    // 服务状态变化
    void statusChanged(const QString &status);

private slots:
    void handleNewConnection();
    void updateTransferProgress(qint64 bytesSent);
    void handleSocketError(QAbstractSocket::SocketError socketError);

private:
    void sendFileHeader();

    QTcpServer *m_server = nullptr;
    QTcpSocket *m_clientSocket = nullptr;
    QFile *m_file = nullptr;

    qint64 m_totalBytes = 0;
    qint64 m_bytesWritten = 0;
    qint64 m_bytesToWrite = 0;
    qint64 m_payloadSize = 64 * 1024; // 64KB 分块大小

    QByteArray m_outBlock;
    QTime m_timer;
    QString m_fileName;
    quint16 m_port;
};

#endif // FILESENDER_H
