#ifndef COLORADJUSTWIDGET_H
#define COLORADJUSTWIDGET_H

#include <QPushButton>
#include <QLabel>
#include <QSlider>
#include "../collapsewidget.h"

class ColorAdjustWidget : public CollapseWidget
{
    Q_OBJECT
public:
    explicit ColorAdjustWidget(QWidget* parent = nullptr);

    void Notify(int notify);

protected slots:
    void OnHueChanged(int value);
    void OnSaturationChanged(int value);
    void OnBrightnessChanged(int value);
    void OnContrastChanged(int value);
    void OnGammaChanged(int value);
    void OnDefBtnClicked();

private:
    void EnableBtns(bool bEnabled);
    void SetHueLabel(int val);
    void SetSaturationLabel(int val);
    void SetBrightnessLabel(int val);
    void SetContrastLabel(int val);
    void SetGammaLabel(int val);

    QLabel*         m_pHueLbl;
    QLabel*         m_pHueValueLbl;
    QSlider*        m_pHueSlider;
    QLabel*         m_pSaturationLbl;
    QLabel*         m_pSaturationValueLbl;
    QSlider*        m_pSaturationSlider;
    QLabel*         m_pBrightnessLbl;
    QLabel*         m_pBrightnessValueLbl;
    QSlider*        m_pBrightnessSlider;
    QLabel*         m_pContrastLbl;
    QLabel*         m_pContrastValueLbl;
    QSlider*        m_pContrastSlider;
    QLabel*         m_pGammaLbl;
    QLabel*         m_pGammaValueLbl;
    QSlider*        m_pGammaSlider;
    QPushButton*    m_pDefBtn;
};

#endif
