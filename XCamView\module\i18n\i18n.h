#ifndef MAPLOADER_H
#define MAPLOADER_H

#include <QString>
#include <QMap>

class I18n
{
public:
    I18n();
    void init();
    const QString& value(const char* id);
    const QString& getLang() { return lang_; }
    const QMap<QString, QString>& getMap() { return map_; }
private:
    void loadXML(const QString& path, bool patch);
    void insert(const QString& id, const QString& val, bool patch);
    QMap<QString, QString> map_;
    QString lang_;
};

extern I18n* g_i18n;

#endif
