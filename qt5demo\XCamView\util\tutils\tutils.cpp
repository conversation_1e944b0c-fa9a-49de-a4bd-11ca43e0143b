#include "tutils.h"

QString singleByteCharSet(const QString& str)
{
    std::string src = str.toStdString(), dst;
    for (size_t i = 0; i < src.size(); ++i)
    {
        if (((src[i] & 0xF0) ^ 0xE0) == 0)
        {
            int old_char = (src[i] & 0xF) << 12 | ((src[i + 1] & 0x3F) << 6 | (src[i + 2] & 0x3F));
            if (old_char == 0x3000) // blank
                dst += 0x20;
            else if (old_char >= 0xFF01 && old_char <= 0xFF5E) // full char
                dst += (old_char - 0xFEE0);
            else // other 3 bytes char
            {
                dst += src[i];
                dst += src[i + 1];
                dst += src[i + 2];
            }
            i = i + 2;
        }
        else
            dst += src[i];
    }
    return QString::fromStdString(dst);
}
