﻿#include <QVBoxLayout>
#include "camerapanel.h"
#include "cameralistwidget.h"
#include "exposuregainwidget.h"
#include "resolutionwidget.h"
#include "whitebalancewidget.h"
#include "coloradjustwidget.h"
#include "powerfrequencywidget.h"

CameraPanel::CameraPanel(QWidget* parent)
    : QScrollArea(parent)
{
    setWidgetResizable(true);

    m_pCameraListWidget = new CameraListWidget();
    m_pResolutionWidget = new ResolutionWidget();
    m_pExposureGainWidget = new ExposureGainWidget();
    m_pWhiteBalanceWidget = new WhiteBalanceWidget();
    m_pColorAdjustWidget = new ColorAdjustWidget();
    m_pPowerFrequencyWidget = new PowerFrequencyWidget();

    QVBoxLayout* mainLyt = new QVBoxLayout();
    mainLyt->setMargin(0);
    mainLyt->addWidget(m_pCameraListWidget);
    mainLyt->addWidget(m_pResolutionWidget);
    mainLyt->addWidget(m_pExposureGainWidget);
    mainLyt->addWidget(m_pWhiteBalanceWidget);
    mainLyt->addWidget(m_pColorAdjustWidget);
    mainLyt->addWidget(m_pPowerFrequencyWidget);
    mainLyt->addStretch(1);

    QWidget *widget = new QWidget();
    widget->setLayout(mainLyt);
    setWidget(widget);
}

void CameraPanel::Notify(int notify)
{
    m_pCameraListWidget->Notify(notify);
    m_pResolutionWidget->Notify(notify);
    m_pExposureGainWidget->Notify(notify);
    m_pWhiteBalanceWidget->Notify(notify);
    m_pColorAdjustWidget->Notify(notify);
    m_pPowerFrequencyWidget->Notify(notify);
}

