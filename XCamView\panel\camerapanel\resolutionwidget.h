#ifndef RESOLUTIONWIDGET_H
#define RESOLUTIONWIDGET_H

#include <QLabel>
#include <QComboBox>
#include <QPushButton>
#include "../collapsewidget.h"

class ResolutionWidget : public CollapseWidget
{
    Q_OBJECT
public:
    explicit ResolutionWidget(QWidget* parent = nullptr);

    void Notify(int notify);

protected slots:
    void OnLiveResCbxActived(int index);

private:
    void EnableBtns(bool bEnabled);

    QPushButton* m_pCaptureBtn;
    QPushButton* m_pRecordBtn;
    QLabel*      m_pLiveResLbl;
    QComboBox*   m_pLiveResCbx;
};

#endif
