#ifndef AUXRECT_H
#define AUXRECT_H

#include <QWidget>
#include <QImage>
#include <QTimer>
#include <QPen>

class AuxRect : public QObject
{
    Q_OBJECT
public:
    AuxRect(const QString& label, const QColor& color, eAuxRectType type, QWidget* parent);
    QRectF getAuxRect() const;
    void setScale(double scale) { m_fScale = scale; }
    void setMaxW(int w) { m_iMaxW = w; }
    void setMaxH(int h) { m_iMaxH = h; }
    void setAuxRect(const QRectF& rect){ m_rect = rect; }
    void setHidden(bool hide){ m_bHide = hide;}
    bool isHidden() const {return m_bHide;}
    bool hasFocus(const QPointF& pos);
    void paint(QPainter& painter, const QPointF& pos);
    void mousePress(const QPointF& pos);
    void mouseMove(const QPointF& pos);
    void mouseRelease();
    ePOS mouseHover(const QPointF& pos);

signals:
    void sizeChanged(QRectF newRect, eAuxRectType type);
    void sizeChangedFini(QRectF newRect, eAuxRectType type);

private:
    ePOS getPosType(QPointF point, const QRectF& rect) const;
    void drawCornerRect(QPainter& painter, const QRectF& rect);
    void updateRect(const QPointF& pos);
    QColor reverseColor(QRgb color);
    eAuxRectType    m_type;
    QString         m_strLabel;
    ePOS            m_iPosType;
    bool            m_bHide;
    QRectF          m_rect;
    QPointF         m_ptS;
    QPointF         m_ptOffSet;
    QPen            m_pen;
    QWidget*        m_pParent;
    double          m_fScale;
    int             m_iMaxW;
    int             m_iMaxH;
};

#endif // AUXRECT_H
