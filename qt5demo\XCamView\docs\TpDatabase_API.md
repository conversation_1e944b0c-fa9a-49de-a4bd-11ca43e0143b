# 数据库通用操作API设计

## 🎯 **设计目标**
从具体业务API中抽取公共的数据库操作，提供统一的增删改查接口，减少重复代码。

## 🔧 **核心通用API类**

### **DatabaseHelper - 通用数据库操作类**

```cpp
class DatabaseHelper : public QObject
{
    Q_OBJECT

public:
    explicit DatabaseHelper(TpdatabaseHandler* dbHandler, QObject* parent = nullptr);

    // ===== 通用查询操作 =====
    
    /**
     * @brief 根据ID查询单条记录
     * @param tableName 表名
     * @param id 主键ID
     * @return QVariantMap 记录数据，空表示未找到
     */
    QVariantMap selectById(const QString& tableName, int id);
    
    /**
     * @brief 根据条件查询单条记录
     * @param tableName 表名
     * @param whereClause WHERE条件（不包含WHERE关键字）
     * @param params 参数列表
     * @return QVariantMap 记录数据
     */
    QVariantMap selectOne(const QString& tableName, const QString& whereClause, 
                         const QVariantList& params = QVariantList());
    
    /**
     * @brief 根据条件查询多条记录
     * @param tableName 表名
     * @param whereClause WHERE条件
     * @param params 参数列表
     * @param orderBy 排序字段
     * @param limit 限制数量，-1表示不限制
     * @param offset 偏移量
     * @return QList<QVariantMap> 记录列表
     */
    QList<QVariantMap> selectList(const QString& tableName, const QString& whereClause = "",
                                 const QVariantList& params = QVariantList(),
                                 const QString& orderBy = "", int limit = -1, int offset = 0);
    
    /**
     * @brief 查询记录总数
     * @param tableName 表名
     * @param whereClause WHERE条件
     * @param params 参数列表
     * @return int 记录总数
     */
    int selectCount(const QString& tableName, const QString& whereClause = "",
                   const QVariantList& params = QVariantList());

    // ===== 通用插入操作 =====
    
    /**
     * @brief 插入单条记录
     * @param tableName 表名
     * @param data 字段数据 {字段名: 值}
     * @return int 新插入记录的ID，失败返回-1
     */
    int insert(const QString& tableName, const QVariantMap& data);
    
    /**
     * @brief 批量插入记录
     * @param tableName 表名
     * @param dataList 数据列表
     * @return bool 是否成功
     */
    bool insertBatch(const QString& tableName, const QList<QVariantMap>& dataList);

    // ===== 通用更新操作 =====
    
    /**
     * @brief 根据ID更新记录
     * @param tableName 表名
     * @param id 主键ID
     * @param data 更新数据
     * @return bool 是否成功
     */
    bool updateById(const QString& tableName, int id, const QVariantMap& data);
    
    /**
     * @brief 根据条件更新记录
     * @param tableName 表名
     * @param data 更新数据
     * @param whereClause WHERE条件
     * @param params 参数列表
     * @return int 影响的行数
     */
    int updateWhere(const QString& tableName, const QVariantMap& data,
                   const QString& whereClause, const QVariantList& params = QVariantList());

    // ===== 通用删除操作 =====
    
    /**
     * @brief 根据ID删除记录
     * @param tableName 表名
     * @param id 主键ID
     * @return bool 是否成功
     */
    bool deleteById(const QString& tableName, int id);
    
    /**
     * @brief 根据条件删除记录
     * @param tableName 表名
     * @param whereClause WHERE条件
     * @param params 参数列表
     * @return int 删除的行数
     */
    int deleteWhere(const QString& tableName, const QString& whereClause,
                   const QVariantList& params = QVariantList());

    // ===== 通用存在性检查 =====
    
    /**
     * @brief 检查记录是否存在
     * @param tableName 表名
     * @param whereClause WHERE条件
     * @param params 参数列表
     * @return bool 是否存在
     */
    bool exists(const QString& tableName, const QString& whereClause,
               const QVariantList& params = QVariantList());
    
    /**
     * @brief 根据ID检查记录是否存在
     * @param tableName 表名
     * @param id 主键ID
     * @return bool 是否存在
     */
    bool existsById(const QString& tableName, int id);

    // ===== 事务操作 =====
    
    /**
     * @brief 开始事务
     * @return bool 是否成功
     */
    bool beginTransaction();
    
    /**
     * @brief 提交事务
     * @return bool 是否成功
     */
    bool commitTransaction();
    
    /**
     * @brief 回滚事务
     * @return bool 是否成功
     */
    bool rollbackTransaction();

    // ===== 高级查询操作 =====
    
    /**
     * @brief 执行JOIN查询
     * @param mainTable 主表
     * @param joinClause JOIN语句
     * @param selectFields 选择字段
     * @param whereClause WHERE条件
     * @param params 参数列表
     * @param orderBy 排序
     * @param limit 限制数量
     * @param offset 偏移量
     * @return QList<QVariantMap> 查询结果
     */
    QList<QVariantMap> selectJoin(const QString& mainTable, const QString& joinClause,
                                 const QString& selectFields = "*",
                                 const QString& whereClause = "",
                                 const QVariantList& params = QVariantList(),
                                 const QString& orderBy = "", int limit = -1, int offset = 0);

    // ===== 原生SQL执行 =====
    
    /**
     * @brief 执行原生SQL查询
     * @param sql SQL语句
     * @param params 参数列表
     * @return QList<QVariantMap> 查询结果
     */
    QList<QVariantMap> executeQuery(const QString& sql, const QVariantList& params = QVariantList());
    
    /**
     * @brief 执行原生SQL更新
     * @param sql SQL语句
     * @param params 参数列表
     * @return int 影响的行数
     */
    int executeUpdate(const QString& sql, const QVariantList& params = QVariantList());

private:
    TpdatabaseHandler* m_dbHandler;
    
    // 辅助方法
    QString buildInsertSql(const QString& tableName, const QVariantMap& data);
    QString buildUpdateSql(const QString& tableName, const QVariantMap& data);
    QString buildSelectSql(const QString& tableName, const QString& whereClause,
                          const QString& orderBy, int limit, int offset);
    QVariantList extractValues(const QVariantMap& data);
    QVariantMap resultToMap(const QSqlQuery& query);
};
```

## 💡 **使用示例**

### **1. 用户管理示例**
```cpp
// 替代原来的复杂SQL操作
DatabaseHelper dbHelper(&m_dbHandler);

// 用户登录验证
QVariantMap user = dbHelper.selectOne("users", 
    "username = ? AND password = ? AND status = 'active'", 
    {username, password});

if (!user.isEmpty()) {
    int userId = user["id"].toInt();
    QString userType = user["user_type"].toString();
    
    // 创建会话
    QVariantMap sessionData;
    sessionData["user_id"] = userId;
    sessionData["session_token"] = generateToken();
    sessionData["ip_address"] = clientIP;
    sessionData["expires_at"] = QDateTime::currentDateTime().addDays(7);
    
    int sessionId = dbHelper.insert("user_sessions", sessionData);
}

// 创建用户
QVariantMap userData;
userData["username"] = username;
userData["password"] = hashedPassword;
userData["user_type"] = userType;
userData["real_name"] = realName;
userData["status"] = "active";

int userId = dbHelper.insert("users", userData);

// 更新用户信息
QVariantMap updateData;
updateData["real_name"] = newRealName;
updateData["last_login"] = QDateTime::currentDateTime();

bool success = dbHelper.updateById("users", userId, updateData);
```

### **2. 消息管理示例**
```cpp
DatabaseHelper dbHelper(&m_dbHandler);

// 发送消息
QVariantMap messageData;
messageData["room_id"] = roomId;
messageData["sender_id"] = senderId;
messageData["message_type"] = "text";
messageData["content"] = content;
messageData["ip_address"] = ipAddress;

int messageId = dbHelper.insert("chat_messages", messageData);

// 获取聊天记录
QList<QVariantMap> messages = dbHelper.selectJoin(
    "chat_messages cm",
    "LEFT JOIN users u ON cm.sender_id = u.id",
    "cm.*, u.username, u.real_name",
    "cm.room_id = ?",
    {roomId},
    "cm.created_at DESC",
    50, 0
);

// 删除消息
bool deleted = dbHelper.deleteById("chat_messages", messageId);

// 统计消息数量
int messageCount = dbHelper.selectCount("chat_messages", "room_id = ?", {roomId});
```

### **3. 文件管理示例**
```cpp
DatabaseHelper dbHelper(&m_dbHandler);

// 上传文件
QVariantMap fileData;
fileData["uploader_id"] = uploaderId;
fileData["room_id"] = roomId;
fileData["original_name"] = originalName;
fileData["file_path"] = filePath;
fileData["file_size"] = fileSize;
fileData["file_hash"] = fileHash;

int fileId = dbHelper.insert("shared_files", fileData);

// 检查文件是否已存在
bool exists = dbHelper.exists("shared_files", "file_hash = ?", {fileHash});

// 记录下载
QVariantMap downloadData;
downloadData["file_id"] = fileId;
downloadData["downloader_id"] = downloaderId;
downloadData["download_ip"] = downloadIP;

dbHelper.insert("file_downloads", downloadData);

// 更新下载计数
dbHelper.executeUpdate("UPDATE shared_files SET download_count = download_count + 1 WHERE id = ?", {fileId});

// 获取文件列表
QList<QVariantMap> files = dbHelper.selectJoin(
    "shared_files sf",
    "LEFT JOIN users u ON sf.uploader_id = u.id",
    "sf.*, u.username as uploader_name",
    "sf.room_id = ?",
    {roomId},
    "sf.created_at DESC"
);
```

### **4. 事务操作示例**
```cpp
DatabaseHelper dbHelper(&m_dbHandler);

// 复杂操作使用事务
if (dbHelper.beginTransaction()) {
    try {
        // 创建聊天室
        QVariantMap roomData;
        roomData["room_name"] = roomName;
        roomData["created_by"] = userId;
        int roomId = dbHelper.insert("chat_rooms", roomData);
        
        // 添加创建者为管理员
        QVariantMap memberData;
        memberData["room_id"] = roomId;
        memberData["user_id"] = userId;
        memberData["role"] = "owner";
        dbHelper.insert("room_members", memberData);
        
        // 记录操作日志
        QVariantMap logData;
        logData["user_id"] = userId;
        logData["operation_type"] = "create_room";
        logData["operation_desc"] = QString("创建聊天室: %1").arg(roomName);
        dbHelper.insert("operation_logs", logData);
        
        dbHelper.commitTransaction();
        
    } catch (...) {
        dbHelper.rollbackTransaction();
        throw;
    }
}
```

## 🚀 **优势**

1. **代码复用**：消除重复的SQL拼接代码
2. **类型安全**：使用QVariantMap避免SQL注入
3. **易于维护**：统一的接口，便于修改和扩展
4. **错误处理**：集中的错误处理和日志记录
5. **性能优化**：可以在底层添加缓存和连接池

## 📝 **实现建议**

1. **第一阶段**：实现基础的增删改查方法
2. **第二阶段**：添加事务支持和JOIN查询
3. **第三阶段**：添加缓存机制和性能优化
4. **第四阶段**：添加数据验证和安全检查

这套通用API可以大大简化您的业务代码，让您专注于业务逻辑而不是SQL操作。
