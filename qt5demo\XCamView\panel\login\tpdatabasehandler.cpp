#include "tpdatabasehandler.h"

// 静态成员初始化
int TpdatabaseHandler::s_connectionCounter = 0;

TpdatabaseHandler::TpdatabaseHandler(QObject *parent) 
    : QObject(parent)
    , m_connectionState(DatabaseConnectionState::NotConnected)
    , m_connectionName(QString("XCamView_DB_Connection_%1").arg(++s_connectionCounter))
{
    qDebug() << "TpdatabaseHandler: 初始化数据库处理器，连接名:" << m_connectionName;
    
    // 加载配置
    if (!loadConfig()) {
        qWarning() << "TpdatabaseHandler: 使用默认配置";
    }
    
    qDebug() << "TpdatabaseHandler: 配置加载完成 -" << formatConnectionString();
}

TpdatabaseHandler::~TpdatabaseHandler()
{
    disconnectFromDatabase();
}

bool TpdatabaseHandler::connectToDatabase()
{
    if (m_connectionState == DatabaseConnectionState::Connected) {
        qDebug() << "TpdatabaseHandler: 数据库已连接";
        return true;
    }
    
    if (m_connectionState == DatabaseConnectionState::Connecting) {
        qWarning() << "TpdatabaseHandler: 正在连接中，请等待";
        return false;
    }
    
    m_connectionState = DatabaseConnectionState::Connecting;
    emit connectionStateChanged(m_connectionState);
    
    logConnectionAttempt();
    
    // 创建连接
    if (!createConnection()) {
        m_connectionState = DatabaseConnectionState::ConnectionFailed;
        emit connectionStateChanged(m_connectionState);
        return false;
    }
    
    // 尝试连接
    if (!m_db.open()) {
        handleConnectionError(m_db.lastError());
        m_connectionState = DatabaseConnectionState::ConnectionFailed;
        emit connectionStateChanged(m_connectionState);
        return false;
    }
    
    // 测试连接
    if (!testConnection()) {
        m_connectionState = DatabaseConnectionState::ConnectionFailed;
        emit connectionStateChanged(m_connectionState);
        return false;
    }
    
    m_connectionState = DatabaseConnectionState::Connected;
    emit connectionStateChanged(m_connectionState);
    
    logConnectionResult(true);
    return true;
}

void TpdatabaseHandler::disconnectFromDatabase()
{
    if (m_connectionState == DatabaseConnectionState::Connected && m_db.isOpen()) {
        m_db.close();
        qDebug() << "TpdatabaseHandler: 数据库连接已关闭";
    }

    // 确保连接完全关闭后再移除
    if (QSqlDatabase::contains(m_connectionName)) {
        // 先获取连接的引用，确保它被正确关闭
        QSqlDatabase db = QSqlDatabase::database(m_connectionName);
        if (db.isOpen()) {
            db.close();
        }
        QSqlDatabase::removeDatabase(m_connectionName);
        qDebug() << "TpdatabaseHandler: 数据库连接已移除";
    }

    m_connectionState = DatabaseConnectionState::NotConnected;
    emit connectionStateChanged(m_connectionState);
}

bool TpdatabaseHandler::isConnected() const
{
    return m_connectionState == DatabaseConnectionState::Connected && m_db.isOpen();
}

bool TpdatabaseHandler::validateUser(const QString &username, const QString &password)
{
    if (!isConnected()) {
        m_lastError = "数据库未连接";
        qWarning() << "TpdatabaseHandler: 尝试验证用户但数据库未连接";
        return false;
    }
    
    QSqlQuery query(m_db);
    query.prepare("SELECT id FROM admin WHERE username = :username AND password = :password");
    query.bindValue(":username", username);
    query.bindValue(":password", password);
    
    if (!query.exec()) {
        m_lastError = QString("SQL执行失败: %1").arg(query.lastError().text());
        qWarning() << "TpdatabaseHandler: 用户验证SQL执行失败:" << query.lastError().text();
        return false;
    }
    
    if (query.next()) {
        qDebug() << "TpdatabaseHandler: 用户验证成功，用户ID:" << query.value(0).toInt();
        return true;
    }
    
    qDebug() << "TpdatabaseHandler: 用户验证失败，用户名:" << username;
    return false;
}

bool TpdatabaseHandler::loadConfig(const QString &configPath)
{
    QString filePath = configPath;
    
    if (filePath.isEmpty()) {
        // 搜索配置文件
        QStringList searchPaths = getConfigSearchPaths();
        for (const QString& path : searchPaths) {
            if (QFile::exists(path)) {
                filePath = path;
                break;
            }
        }
    }
    
    if (filePath.isEmpty() || !QFile::exists(filePath)) {
        qWarning() << "TpdatabaseHandler: 配置文件未找到，使用默认配置";
        return false;
    }
    
    return loadConfigFromFile(filePath);
}

void TpdatabaseHandler::setConfig(const DatabaseConfig &config)
{
    if (!config.isValid()) {
        qWarning() << "TpdatabaseHandler: 无效的数据库配置";
        return;
    }
    
    // 如果已连接，先断开
    if (isConnected()) {
        disconnectFromDatabase();
    }
    
    m_config = config;
    qDebug() << "TpdatabaseHandler: 配置已更新 -" << formatConnectionString();
}

bool TpdatabaseHandler::createConnection()
{
    // 移除已存在的连接
    if (QSqlDatabase::contains(m_connectionName)) {
        QSqlDatabase existingDb = QSqlDatabase::database(m_connectionName);
        if (existingDb.isOpen()) {
            existingDb.close();
        }
        QSqlDatabase::removeDatabase(m_connectionName);
        qDebug() << "TpdatabaseHandler: 移除已存在的连接:" << m_connectionName;
    }

    // 创建新连接
    m_db = QSqlDatabase::addDatabase("QMYSQL", m_connectionName);

    if (!m_db.isValid()) {
        m_lastError = "无法创建MySQL数据库连接";
        qCritical() << "TpdatabaseHandler: 无法创建数据库连接";
        return false;
    }

    // 设置连接参数
    m_db.setHostName(m_config.hostName);
    m_db.setPort(m_config.port);
    m_db.setDatabaseName(m_config.databaseName);
    m_db.setUserName(m_config.userName);
    m_db.setPassword(m_config.password);

    // 设置连接选项（可以根据需要添加其他选项）
    // 注意：MYSQL_OPT_RECONNECT 已被弃用，不再使用
    // m_db.setConnectOptions("CLIENT_IGNORE_SPACE=1");

    qDebug() << "TpdatabaseHandler: 数据库连接对象创建成功";
    return true;
}

bool TpdatabaseHandler::testConnection()
{
    QSqlQuery testQuery(m_db);

    // 使用简单的测试查询
    if (!testQuery.exec("SELECT 1 as test")) {
        m_lastError = QString("连接测试失败: %1").arg(testQuery.lastError().text());
        qWarning() << "TpdatabaseHandler: 连接测试失败:" << testQuery.lastError().text();
        return false;
    }

    if (testQuery.next()) {
        qDebug() << "TpdatabaseHandler: 连接测试成功";
        qDebug() << "  - 测试结果:" << testQuery.value("test").toInt();

        // 尝试获取更多信息（如果失败也不影响连接测试）
        QSqlQuery infoQuery(m_db);
        if (infoQuery.exec("SELECT NOW() as server_time, USER() as current_user")) {
            if (infoQuery.next()) {
                qDebug() << "  - 服务器时间:" << infoQuery.value("server_time").toString();
                qDebug() << "  - 当前用户:" << infoQuery.value("current_user").toString();
            }
        } else {
            qDebug() << "  - 无法获取服务器详细信息（不影响连接）";
        }

        return true;
    }

    m_lastError = "连接测试查询无结果";
    return false;
}

void TpdatabaseHandler::handleConnectionError(const QSqlError &error)
{
    m_lastError = error.text();
    
    qCritical() << "TpdatabaseHandler: 数据库连接失败";
    qCritical() << "  - 错误信息:" << error.text();
    qCritical() << "  - 错误类型:" << error.type();
    qCritical() << "  - 错误代码:" << error.nativeErrorCode();
    
    // 分析错误并提供解决方案
    QString errorMsg = error.text();
    if (errorMsg.contains("Access denied") && errorMsg.contains("@")) {
        QRegExp rx("'[^']*'@'([^']*)'");
        if (rx.indexIn(errorMsg) != -1) {
            QString actualHostname = rx.cap(1);
            qCritical() << "TpdatabaseHandler: MySQL反向DNS解析问题";
            qCritical() << "  - MySQL识别的主机名:" << actualHostname;
            qCritical() << "  - 解决方案1: CREATE USER '" + m_config.userName + "'@'%' IDENTIFIED BY '" + m_config.password + "';";
            qCritical() << "  - 解决方案2: CREATE USER '" + m_config.userName + "'@'" + actualHostname + "' IDENTIFIED BY '" + m_config.password + "';";
            qCritical() << "  - 解决方案3: 在MySQL配置中添加 skip-name-resolve";
        }
    } else if (errorMsg.contains("Can't connect")) {
        qCritical() << "TpdatabaseHandler: 网络连接问题";
        qCritical() << "  - 检查MySQL服务器是否运行";
        qCritical() << "  - 检查网络连通性: ping" << m_config.hostName;
        qCritical() << "  - 检查端口是否开放: telnet" << m_config.hostName << m_config.port;
    } else if (errorMsg.contains("Unknown database")) {
        qCritical() << "TpdatabaseHandler: 数据库不存在";
        qCritical() << "  - 检查数据库名称:" << m_config.databaseName;
    }
    
    emit errorOccurred(m_lastError);
}

QStringList TpdatabaseHandler::getConfigSearchPaths() const
{
    QStringList paths;
    QString appDir = QApplication::applicationDirPath();
    QString currentDir = QDir::currentPath();

    // 按优先级排序
    paths << appDir + "/config/database.ini";
    paths << appDir + "/database.ini";
    paths << currentDir + "/config/database.ini";
    paths << currentDir + "/database.ini";

    qDebug() << "TpdatabaseHandler: 配置文件搜索路径:";
    for (const QString& path : paths) {
        qDebug() << "  - " << path << (QFile::exists(path) ? "[存在]" : "[不存在]");
    }

    return paths;
}

bool TpdatabaseHandler::loadConfigFromFile(const QString &filePath)
{
    qDebug() << "TpdatabaseHandler: 从文件加载配置:" << filePath;

    QSettings settings(filePath, QSettings::IniFormat);
    settings.beginGroup("Database");

    DatabaseConfig config;
    config.hostName = settings.value("host", config.hostName).toString();
    config.port = settings.value("port", config.port).toInt();
    config.databaseName = settings.value("database", config.databaseName).toString();
    config.userName = settings.value("username", config.userName).toString();
    config.password = settings.value("password", config.password).toString();

    settings.endGroup();

    if (!config.isValid()) {
        qWarning() << "TpdatabaseHandler: 配置文件中的配置无效";
        return false;
    }

    m_config = config;
    qDebug() << "TpdatabaseHandler: 配置加载成功 -" << formatConnectionString();
    return true;
}

QString TpdatabaseHandler::formatConnectionString() const
{
    return QString("Host:%1:%2 Database:%3 User:%4")
           .arg(m_config.hostName)
           .arg(m_config.port)
           .arg(m_config.databaseName)
           .arg(m_config.userName);
}

void TpdatabaseHandler::logConnectionAttempt() const
{
    qDebug() << "TpdatabaseHandler: 尝试连接数据库";
    qDebug() << "  - 连接字符串:" << formatConnectionString();
    qDebug() << "  - 连接名:" << m_connectionName;
}

void TpdatabaseHandler::logConnectionResult(bool success) const
{
    if (success) {
        qDebug() << "TpdatabaseHandler: 数据库连接成功!";
    } else {
        qCritical() << "TpdatabaseHandler: 数据库连接失败!";
    }
}
