-- 快速修复MySQL远程连接问题
-- 在您的MySQL服务器上以root身份执行

-- 1. 查看当前用户
SELECT user, host FROM mysql.user WHERE user = 'root';

-- 2. 创建允许远程连接的root用户
-- 方法1：允许从特定主机名连接（针对当前问题）
CREATE USER IF NOT EXISTS 'root'@'DESKTOP-KDN5QHV' IDENTIFIED BY '123456@mysql';
GRANT ALL PRIVILEGES ON *.* TO 'root'@'DESKTOP-KDN5QHV' WITH GRANT OPTION;

-- 方法2：允许从特定网段连接
CREATE USER IF NOT EXISTS 'root'@'192.168.6.%' IDENTIFIED BY '123456@mysql';
GRANT ALL PRIVILEGES ON *.* TO 'root'@'192.168.6.%' WITH GRANT OPTION;

-- 方法3：允许从任何主机连接（最灵活的解决方案）
CREATE USER IF NOT EXISTS 'root'@'%' IDENTIFIED BY '123456@mysql';
GRANT ALL PRIVILEGES ON *.* TO 'root'@'%' WITH GRANT OPTION;

-- 3. 刷新权限
FLUSH PRIVILEGES;

-- 4. 验证用户创建成功
SELECT user, host FROM mysql.user WHERE user = 'root';

-- 5. 查看权限
SHOW GRANTS FOR 'root'@'DESKTOP-KDN5QHV';
SHOW GRANTS FOR 'root'@'192.168.6.%';
SHOW GRANTS FOR 'root'@'%';

-- 6. 测试连接（可选）
-- 从命令行测试：mysql -h ************* -u root -p

-- 执行完成后，重启MySQL服务：
-- Windows: net stop mysql && net start mysql
-- Linux: sudo systemctl restart mysql
