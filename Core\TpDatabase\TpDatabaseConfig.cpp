#include "TpDatabaseConfig.h"
#include <QApplication>
#include <QDir>
#include <QFile>
#include <QStandardPaths>
#include <QDebug>

// TpDatabasePoolConfig 实现
bool TpDatabasePoolConfig::isValid() const
{
    return minConnections > 0 && 
           maxConnections >= minConnections && 
           connectionTimeout > 0 && 
           idleTimeout > 0 && 
           healthCheckInterval > 0;
}

// TpDatabasePerformanceConfig 实现
bool TpDatabasePerformanceConfig::isValid() const
{
    return queryCacheSize > 0 && 
           queryCacheTimeout > 0 && 
           slowQueryThreshold > 0 && 
           batchSize > 0;
}

// TpDatabaseSecurityConfig 实现
bool TpDatabaseSecurityConfig::isValid() const
{
    if (enableSensitiveDataEncryption && encryptionKey.isEmpty()) {
        return false;
    }
    return true;
}

// TpDatabaseConfig 实现
TpDatabaseConfig::TpDatabaseConfig()
    : m_hostName("localhost")
    , m_port(3306)
    , m_driverName("QMYSQL")
{
}

TpDatabaseConfig::TpDatabaseConfig(const TpDatabaseConfig& other)
    : m_hostName(other.m_hostName)
    , m_port(other.m_port)
    , m_databaseName(other.m_databaseName)
    , m_userName(other.m_userName)
    , m_password(other.m_password)
    , m_driverName(other.m_driverName)
    , m_connectOptions(other.m_connectOptions)
    , m_poolConfig(other.m_poolConfig)
    , m_performanceConfig(other.m_performanceConfig)
    , m_securityConfig(other.m_securityConfig)
{
}

TpDatabaseConfig& TpDatabaseConfig::operator=(const TpDatabaseConfig& other)
{
    if (this != &other) {
        m_hostName = other.m_hostName;
        m_port = other.m_port;
        m_databaseName = other.m_databaseName;
        m_userName = other.m_userName;
        m_password = other.m_password;
        m_driverName = other.m_driverName;
        m_connectOptions = other.m_connectOptions;
        m_poolConfig = other.m_poolConfig;
        m_performanceConfig = other.m_performanceConfig;
        m_securityConfig = other.m_securityConfig;
    }
    return *this;
}

TpDatabaseConfig::~TpDatabaseConfig()
{
}

bool TpDatabaseConfig::isValid() const
{
    return !m_hostName.isEmpty() && 
           m_port > 0 && 
           !m_databaseName.isEmpty() && 
           !m_userName.isEmpty() && 
           !m_driverName.isEmpty() &&
           m_poolConfig.isValid() &&
           m_performanceConfig.isValid() &&
           m_securityConfig.isValid();
}

bool TpDatabaseConfig::loadFromFile(const QString& filePath)
{
    QString configPath = filePath;
    
    if (configPath.isEmpty()) {
        // 自动搜索配置文件
        QStringList searchPaths = getConfigSearchPaths();
        for (const QString& path : searchPaths) {
            if (QFile::exists(path)) {
                configPath = path;
                break;
            }
        }
    }
    
    if (configPath.isEmpty() || !QFile::exists(configPath)) {
        qWarning() << "TpDatabaseConfig: 配置文件未找到";
        return false;
    }
    
    qDebug() << "TpDatabaseConfig: 从文件加载配置:" << configPath;
    
    QSettings settings(configPath, QSettings::IniFormat);
    
    // 加载各部分配置
    loadBasicConfigFromSettings(settings);
    loadPoolConfigFromSettings(settings);
    loadPerformanceConfigFromSettings(settings);
    loadSecurityConfigFromSettings(settings);
    
    qDebug() << "TpDatabaseConfig: 配置加载完成 -" << formatConnectionString();
    return true;
}

bool TpDatabaseConfig::loadFromEnvironment()
{
    qDebug() << "TpDatabaseConfig: 从环境变量加载配置";
    
    // 基础连接配置
    QString envHost = qgetenv("TP_DB_HOST");
    if (!envHost.isEmpty()) m_hostName = envHost;
    
    QString envPort = qgetenv("TP_DB_PORT");
    if (!envPort.isEmpty()) m_port = envPort.toInt();
    
    QString envDatabase = qgetenv("TP_DB_DATABASE");
    if (!envDatabase.isEmpty()) m_databaseName = envDatabase;
    
    QString envUser = qgetenv("TP_DB_USER");
    if (!envUser.isEmpty()) m_userName = envUser;
    
    QString envPassword = qgetenv("TP_DB_PASSWORD");
    if (!envPassword.isEmpty()) m_password = envPassword;
    
    QString envDriver = qgetenv("TP_DB_DRIVER");
    if (!envDriver.isEmpty()) m_driverName = envDriver;
    
    // 连接池配置
    QString envMinConn = qgetenv("TP_DB_MIN_CONNECTIONS");
    if (!envMinConn.isEmpty()) m_poolConfig.minConnections = envMinConn.toInt();
    
    QString envMaxConn = qgetenv("TP_DB_MAX_CONNECTIONS");
    if (!envMaxConn.isEmpty()) m_poolConfig.maxConnections = envMaxConn.toInt();
    
    qDebug() << "TpDatabaseConfig: 环境变量配置加载完成";
    return true;
}

bool TpDatabaseConfig::saveToFile(const QString& filePath) const
{
    if (filePath.isEmpty()) {
        qWarning() << "TpDatabaseConfig: 保存路径为空";
        return false;
    }
    
    // 确保目录存在
    QFileInfo fileInfo(filePath);
    QDir dir = fileInfo.absoluteDir();
    if (!dir.exists()) {
        dir.mkpath(".");
    }
    
    QSettings settings(filePath, QSettings::IniFormat);
    
    // 保存各部分配置
    saveBasicConfigToSettings(settings);
    savePoolConfigToSettings(settings);
    savePerformanceConfigToSettings(settings);
    saveSecurityConfigToSettings(settings);
    
    settings.sync();
    
    qDebug() << "TpDatabaseConfig: 配置已保存到:" << filePath;
    return true;
}

QStringList TpDatabaseConfig::getConfigSearchPaths() const
{
    QStringList paths;
    QString appDir = QApplication::applicationDirPath();
    QString currentDir = QDir::currentPath();
    
    // 按优先级排序
    paths << appDir + "/config/database.ini";
    paths << appDir + "/database.ini";
    paths << currentDir + "/config/database.ini";
    paths << currentDir + "/database.ini";
    
    // 添加系统配置目录
    QString configDir = QStandardPaths::writableLocation(QStandardPaths::ConfigLocation);
    paths << configDir + "/XCamView/database.ini";
    
    return paths;
}

QString TpDatabaseConfig::formatConnectionString() const
{
    return QString("Host:%1:%2 Database:%3 User:%4 Driver:%5")
           .arg(m_hostName)
           .arg(m_port)
           .arg(m_databaseName)
           .arg(m_userName)
           .arg(m_driverName);
}

TpDatabaseConfig TpDatabaseConfig::createDefault()
{
    TpDatabaseConfig config;
    config.setHostName("localhost");
    config.setPort(3306);
    config.setDatabaseName("test");
    config.setUserName("root");
    config.setPassword("");
    config.setDriverName("QMYSQL");
    
    return config;
}

void TpDatabaseConfig::loadBasicConfigFromSettings(QSettings& settings)
{
    settings.beginGroup("Database");
    
    m_hostName = settings.value("host", m_hostName).toString();
    m_port = settings.value("port", m_port).toInt();
    m_databaseName = settings.value("database", m_databaseName).toString();
    m_userName = settings.value("username", m_userName).toString();
    m_password = settings.value("password", m_password).toString();
    m_driverName = settings.value("driver", m_driverName).toString();
    m_connectOptions = settings.value("options", m_connectOptions).toString();
    
    settings.endGroup();
}

void TpDatabaseConfig::loadPoolConfigFromSettings(QSettings& settings)
{
    settings.beginGroup("ConnectionPool");
    
    m_poolConfig.minConnections = settings.value("minConnections", m_poolConfig.minConnections).toInt();
    m_poolConfig.maxConnections = settings.value("maxConnections", m_poolConfig.maxConnections).toInt();
    m_poolConfig.connectionTimeout = settings.value("connectionTimeout", m_poolConfig.connectionTimeout).toInt();
    m_poolConfig.idleTimeout = settings.value("idleTimeout", m_poolConfig.idleTimeout).toInt();
    m_poolConfig.healthCheckInterval = settings.value("healthCheckInterval", m_poolConfig.healthCheckInterval).toInt();
    m_poolConfig.enableHealthCheck = settings.value("enableHealthCheck", m_poolConfig.enableHealthCheck).toBool();
    
    settings.endGroup();
}

void TpDatabaseConfig::loadPerformanceConfigFromSettings(QSettings& settings)
{
    settings.beginGroup("Performance");
    
    m_performanceConfig.enableQueryCache = settings.value("enableQueryCache", m_performanceConfig.enableQueryCache).toBool();
    m_performanceConfig.queryCacheSize = settings.value("queryCacheSize", m_performanceConfig.queryCacheSize).toInt();
    m_performanceConfig.queryCacheTimeout = settings.value("queryCacheTimeout", m_performanceConfig.queryCacheTimeout).toInt();
    m_performanceConfig.enableSlowQueryLog = settings.value("enableSlowQueryLog", m_performanceConfig.enableSlowQueryLog).toBool();
    m_performanceConfig.slowQueryThreshold = settings.value("slowQueryThreshold", m_performanceConfig.slowQueryThreshold).toInt();
    m_performanceConfig.enableBatchOptimization = settings.value("enableBatchOptimization", m_performanceConfig.enableBatchOptimization).toBool();
    m_performanceConfig.batchSize = settings.value("batchSize", m_performanceConfig.batchSize).toInt();
    
    settings.endGroup();
}

void TpDatabaseConfig::loadSecurityConfigFromSettings(QSettings& settings)
{
    settings.beginGroup("Security");
    
    m_securityConfig.enableSqlInjectionProtection = settings.value("enableSqlInjectionProtection", m_securityConfig.enableSqlInjectionProtection).toBool();
    m_securityConfig.enableInputValidation = settings.value("enableInputValidation", m_securityConfig.enableInputValidation).toBool();
    m_securityConfig.enableSensitiveDataEncryption = settings.value("enableSensitiveDataEncryption", m_securityConfig.enableSensitiveDataEncryption).toBool();
    m_securityConfig.encryptionKey = settings.value("encryptionKey", m_securityConfig.encryptionKey).toString();
    m_securityConfig.enableAccessControl = settings.value("enableAccessControl", m_securityConfig.enableAccessControl).toBool();
    
    settings.endGroup();
}

void TpDatabaseConfig::saveBasicConfigToSettings(QSettings& settings) const
{
    settings.beginGroup("Database");
    
    settings.setValue("host", m_hostName);
    settings.setValue("port", m_port);
    settings.setValue("database", m_databaseName);
    settings.setValue("username", m_userName);
    settings.setValue("password", m_password);
    settings.setValue("driver", m_driverName);
    settings.setValue("options", m_connectOptions);
    
    settings.endGroup();
}

void TpDatabaseConfig::savePoolConfigToSettings(QSettings& settings) const
{
    settings.beginGroup("ConnectionPool");
    
    settings.setValue("minConnections", m_poolConfig.minConnections);
    settings.setValue("maxConnections", m_poolConfig.maxConnections);
    settings.setValue("connectionTimeout", m_poolConfig.connectionTimeout);
    settings.setValue("idleTimeout", m_poolConfig.idleTimeout);
    settings.setValue("healthCheckInterval", m_poolConfig.healthCheckInterval);
    settings.setValue("enableHealthCheck", m_poolConfig.enableHealthCheck);
    
    settings.endGroup();
}

void TpDatabaseConfig::savePerformanceConfigToSettings(QSettings& settings) const
{
    settings.beginGroup("Performance");
    
    settings.setValue("enableQueryCache", m_performanceConfig.enableQueryCache);
    settings.setValue("queryCacheSize", m_performanceConfig.queryCacheSize);
    settings.setValue("queryCacheTimeout", m_performanceConfig.queryCacheTimeout);
    settings.setValue("enableSlowQueryLog", m_performanceConfig.enableSlowQueryLog);
    settings.setValue("slowQueryThreshold", m_performanceConfig.slowQueryThreshold);
    settings.setValue("enableBatchOptimization", m_performanceConfig.enableBatchOptimization);
    settings.setValue("batchSize", m_performanceConfig.batchSize);
    
    settings.endGroup();
}

void TpDatabaseConfig::saveSecurityConfigToSettings(QSettings& settings) const
{
    settings.beginGroup("Security");
    
    settings.setValue("enableSqlInjectionProtection", m_securityConfig.enableSqlInjectionProtection);
    settings.setValue("enableInputValidation", m_securityConfig.enableInputValidation);
    settings.setValue("enableSensitiveDataEncryption", m_securityConfig.enableSensitiveDataEncryption);
    settings.setValue("encryptionKey", m_securityConfig.encryptionKey);
    settings.setValue("enableAccessControl", m_securityConfig.enableAccessControl);
    
    settings.endGroup();
}
