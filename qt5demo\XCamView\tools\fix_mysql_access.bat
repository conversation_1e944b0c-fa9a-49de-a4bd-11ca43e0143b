@echo off
echo MySQL Access Fix Tool
echo =====================
echo.

echo This tool will fix the MySQL access denied error.
echo Current computer name: %COMPUTERNAME%
echo.

echo Please enter your MySQL root password:
set /p MYSQL_PASSWORD="MySQL root password: "

echo.
echo Executing MySQL commands to fix access...

mysql -u root -p%MYSQL_PASSWORD% -e "CREATE USER IF NOT EXISTS 'root'@'%COMPUTERNAME%' IDENTIFIED BY '123456@mysql';"
mysql -u root -p%MYSQL_PASSWORD% -e "GRANT ALL PRIVILEGES ON *.* TO 'root'@'%COMPUTERNAME%' WITH GRANT OPTION;"
mysql -u root -p%MYSQL_PASSWORD% -e "CREATE USER IF NOT EXISTS 'root'@'%%' IDENTIFIED BY '123456@mysql';"
mysql -u root -p%MY<PERSON>QL_PASSWORD% -e "GRANT ALL PRIVILEGES ON *.* TO 'root'@'%%' WITH GRANT OPTION;"
mysql -u root -p%MYSQL_PASSWORD% -e "FLUSH PRIVILEGES;"

echo.
echo Verifying users created:
mysql -u root -p%MYSQL_PASSWORD% -e "SELECT user, host FROM mysql.user WHERE user = 'root';"

echo.
echo MySQL access fix completed!
echo You can now run XCamView.exe
echo.
pause
