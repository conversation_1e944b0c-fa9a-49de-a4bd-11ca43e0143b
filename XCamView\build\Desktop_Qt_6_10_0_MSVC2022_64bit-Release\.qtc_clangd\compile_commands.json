[{"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-DWIN32", "-D_WINDOWS", "-GR", "-EHsc", "-DNDEBUG", "-clang:-std=c++17", "-MD", "-Zc:__cplusplus", "-permissive-", "-utf-8", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\cmake_pch.hxx", "-fms-compatibility-version=19.44", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NO_DEBUG", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DQT_XML_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\XCamView_autogen\\include", "-ID:\\work\\XCamView\\XCamView\\lib\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtCore", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtWidgets", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtGui", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtXml", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtSql", "/clang:-isystem", "/clang:E:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/clang:-<PERSON>", "/clang:-<PERSON>x<PERSON>", "/TP", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\qtc_cmake_pch.hxx", "D:\\work\\XCamView\\XCamView\\main.cpp"], "directory": "D:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/.qtc_clangd", "file": "D:/work/XCamView/XCamView/main.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-DWIN32", "-D_WINDOWS", "-GR", "-EHsc", "-DNDEBUG", "-clang:-std=c++17", "-MD", "-Zc:__cplusplus", "-permissive-", "-utf-8", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\cmake_pch.hxx", "-fms-compatibility-version=19.44", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NO_DEBUG", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DQT_XML_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\XCamView_autogen\\include", "-ID:\\work\\XCamView\\XCamView\\lib\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtCore", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtWidgets", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtGui", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtXml", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtSql", "/clang:-isystem", "/clang:E:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/clang:-<PERSON>", "/clang:-<PERSON>x<PERSON>", "/TP", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\qtc_cmake_pch.hxx", "D:\\work\\XCamView\\XCamView\\mainwindow.cpp"], "directory": "D:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/.qtc_clangd", "file": "D:/work/XCamView/XCamView/mainwindow.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-DWIN32", "-D_WINDOWS", "-GR", "-EHsc", "-DNDEBUG", "-clang:-std=c++17", "-MD", "-Zc:__cplusplus", "-permissive-", "-utf-8", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\cmake_pch.hxx", "-fms-compatibility-version=19.44", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NO_DEBUG", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DQT_XML_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\XCamView_autogen\\include", "-ID:\\work\\XCamView\\XCamView\\lib\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtCore", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtWidgets", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtGui", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtXml", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtSql", "/clang:-isystem", "/clang:E:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/clang:-<PERSON>", "/clang:-<PERSON>x<PERSON>", "/TP", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\qtc_cmake_pch.hxx", "D:\\work\\XCamView\\XCamView\\module\\camera\\uvchamcam\\uvchamcam.cpp"], "directory": "D:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/.qtc_clangd", "file": "D:/work/XCamView/XCamView/module/camera/uvchamcam/uvchamcam.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-DWIN32", "-D_WINDOWS", "-GR", "-EHsc", "-DNDEBUG", "-clang:-std=c++17", "-MD", "-Zc:__cplusplus", "-permissive-", "-utf-8", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\cmake_pch.hxx", "-fms-compatibility-version=19.44", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NO_DEBUG", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DQT_XML_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\XCamView_autogen\\include", "-ID:\\work\\XCamView\\XCamView\\lib\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtCore", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtWidgets", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtGui", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtXml", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtSql", "/clang:-isystem", "/clang:E:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/clang:-<PERSON>", "/clang:-<PERSON>x<PERSON>", "/TP", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\qtc_cmake_pch.hxx", "D:\\work\\XCamView\\XCamView\\module\\camera\\uvchamcam\\uvchamloader.cpp"], "directory": "D:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/.qtc_clangd", "file": "D:/work/XCamView/XCamView/module/camera/uvchamcam/uvchamloader.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-DWIN32", "-D_WINDOWS", "-GR", "-EHsc", "-DNDEBUG", "-clang:-std=c++17", "-MD", "-Zc:__cplusplus", "-permissive-", "-utf-8", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\cmake_pch.hxx", "-fms-compatibility-version=19.44", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NO_DEBUG", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DQT_XML_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\XCamView_autogen\\include", "-ID:\\work\\XCamView\\XCamView\\lib\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtCore", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtWidgets", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtGui", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtXml", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtSql", "/clang:-isystem", "/clang:E:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/clang:-<PERSON>", "/clang:-<PERSON>x<PERSON>", "/TP", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\qtc_cmake_pch.hxx", "D:\\work\\XCamView\\XCamView\\module\\camera\\camera.cpp"], "directory": "D:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/.qtc_clangd", "file": "D:/work/XCamView/XCamView/module/camera/camera.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-DWIN32", "-D_WINDOWS", "-GR", "-EHsc", "-DNDEBUG", "-clang:-std=c++17", "-MD", "-Zc:__cplusplus", "-permissive-", "-utf-8", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\cmake_pch.hxx", "-fms-compatibility-version=19.44", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NO_DEBUG", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DQT_XML_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\XCamView_autogen\\include", "-ID:\\work\\XCamView\\XCamView\\lib\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtCore", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtWidgets", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtGui", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtXml", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtSql", "/clang:-isystem", "/clang:E:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/clang:-<PERSON>", "/clang:-<PERSON>x<PERSON>", "/TP", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\qtc_cmake_pch.hxx", "D:\\work\\XCamView\\XCamView\\module\\camera\\cameramanager.cpp"], "directory": "D:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/.qtc_clangd", "file": "D:/work/XCamView/XCamView/module/camera/cameramanager.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-DWIN32", "-D_WINDOWS", "-GR", "-EHsc", "-DNDEBUG", "-clang:-std=c++17", "-MD", "-Zc:__cplusplus", "-permissive-", "-utf-8", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\cmake_pch.hxx", "-fms-compatibility-version=19.44", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NO_DEBUG", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DQT_XML_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\XCamView_autogen\\include", "-ID:\\work\\XCamView\\XCamView\\lib\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtCore", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtWidgets", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtGui", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtXml", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtSql", "/clang:-isystem", "/clang:E:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/clang:-<PERSON>", "/clang:-<PERSON>x<PERSON>", "/TP", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\qtc_cmake_pch.hxx", "D:\\work\\XCamView\\XCamView\\module\\auxrect\\auxrect.cpp"], "directory": "D:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/.qtc_clangd", "file": "D:/work/XCamView/XCamView/module/auxrect/auxrect.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-DWIN32", "-D_WINDOWS", "-GR", "-EHsc", "-DNDEBUG", "-clang:-std=c++17", "-MD", "-Zc:__cplusplus", "-permissive-", "-utf-8", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\cmake_pch.hxx", "-fms-compatibility-version=19.44", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NO_DEBUG", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DQT_XML_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\XCamView_autogen\\include", "-ID:\\work\\XCamView\\XCamView\\lib\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtCore", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtWidgets", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtGui", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtXml", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtSql", "/clang:-isystem", "/clang:E:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/clang:-<PERSON>", "/clang:-<PERSON>x<PERSON>", "/TP", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\qtc_cmake_pch.hxx", "D:\\work\\XCamView\\XCamView\\module\\i18n\\i18n.cpp"], "directory": "D:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/.qtc_clangd", "file": "D:/work/XCamView/XCamView/module/i18n/i18n.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-DWIN32", "-D_WINDOWS", "-GR", "-EHsc", "-DNDEBUG", "-clang:-std=c++17", "-MD", "-Zc:__cplusplus", "-permissive-", "-utf-8", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\cmake_pch.hxx", "-fms-compatibility-version=19.44", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NO_DEBUG", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DQT_XML_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\XCamView_autogen\\include", "-ID:\\work\\XCamView\\XCamView\\lib\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtCore", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtWidgets", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtGui", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtXml", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtSql", "/clang:-isystem", "/clang:E:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/clang:-<PERSON>", "/clang:-<PERSON>x<PERSON>", "/TP", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\qtc_cmake_pch.hxx", "D:\\work\\XCamView\\XCamView\\module\\TpSaveLoader\\tpsaveloader.cpp"], "directory": "D:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/.qtc_clangd", "file": "D:/work/XCamView/XCamView/module/TpSaveLoader/tpsaveloader.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-DWIN32", "-D_WINDOWS", "-GR", "-EHsc", "-DNDEBUG", "-clang:-std=c++17", "-MD", "-Zc:__cplusplus", "-permissive-", "-utf-8", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\cmake_pch.hxx", "-fms-compatibility-version=19.44", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NO_DEBUG", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DQT_XML_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\XCamView_autogen\\include", "-ID:\\work\\XCamView\\XCamView\\lib\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtCore", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtWidgets", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtGui", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtXml", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtSql", "/clang:-isystem", "/clang:E:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/clang:-<PERSON>", "/clang:-<PERSON>x<PERSON>", "/TP", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\qtc_cmake_pch.hxx", "D:\\work\\XCamView\\XCamView\\module\\TpSignalConnect\\TpSignalConnect.cpp"], "directory": "D:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/.qtc_clangd", "file": "D:/work/XCamView/XCamView/module/TpSignalConnect/TpSignalConnect.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-DWIN32", "-D_WINDOWS", "-GR", "-EHsc", "-DNDEBUG", "-clang:-std=c++17", "-MD", "-Zc:__cplusplus", "-permissive-", "-utf-8", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\cmake_pch.hxx", "-fms-compatibility-version=19.44", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NO_DEBUG", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DQT_XML_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\XCamView_autogen\\include", "-ID:\\work\\XCamView\\XCamView\\lib\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtCore", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtWidgets", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtGui", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtXml", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtSql", "/clang:-isystem", "/clang:E:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/clang:-<PERSON>", "/clang:-<PERSON>x<PERSON>", "/TP", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\qtc_cmake_pch.hxx", "D:\\work\\XCamView\\XCamView\\module\\tutils\\tutils.cpp"], "directory": "D:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/.qtc_clangd", "file": "D:/work/XCamView/XCamView/module/tutils/tutils.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-DWIN32", "-D_WINDOWS", "-GR", "-EHsc", "-DNDEBUG", "-clang:-std=c++17", "-MD", "-Zc:__cplusplus", "-permissive-", "-utf-8", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\cmake_pch.hxx", "-fms-compatibility-version=19.44", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NO_DEBUG", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DQT_XML_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\XCamView_autogen\\include", "-ID:\\work\\XCamView\\XCamView\\lib\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtCore", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtWidgets", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtGui", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtXml", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtSql", "/clang:-isystem", "/clang:E:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/clang:-<PERSON>", "/clang:-<PERSON>x<PERSON>", "/TP", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\qtc_cmake_pch.hxx", "D:\\work\\XCamView\\XCamView\\panel\\camerapanel\\cameralistwidget.cpp"], "directory": "D:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/.qtc_clangd", "file": "D:/work/XCamView/XCamView/panel/camerapanel/cameralistwidget.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-DWIN32", "-D_WINDOWS", "-GR", "-EHsc", "-DNDEBUG", "-clang:-std=c++17", "-MD", "-Zc:__cplusplus", "-permissive-", "-utf-8", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\cmake_pch.hxx", "-fms-compatibility-version=19.44", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NO_DEBUG", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DQT_XML_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\XCamView_autogen\\include", "-ID:\\work\\XCamView\\XCamView\\lib\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtCore", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtWidgets", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtGui", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtXml", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtSql", "/clang:-isystem", "/clang:E:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/clang:-<PERSON>", "/clang:-<PERSON>x<PERSON>", "/TP", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\qtc_cmake_pch.hxx", "D:\\work\\XCamView\\XCamView\\panel\\camerapanel\\camerapanel.cpp"], "directory": "D:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/.qtc_clangd", "file": "D:/work/XCamView/XCamView/panel/camerapanel/camerapanel.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-DWIN32", "-D_WINDOWS", "-GR", "-EHsc", "-DNDEBUG", "-clang:-std=c++17", "-MD", "-Zc:__cplusplus", "-permissive-", "-utf-8", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\cmake_pch.hxx", "-fms-compatibility-version=19.44", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NO_DEBUG", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DQT_XML_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\XCamView_autogen\\include", "-ID:\\work\\XCamView\\XCamView\\lib\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtCore", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtWidgets", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtGui", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtXml", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtSql", "/clang:-isystem", "/clang:E:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/clang:-<PERSON>", "/clang:-<PERSON>x<PERSON>", "/TP", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\qtc_cmake_pch.hxx", "D:\\work\\XCamView\\XCamView\\panel\\camerapanel\\coloradjustwidget.cpp"], "directory": "D:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/.qtc_clangd", "file": "D:/work/XCamView/XCamView/panel/camerapanel/coloradjustwidget.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-DWIN32", "-D_WINDOWS", "-GR", "-EHsc", "-DNDEBUG", "-clang:-std=c++17", "-MD", "-Zc:__cplusplus", "-permissive-", "-utf-8", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\cmake_pch.hxx", "-fms-compatibility-version=19.44", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NO_DEBUG", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DQT_XML_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\XCamView_autogen\\include", "-ID:\\work\\XCamView\\XCamView\\lib\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtCore", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtWidgets", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtGui", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtXml", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtSql", "/clang:-isystem", "/clang:E:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/clang:-<PERSON>", "/clang:-<PERSON>x<PERSON>", "/TP", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\qtc_cmake_pch.hxx", "D:\\work\\XCamView\\XCamView\\panel\\camerapanel\\exposuregainwidget.cpp"], "directory": "D:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/.qtc_clangd", "file": "D:/work/XCamView/XCamView/panel/camerapanel/exposuregainwidget.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-DWIN32", "-D_WINDOWS", "-GR", "-EHsc", "-DNDEBUG", "-clang:-std=c++17", "-MD", "-Zc:__cplusplus", "-permissive-", "-utf-8", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\cmake_pch.hxx", "-fms-compatibility-version=19.44", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NO_DEBUG", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DQT_XML_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\XCamView_autogen\\include", "-ID:\\work\\XCamView\\XCamView\\lib\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtCore", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtWidgets", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtGui", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtXml", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtSql", "/clang:-isystem", "/clang:E:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/clang:-<PERSON>", "/clang:-<PERSON>x<PERSON>", "/TP", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\qtc_cmake_pch.hxx", "D:\\work\\XCamView\\XCamView\\panel\\camerapanel\\powerfrequencywidget.cpp"], "directory": "D:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/.qtc_clangd", "file": "D:/work/XCamView/XCamView/panel/camerapanel/powerfrequencywidget.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-DWIN32", "-D_WINDOWS", "-GR", "-EHsc", "-DNDEBUG", "-clang:-std=c++17", "-MD", "-Zc:__cplusplus", "-permissive-", "-utf-8", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\cmake_pch.hxx", "-fms-compatibility-version=19.44", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NO_DEBUG", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DQT_XML_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\XCamView_autogen\\include", "-ID:\\work\\XCamView\\XCamView\\lib\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtCore", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtWidgets", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtGui", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtXml", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtSql", "/clang:-isystem", "/clang:E:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/clang:-<PERSON>", "/clang:-<PERSON>x<PERSON>", "/TP", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\qtc_cmake_pch.hxx", "D:\\work\\XCamView\\XCamView\\panel\\camerapanel\\resolutionwidget.cpp"], "directory": "D:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/.qtc_clangd", "file": "D:/work/XCamView/XCamView/panel/camerapanel/resolutionwidget.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-DWIN32", "-D_WINDOWS", "-GR", "-EHsc", "-DNDEBUG", "-clang:-std=c++17", "-MD", "-Zc:__cplusplus", "-permissive-", "-utf-8", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\cmake_pch.hxx", "-fms-compatibility-version=19.44", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NO_DEBUG", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DQT_XML_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\XCamView_autogen\\include", "-ID:\\work\\XCamView\\XCamView\\lib\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtCore", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtWidgets", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtGui", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtXml", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtSql", "/clang:-isystem", "/clang:E:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/clang:-<PERSON>", "/clang:-<PERSON>x<PERSON>", "/TP", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\qtc_cmake_pch.hxx", "D:\\work\\XCamView\\XCamView\\panel\\camerapanel\\whitebalancewidget.cpp"], "directory": "D:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/.qtc_clangd", "file": "D:/work/XCamView/XCamView/panel/camerapanel/whitebalancewidget.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-DWIN32", "-D_WINDOWS", "-GR", "-EHsc", "-DNDEBUG", "-clang:-std=c++17", "-MD", "-Zc:__cplusplus", "-permissive-", "-utf-8", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\cmake_pch.hxx", "-fms-compatibility-version=19.44", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NO_DEBUG", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DQT_XML_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\XCamView_autogen\\include", "-ID:\\work\\XCamView\\XCamView\\lib\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtCore", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtWidgets", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtGui", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtXml", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtSql", "/clang:-isystem", "/clang:E:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/clang:-<PERSON>", "/clang:-<PERSON>x<PERSON>", "/TP", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\qtc_cmake_pch.hxx", "D:\\work\\XCamView\\XCamView\\panel\\centralpanel\\displayview\\displayview.cpp"], "directory": "D:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/.qtc_clangd", "file": "D:/work/XCamView/XCamView/panel/centralpanel/displayview/displayview.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-DWIN32", "-D_WINDOWS", "-GR", "-EHsc", "-DNDEBUG", "-clang:-std=c++17", "-MD", "-Zc:__cplusplus", "-permissive-", "-utf-8", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\cmake_pch.hxx", "-fms-compatibility-version=19.44", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NO_DEBUG", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DQT_XML_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\XCamView_autogen\\include", "-ID:\\work\\XCamView\\XCamView\\lib\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtCore", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtWidgets", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtGui", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtXml", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtSql", "/clang:-isystem", "/clang:E:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/clang:-<PERSON>", "/clang:-<PERSON>x<PERSON>", "/TP", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\qtc_cmake_pch.hxx", "D:\\work\\XCamView\\XCamView\\panel\\centralpanel\\displayview\\imageview.cpp"], "directory": "D:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/.qtc_clangd", "file": "D:/work/XCamView/XCamView/panel/centralpanel/displayview/imageview.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-DWIN32", "-D_WINDOWS", "-GR", "-EHsc", "-DNDEBUG", "-clang:-std=c++17", "-MD", "-Zc:__cplusplus", "-permissive-", "-utf-8", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\cmake_pch.hxx", "-fms-compatibility-version=19.44", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NO_DEBUG", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DQT_XML_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\XCamView_autogen\\include", "-ID:\\work\\XCamView\\XCamView\\lib\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtCore", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtWidgets", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtGui", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtXml", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtSql", "/clang:-isystem", "/clang:E:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/clang:-<PERSON>", "/clang:-<PERSON>x<PERSON>", "/TP", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\qtc_cmake_pch.hxx", "D:\\work\\XCamView\\XCamView\\panel\\centralpanel\\displayview\\previewview.cpp"], "directory": "D:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/.qtc_clangd", "file": "D:/work/XCamView/XCamView/panel/centralpanel/displayview/previewview.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-DWIN32", "-D_WINDOWS", "-GR", "-EHsc", "-DNDEBUG", "-clang:-std=c++17", "-MD", "-Zc:__cplusplus", "-permissive-", "-utf-8", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\cmake_pch.hxx", "-fms-compatibility-version=19.44", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NO_DEBUG", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DQT_XML_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\XCamView_autogen\\include", "-ID:\\work\\XCamView\\XCamView\\lib\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtCore", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtWidgets", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtGui", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtXml", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtSql", "/clang:-isystem", "/clang:E:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/clang:-<PERSON>", "/clang:-<PERSON>x<PERSON>", "/TP", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\qtc_cmake_pch.hxx", "D:\\work\\XCamView\\XCamView\\panel\\centralpanel\\monitorview\\monitorview.cpp"], "directory": "D:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/.qtc_clangd", "file": "D:/work/XCamView/XCamView/panel/centralpanel/monitorview/monitorview.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-DWIN32", "-D_WINDOWS", "-GR", "-EHsc", "-DNDEBUG", "-clang:-std=c++17", "-MD", "-Zc:__cplusplus", "-permissive-", "-utf-8", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\cmake_pch.hxx", "-fms-compatibility-version=19.44", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NO_DEBUG", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DQT_XML_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\XCamView_autogen\\include", "-ID:\\work\\XCamView\\XCamView\\lib\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtCore", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtWidgets", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtGui", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtXml", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtSql", "/clang:-isystem", "/clang:E:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/clang:-<PERSON>", "/clang:-<PERSON>x<PERSON>", "/TP", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\qtc_cmake_pch.hxx", "D:\\work\\XCamView\\XCamView\\panel\\centralpanel\\tptabwidget.cpp"], "directory": "D:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/.qtc_clangd", "file": "D:/work/XCamView/XCamView/panel/centralpanel/tptabwidget.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-DWIN32", "-D_WINDOWS", "-GR", "-EHsc", "-DNDEBUG", "-clang:-std=c++17", "-MD", "-Zc:__cplusplus", "-permissive-", "-utf-8", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\cmake_pch.hxx", "-fms-compatibility-version=19.44", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NO_DEBUG", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DQT_XML_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\XCamView_autogen\\include", "-ID:\\work\\XCamView\\XCamView\\lib\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtCore", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtWidgets", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtGui", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtXml", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtSql", "/clang:-isystem", "/clang:E:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/clang:-<PERSON>", "/clang:-<PERSON>x<PERSON>", "/TP", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\qtc_cmake_pch.hxx", "D:\\work\\XCamView\\XCamView\\panel\\centralpanel\\view.cpp"], "directory": "D:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/.qtc_clangd", "file": "D:/work/XCamView/XCamView/panel/centralpanel/view.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-DWIN32", "-D_WINDOWS", "-GR", "-EHsc", "-DNDEBUG", "-clang:-std=c++17", "-MD", "-Zc:__cplusplus", "-permissive-", "-utf-8", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\cmake_pch.hxx", "-fms-compatibility-version=19.44", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NO_DEBUG", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DQT_XML_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\XCamView_autogen\\include", "-ID:\\work\\XCamView\\XCamView\\lib\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtCore", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtWidgets", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtGui", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtXml", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtSql", "/clang:-isystem", "/clang:E:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/clang:-<PERSON>", "/clang:-<PERSON>x<PERSON>", "/TP", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\qtc_cmake_pch.hxx", "D:\\work\\XCamView\\XCamView\\panel\\chatpanel\\chatwidget.cpp"], "directory": "D:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/.qtc_clangd", "file": "D:/work/XCamView/XCamView/panel/chatpanel/chatwidget.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-DWIN32", "-D_WINDOWS", "-GR", "-EHsc", "-DNDEBUG", "-clang:-std=c++17", "-MD", "-Zc:__cplusplus", "-permissive-", "-utf-8", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\cmake_pch.hxx", "-fms-compatibility-version=19.44", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NO_DEBUG", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DQT_XML_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\XCamView_autogen\\include", "-ID:\\work\\XCamView\\XCamView\\lib\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtCore", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtWidgets", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtGui", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtXml", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtSql", "/clang:-isystem", "/clang:E:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/clang:-<PERSON>", "/clang:-<PERSON>x<PERSON>", "/TP", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\qtc_cmake_pch.hxx", "D:\\work\\XCamView\\XCamView\\panel\\login\\logindialog.cpp"], "directory": "D:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/.qtc_clangd", "file": "D:/work/XCamView/XCamView/panel/login/logindialog.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-DWIN32", "-D_WINDOWS", "-GR", "-EHsc", "-DNDEBUG", "-clang:-std=c++17", "-MD", "-Zc:__cplusplus", "-permissive-", "-utf-8", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\cmake_pch.hxx", "-fms-compatibility-version=19.44", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NO_DEBUG", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DQT_XML_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\XCamView_autogen\\include", "-ID:\\work\\XCamView\\XCamView\\lib\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtCore", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtWidgets", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtGui", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtXml", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtSql", "/clang:-isystem", "/clang:E:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/clang:-<PERSON>", "/clang:-<PERSON>x<PERSON>", "/TP", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\qtc_cmake_pch.hxx", "D:\\work\\XCamView\\XCamView\\panel\\measure\\tpcalibrationdlg.cpp"], "directory": "D:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/.qtc_clangd", "file": "D:/work/XCamView/XCamView/panel/measure/tpcalibrationdlg.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-DWIN32", "-D_WINDOWS", "-GR", "-EHsc", "-DNDEBUG", "-clang:-std=c++17", "-MD", "-Zc:__cplusplus", "-permissive-", "-utf-8", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\cmake_pch.hxx", "-fms-compatibility-version=19.44", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NO_DEBUG", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DQT_XML_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\XCamView_autogen\\include", "-ID:\\work\\XCamView\\XCamView\\lib\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtCore", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtWidgets", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtGui", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtXml", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtSql", "/clang:-isystem", "/clang:E:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/clang:-<PERSON>", "/clang:-<PERSON>x<PERSON>", "/TP", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\qtc_cmake_pch.hxx", "D:\\work\\XCamView\\XCamView\\panel\\measure\\tptexteditdlg.cpp"], "directory": "D:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/.qtc_clangd", "file": "D:/work/XCamView/XCamView/panel/measure/tptexteditdlg.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-DWIN32", "-D_WINDOWS", "-GR", "-EHsc", "-DNDEBUG", "-clang:-std=c++17", "-MD", "-Zc:__cplusplus", "-permissive-", "-utf-8", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\cmake_pch.hxx", "-fms-compatibility-version=19.44", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NO_DEBUG", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DQT_XML_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\XCamView_autogen\\include", "-ID:\\work\\XCamView\\XCamView\\lib\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtCore", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtWidgets", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtGui", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtXml", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtSql", "/clang:-isystem", "/clang:E:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/clang:-<PERSON>", "/clang:-<PERSON>x<PERSON>", "/TP", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\qtc_cmake_pch.hxx", "D:\\work\\XCamView\\XCamView\\panel\\monitorpanel\\monitordialog.cpp"], "directory": "D:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/.qtc_clangd", "file": "D:/work/XCamView/XCamView/panel/monitorpanel/monitordialog.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-DWIN32", "-D_WINDOWS", "-GR", "-EHsc", "-DNDEBUG", "-clang:-std=c++17", "-MD", "-Zc:__cplusplus", "-permissive-", "-utf-8", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\cmake_pch.hxx", "-fms-compatibility-version=19.44", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NO_DEBUG", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DQT_XML_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\XCamView_autogen\\include", "-ID:\\work\\XCamView\\XCamView\\lib\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtCore", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtWidgets", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtGui", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtXml", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtSql", "/clang:-isystem", "/clang:E:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/clang:-<PERSON>", "/clang:-<PERSON>x<PERSON>", "/TP", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\qtc_cmake_pch.hxx", "D:\\work\\XCamView\\XCamView\\panel\\monitorpanel\\monitorwidget.cpp"], "directory": "D:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/.qtc_clangd", "file": "D:/work/XCamView/XCamView/panel/monitorpanel/monitorwidget.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-DWIN32", "-D_WINDOWS", "-GR", "-EHsc", "-DNDEBUG", "-clang:-std=c++17", "-MD", "-Zc:__cplusplus", "-permissive-", "-utf-8", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\cmake_pch.hxx", "-fms-compatibility-version=19.44", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NO_DEBUG", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DQT_XML_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\XCamView_autogen\\include", "-ID:\\work\\XCamView\\XCamView\\lib\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtCore", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtWidgets", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtGui", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtXml", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtSql", "/clang:-isystem", "/clang:E:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/clang:-<PERSON>", "/clang:-<PERSON>x<PERSON>", "/TP", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\qtc_cmake_pch.hxx", "D:\\work\\XCamView\\XCamView\\panel\\collapsewidget.cpp"], "directory": "D:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/.qtc_clangd", "file": "D:/work/XCamView/XCamView/panel/collapsewidget.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-DWIN32", "-D_WINDOWS", "-GR", "-EHsc", "-DNDEBUG", "-clang:-std=c++17", "-MD", "-Zc:__cplusplus", "-permissive-", "-utf-8", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\cmake_pch.hxx", "-fms-compatibility-version=19.44", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NO_DEBUG", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DQT_XML_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\XCamView_autogen\\include", "-ID:\\work\\XCamView\\XCamView\\lib\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtCore", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtWidgets", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtGui", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtXml", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtSql", "/clang:-isystem", "/clang:E:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/clang:-<PERSON>", "/clang:-<PERSON>x<PERSON>", "/TP", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\qtc_cmake_pch.hxx", "D:\\work\\XCamView\\XCamView\\panel\\leftpanel.cpp"], "directory": "D:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/.qtc_clangd", "file": "D:/work/XCamView/XCamView/panel/leftpanel.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-DWIN32", "-D_WINDOWS", "-GR", "-EHsc", "-DNDEBUG", "-clang:-std=c++17", "-MD", "-Zc:__cplusplus", "-permissive-", "-utf-8", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\cmake_pch.hxx", "-fms-compatibility-version=19.44", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NO_DEBUG", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DQT_XML_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\XCamView_autogen\\include", "-ID:\\work\\XCamView\\XCamView\\lib\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtCore", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtWidgets", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtGui", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtXml", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtSql", "/clang:-isystem", "/clang:E:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/clang:-<PERSON>", "/clang:-<PERSON>x<PERSON>", "/TP", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\qtc_cmake_pch.hxx", "D:\\work\\XCamView\\XCamView\\global.cpp"], "directory": "D:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/.qtc_clangd", "file": "D:/work/XCamView/XCamView/global.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-DWIN32", "-D_WINDOWS", "-GR", "-EHsc", "-DNDEBUG", "-clang:-std=c++17", "-MD", "-Zc:__cplusplus", "-permissive-", "-utf-8", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\cmake_pch.hxx", "-fms-compatibility-version=19.44", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NO_DEBUG", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DQT_XML_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\XCamView_autogen\\include", "-ID:\\work\\XCamView\\XCamView\\lib\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtCore", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtWidgets", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtGui", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtXml", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtSql", "/clang:-isystem", "/clang:E:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/clang:-<PERSON>", "/clang:-<PERSON>x<PERSON>", "/TP", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\qtc_cmake_pch.hxx", "D:\\work\\XCamView\\XCamView\\mainwindow.h"], "directory": "D:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/.qtc_clangd", "file": "D:/work/XCamView/XCamView/mainwindow.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-DWIN32", "-D_WINDOWS", "-GR", "-EHsc", "-DNDEBUG", "-clang:-std=c++17", "-MD", "-Zc:__cplusplus", "-permissive-", "-utf-8", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\cmake_pch.hxx", "-fms-compatibility-version=19.44", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NO_DEBUG", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DQT_XML_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\XCamView_autogen\\include", "-ID:\\work\\XCamView\\XCamView\\lib\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtCore", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtWidgets", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtGui", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtXml", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtSql", "/clang:-isystem", "/clang:E:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/clang:-<PERSON>", "/clang:-<PERSON>x<PERSON>", "/TP", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\qtc_cmake_pch.hxx", "D:\\work\\XCamView\\XCamView\\module\\camera\\uvchamcam\\uvcham.h"], "directory": "D:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/.qtc_clangd", "file": "D:/work/XCamView/XCamView/module/camera/uvchamcam/uvcham.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-DWIN32", "-D_WINDOWS", "-GR", "-EHsc", "-DNDEBUG", "-clang:-std=c++17", "-MD", "-Zc:__cplusplus", "-permissive-", "-utf-8", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\cmake_pch.hxx", "-fms-compatibility-version=19.44", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NO_DEBUG", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DQT_XML_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\XCamView_autogen\\include", "-ID:\\work\\XCamView\\XCamView\\lib\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtCore", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtWidgets", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtGui", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtXml", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtSql", "/clang:-isystem", "/clang:E:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/clang:-<PERSON>", "/clang:-<PERSON>x<PERSON>", "/TP", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\qtc_cmake_pch.hxx", "D:\\work\\XCamView\\XCamView\\module\\camera\\uvchamcam\\uvchamcam.h"], "directory": "D:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/.qtc_clangd", "file": "D:/work/XCamView/XCamView/module/camera/uvchamcam/uvchamcam.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-DWIN32", "-D_WINDOWS", "-GR", "-EHsc", "-DNDEBUG", "-clang:-std=c++17", "-MD", "-Zc:__cplusplus", "-permissive-", "-utf-8", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\cmake_pch.hxx", "-fms-compatibility-version=19.44", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NO_DEBUG", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DQT_XML_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\XCamView_autogen\\include", "-ID:\\work\\XCamView\\XCamView\\lib\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtCore", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtWidgets", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtGui", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtXml", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtSql", "/clang:-isystem", "/clang:E:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/clang:-<PERSON>", "/clang:-<PERSON>x<PERSON>", "/TP", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\qtc_cmake_pch.hxx", "D:\\work\\XCamView\\XCamView\\module\\camera\\uvchamcam\\uvchamloader.h"], "directory": "D:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/.qtc_clangd", "file": "D:/work/XCamView/XCamView/module/camera/uvchamcam/uvchamloader.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-DWIN32", "-D_WINDOWS", "-GR", "-EHsc", "-DNDEBUG", "-clang:-std=c++17", "-MD", "-Zc:__cplusplus", "-permissive-", "-utf-8", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\cmake_pch.hxx", "-fms-compatibility-version=19.44", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NO_DEBUG", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DQT_XML_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\XCamView_autogen\\include", "-ID:\\work\\XCamView\\XCamView\\lib\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtCore", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtWidgets", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtGui", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtXml", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtSql", "/clang:-isystem", "/clang:E:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/clang:-<PERSON>", "/clang:-<PERSON>x<PERSON>", "/TP", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\qtc_cmake_pch.hxx", "D:\\work\\XCamView\\XCamView\\module\\camera\\camera.h"], "directory": "D:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/.qtc_clangd", "file": "D:/work/XCamView/XCamView/module/camera/camera.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-DWIN32", "-D_WINDOWS", "-GR", "-EHsc", "-DNDEBUG", "-clang:-std=c++17", "-MD", "-Zc:__cplusplus", "-permissive-", "-utf-8", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\cmake_pch.hxx", "-fms-compatibility-version=19.44", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NO_DEBUG", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DQT_XML_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\XCamView_autogen\\include", "-ID:\\work\\XCamView\\XCamView\\lib\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtCore", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtWidgets", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtGui", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtXml", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtSql", "/clang:-isystem", "/clang:E:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/clang:-<PERSON>", "/clang:-<PERSON>x<PERSON>", "/TP", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\qtc_cmake_pch.hxx", "D:\\work\\XCamView\\XCamView\\module\\camera\\cameradef.h"], "directory": "D:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/.qtc_clangd", "file": "D:/work/XCamView/XCamView/module/camera/cameradef.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-DWIN32", "-D_WINDOWS", "-GR", "-EHsc", "-DNDEBUG", "-clang:-std=c++17", "-MD", "-Zc:__cplusplus", "-permissive-", "-utf-8", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\cmake_pch.hxx", "-fms-compatibility-version=19.44", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NO_DEBUG", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DQT_XML_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\XCamView_autogen\\include", "-ID:\\work\\XCamView\\XCamView\\lib\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtCore", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtWidgets", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtGui", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtXml", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtSql", "/clang:-isystem", "/clang:E:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/clang:-<PERSON>", "/clang:-<PERSON>x<PERSON>", "/TP", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\qtc_cmake_pch.hxx", "D:\\work\\XCamView\\XCamView\\module\\camera\\cameramanager.h"], "directory": "D:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/.qtc_clangd", "file": "D:/work/XCamView/XCamView/module/camera/cameramanager.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-DWIN32", "-D_WINDOWS", "-GR", "-EHsc", "-DNDEBUG", "-clang:-std=c++17", "-MD", "-Zc:__cplusplus", "-permissive-", "-utf-8", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\cmake_pch.hxx", "-fms-compatibility-version=19.44", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NO_DEBUG", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DQT_XML_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\XCamView_autogen\\include", "-ID:\\work\\XCamView\\XCamView\\lib\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtCore", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtWidgets", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtGui", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtXml", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtSql", "/clang:-isystem", "/clang:E:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/clang:-<PERSON>", "/clang:-<PERSON>x<PERSON>", "/TP", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\qtc_cmake_pch.hxx", "D:\\work\\XCamView\\XCamView\\module\\auxrect\\auxrect.h"], "directory": "D:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/.qtc_clangd", "file": "D:/work/XCamView/XCamView/module/auxrect/auxrect.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-DWIN32", "-D_WINDOWS", "-GR", "-EHsc", "-DNDEBUG", "-clang:-std=c++17", "-MD", "-Zc:__cplusplus", "-permissive-", "-utf-8", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\cmake_pch.hxx", "-fms-compatibility-version=19.44", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NO_DEBUG", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DQT_XML_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\XCamView_autogen\\include", "-ID:\\work\\XCamView\\XCamView\\lib\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtCore", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtWidgets", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtGui", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtXml", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtSql", "/clang:-isystem", "/clang:E:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/clang:-<PERSON>", "/clang:-<PERSON>x<PERSON>", "/TP", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\qtc_cmake_pch.hxx", "D:\\work\\XCamView\\XCamView\\module\\i18n\\i18n.h"], "directory": "D:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/.qtc_clangd", "file": "D:/work/XCamView/XCamView/module/i18n/i18n.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-DWIN32", "-D_WINDOWS", "-GR", "-EHsc", "-DNDEBUG", "-clang:-std=c++17", "-MD", "-Zc:__cplusplus", "-permissive-", "-utf-8", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\cmake_pch.hxx", "-fms-compatibility-version=19.44", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NO_DEBUG", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DQT_XML_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\XCamView_autogen\\include", "-ID:\\work\\XCamView\\XCamView\\lib\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtCore", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtWidgets", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtGui", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtXml", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtSql", "/clang:-isystem", "/clang:E:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/clang:-<PERSON>", "/clang:-<PERSON>x<PERSON>", "/TP", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\qtc_cmake_pch.hxx", "D:\\work\\XCamView\\XCamView\\module\\TpSaveLoader\\tpsaveloader.h"], "directory": "D:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/.qtc_clangd", "file": "D:/work/XCamView/XCamView/module/TpSaveLoader/tpsaveloader.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-DWIN32", "-D_WINDOWS", "-GR", "-EHsc", "-DNDEBUG", "-clang:-std=c++17", "-MD", "-Zc:__cplusplus", "-permissive-", "-utf-8", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\cmake_pch.hxx", "-fms-compatibility-version=19.44", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NO_DEBUG", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DQT_XML_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\XCamView_autogen\\include", "-ID:\\work\\XCamView\\XCamView\\lib\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtCore", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtWidgets", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtGui", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtXml", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtSql", "/clang:-isystem", "/clang:E:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/clang:-<PERSON>", "/clang:-<PERSON>x<PERSON>", "/TP", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\qtc_cmake_pch.hxx", "D:\\work\\XCamView\\XCamView\\module\\TpSignalConnect\\TpSignalConnect.h"], "directory": "D:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/.qtc_clangd", "file": "D:/work/XCamView/XCamView/module/TpSignalConnect/TpSignalConnect.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-DWIN32", "-D_WINDOWS", "-GR", "-EHsc", "-DNDEBUG", "-clang:-std=c++17", "-MD", "-Zc:__cplusplus", "-permissive-", "-utf-8", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\cmake_pch.hxx", "-fms-compatibility-version=19.44", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NO_DEBUG", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DQT_XML_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\XCamView_autogen\\include", "-ID:\\work\\XCamView\\XCamView\\lib\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtCore", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtWidgets", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtGui", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtXml", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtSql", "/clang:-isystem", "/clang:E:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/clang:-<PERSON>", "/clang:-<PERSON>x<PERSON>", "/TP", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\qtc_cmake_pch.hxx", "D:\\work\\XCamView\\XCamView\\module\\tutils\\tutils.h"], "directory": "D:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/.qtc_clangd", "file": "D:/work/XCamView/XCamView/module/tutils/tutils.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-DWIN32", "-D_WINDOWS", "-GR", "-EHsc", "-DNDEBUG", "-clang:-std=c++17", "-MD", "-Zc:__cplusplus", "-permissive-", "-utf-8", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\cmake_pch.hxx", "-fms-compatibility-version=19.44", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NO_DEBUG", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DQT_XML_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\XCamView_autogen\\include", "-ID:\\work\\XCamView\\XCamView\\lib\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtCore", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtWidgets", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtGui", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtXml", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtSql", "/clang:-isystem", "/clang:E:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/clang:-<PERSON>", "/clang:-<PERSON>x<PERSON>", "/TP", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\qtc_cmake_pch.hxx", "D:\\work\\XCamView\\XCamView\\panel\\camerapanel\\cameralistwidget.h"], "directory": "D:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/.qtc_clangd", "file": "D:/work/XCamView/XCamView/panel/camerapanel/cameralistwidget.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-DWIN32", "-D_WINDOWS", "-GR", "-EHsc", "-DNDEBUG", "-clang:-std=c++17", "-MD", "-Zc:__cplusplus", "-permissive-", "-utf-8", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\cmake_pch.hxx", "-fms-compatibility-version=19.44", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NO_DEBUG", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DQT_XML_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\XCamView_autogen\\include", "-ID:\\work\\XCamView\\XCamView\\lib\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtCore", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtWidgets", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtGui", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtXml", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtSql", "/clang:-isystem", "/clang:E:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/clang:-<PERSON>", "/clang:-<PERSON>x<PERSON>", "/TP", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\qtc_cmake_pch.hxx", "D:\\work\\XCamView\\XCamView\\panel\\camerapanel\\camerapanel.h"], "directory": "D:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/.qtc_clangd", "file": "D:/work/XCamView/XCamView/panel/camerapanel/camerapanel.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-DWIN32", "-D_WINDOWS", "-GR", "-EHsc", "-DNDEBUG", "-clang:-std=c++17", "-MD", "-Zc:__cplusplus", "-permissive-", "-utf-8", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\cmake_pch.hxx", "-fms-compatibility-version=19.44", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NO_DEBUG", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DQT_XML_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\XCamView_autogen\\include", "-ID:\\work\\XCamView\\XCamView\\lib\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtCore", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtWidgets", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtGui", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtXml", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtSql", "/clang:-isystem", "/clang:E:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/clang:-<PERSON>", "/clang:-<PERSON>x<PERSON>", "/TP", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\qtc_cmake_pch.hxx", "D:\\work\\XCamView\\XCamView\\panel\\camerapanel\\coloradjustwidget.h"], "directory": "D:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/.qtc_clangd", "file": "D:/work/XCamView/XCamView/panel/camerapanel/coloradjustwidget.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-DWIN32", "-D_WINDOWS", "-GR", "-EHsc", "-DNDEBUG", "-clang:-std=c++17", "-MD", "-Zc:__cplusplus", "-permissive-", "-utf-8", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\cmake_pch.hxx", "-fms-compatibility-version=19.44", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NO_DEBUG", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DQT_XML_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\XCamView_autogen\\include", "-ID:\\work\\XCamView\\XCamView\\lib\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtCore", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtWidgets", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtGui", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtXml", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtSql", "/clang:-isystem", "/clang:E:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/clang:-<PERSON>", "/clang:-<PERSON>x<PERSON>", "/TP", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\qtc_cmake_pch.hxx", "D:\\work\\XCamView\\XCamView\\panel\\camerapanel\\exposuregainwidget.h"], "directory": "D:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/.qtc_clangd", "file": "D:/work/XCamView/XCamView/panel/camerapanel/exposuregainwidget.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-DWIN32", "-D_WINDOWS", "-GR", "-EHsc", "-DNDEBUG", "-clang:-std=c++17", "-MD", "-Zc:__cplusplus", "-permissive-", "-utf-8", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\cmake_pch.hxx", "-fms-compatibility-version=19.44", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NO_DEBUG", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DQT_XML_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\XCamView_autogen\\include", "-ID:\\work\\XCamView\\XCamView\\lib\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtCore", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtWidgets", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtGui", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtXml", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtSql", "/clang:-isystem", "/clang:E:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/clang:-<PERSON>", "/clang:-<PERSON>x<PERSON>", "/TP", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\qtc_cmake_pch.hxx", "D:\\work\\XCamView\\XCamView\\panel\\camerapanel\\powerfrequencywidget.h"], "directory": "D:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/.qtc_clangd", "file": "D:/work/XCamView/XCamView/panel/camerapanel/powerfrequencywidget.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-DWIN32", "-D_WINDOWS", "-GR", "-EHsc", "-DNDEBUG", "-clang:-std=c++17", "-MD", "-Zc:__cplusplus", "-permissive-", "-utf-8", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\cmake_pch.hxx", "-fms-compatibility-version=19.44", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NO_DEBUG", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DQT_XML_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\XCamView_autogen\\include", "-ID:\\work\\XCamView\\XCamView\\lib\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtCore", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtWidgets", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtGui", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtXml", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtSql", "/clang:-isystem", "/clang:E:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/clang:-<PERSON>", "/clang:-<PERSON>x<PERSON>", "/TP", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\qtc_cmake_pch.hxx", "D:\\work\\XCamView\\XCamView\\panel\\camerapanel\\resolutionwidget.h"], "directory": "D:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/.qtc_clangd", "file": "D:/work/XCamView/XCamView/panel/camerapanel/resolutionwidget.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-DWIN32", "-D_WINDOWS", "-GR", "-EHsc", "-DNDEBUG", "-clang:-std=c++17", "-MD", "-Zc:__cplusplus", "-permissive-", "-utf-8", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\cmake_pch.hxx", "-fms-compatibility-version=19.44", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NO_DEBUG", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DQT_XML_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\XCamView_autogen\\include", "-ID:\\work\\XCamView\\XCamView\\lib\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtCore", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtWidgets", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtGui", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtXml", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtSql", "/clang:-isystem", "/clang:E:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/clang:-<PERSON>", "/clang:-<PERSON>x<PERSON>", "/TP", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\qtc_cmake_pch.hxx", "D:\\work\\XCamView\\XCamView\\panel\\camerapanel\\whitebalancewidget.h"], "directory": "D:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/.qtc_clangd", "file": "D:/work/XCamView/XCamView/panel/camerapanel/whitebalancewidget.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-DWIN32", "-D_WINDOWS", "-GR", "-EHsc", "-DNDEBUG", "-clang:-std=c++17", "-MD", "-Zc:__cplusplus", "-permissive-", "-utf-8", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\cmake_pch.hxx", "-fms-compatibility-version=19.44", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NO_DEBUG", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DQT_XML_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\XCamView_autogen\\include", "-ID:\\work\\XCamView\\XCamView\\lib\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtCore", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtWidgets", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtGui", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtXml", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtSql", "/clang:-isystem", "/clang:E:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/clang:-<PERSON>", "/clang:-<PERSON>x<PERSON>", "/TP", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\qtc_cmake_pch.hxx", "D:\\work\\XCamView\\XCamView\\panel\\centralpanel\\displayview\\displayview.h"], "directory": "D:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/.qtc_clangd", "file": "D:/work/XCamView/XCamView/panel/centralpanel/displayview/displayview.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-DWIN32", "-D_WINDOWS", "-GR", "-EHsc", "-DNDEBUG", "-clang:-std=c++17", "-MD", "-Zc:__cplusplus", "-permissive-", "-utf-8", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\cmake_pch.hxx", "-fms-compatibility-version=19.44", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NO_DEBUG", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DQT_XML_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\XCamView_autogen\\include", "-ID:\\work\\XCamView\\XCamView\\lib\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtCore", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtWidgets", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtGui", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtXml", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtSql", "/clang:-isystem", "/clang:E:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/clang:-<PERSON>", "/clang:-<PERSON>x<PERSON>", "/TP", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\qtc_cmake_pch.hxx", "D:\\work\\XCamView\\XCamView\\panel\\centralpanel\\displayview\\imageview.h"], "directory": "D:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/.qtc_clangd", "file": "D:/work/XCamView/XCamView/panel/centralpanel/displayview/imageview.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-DWIN32", "-D_WINDOWS", "-GR", "-EHsc", "-DNDEBUG", "-clang:-std=c++17", "-MD", "-Zc:__cplusplus", "-permissive-", "-utf-8", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\cmake_pch.hxx", "-fms-compatibility-version=19.44", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NO_DEBUG", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DQT_XML_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\XCamView_autogen\\include", "-ID:\\work\\XCamView\\XCamView\\lib\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtCore", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtWidgets", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtGui", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtXml", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtSql", "/clang:-isystem", "/clang:E:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/clang:-<PERSON>", "/clang:-<PERSON>x<PERSON>", "/TP", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\qtc_cmake_pch.hxx", "D:\\work\\XCamView\\XCamView\\panel\\centralpanel\\displayview\\previewview.h"], "directory": "D:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/.qtc_clangd", "file": "D:/work/XCamView/XCamView/panel/centralpanel/displayview/previewview.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-DWIN32", "-D_WINDOWS", "-GR", "-EHsc", "-DNDEBUG", "-clang:-std=c++17", "-MD", "-Zc:__cplusplus", "-permissive-", "-utf-8", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\cmake_pch.hxx", "-fms-compatibility-version=19.44", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NO_DEBUG", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DQT_XML_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\XCamView_autogen\\include", "-ID:\\work\\XCamView\\XCamView\\lib\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtCore", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtWidgets", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtGui", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtXml", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtSql", "/clang:-isystem", "/clang:E:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/clang:-<PERSON>", "/clang:-<PERSON>x<PERSON>", "/TP", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\qtc_cmake_pch.hxx", "D:\\work\\XCamView\\XCamView\\panel\\centralpanel\\monitorview\\monitorview.h"], "directory": "D:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/.qtc_clangd", "file": "D:/work/XCamView/XCamView/panel/centralpanel/monitorview/monitorview.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-DWIN32", "-D_WINDOWS", "-GR", "-EHsc", "-DNDEBUG", "-clang:-std=c++17", "-MD", "-Zc:__cplusplus", "-permissive-", "-utf-8", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\cmake_pch.hxx", "-fms-compatibility-version=19.44", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NO_DEBUG", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DQT_XML_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\XCamView_autogen\\include", "-ID:\\work\\XCamView\\XCamView\\lib\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtCore", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtWidgets", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtGui", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtXml", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtSql", "/clang:-isystem", "/clang:E:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/clang:-<PERSON>", "/clang:-<PERSON>x<PERSON>", "/TP", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\qtc_cmake_pch.hxx", "D:\\work\\XCamView\\XCamView\\panel\\centralpanel\\tptabwidget.h"], "directory": "D:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/.qtc_clangd", "file": "D:/work/XCamView/XCamView/panel/centralpanel/tptabwidget.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-DWIN32", "-D_WINDOWS", "-GR", "-EHsc", "-DNDEBUG", "-clang:-std=c++17", "-MD", "-Zc:__cplusplus", "-permissive-", "-utf-8", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\cmake_pch.hxx", "-fms-compatibility-version=19.44", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NO_DEBUG", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DQT_XML_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\XCamView_autogen\\include", "-ID:\\work\\XCamView\\XCamView\\lib\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtCore", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtWidgets", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtGui", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtXml", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtSql", "/clang:-isystem", "/clang:E:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/clang:-<PERSON>", "/clang:-<PERSON>x<PERSON>", "/TP", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\qtc_cmake_pch.hxx", "D:\\work\\XCamView\\XCamView\\panel\\centralpanel\\view.h"], "directory": "D:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/.qtc_clangd", "file": "D:/work/XCamView/XCamView/panel/centralpanel/view.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-DWIN32", "-D_WINDOWS", "-GR", "-EHsc", "-DNDEBUG", "-clang:-std=c++17", "-MD", "-Zc:__cplusplus", "-permissive-", "-utf-8", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\cmake_pch.hxx", "-fms-compatibility-version=19.44", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NO_DEBUG", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DQT_XML_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\XCamView_autogen\\include", "-ID:\\work\\XCamView\\XCamView\\lib\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtCore", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtWidgets", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtGui", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtXml", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtSql", "/clang:-isystem", "/clang:E:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/clang:-<PERSON>", "/clang:-<PERSON>x<PERSON>", "/TP", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\qtc_cmake_pch.hxx", "D:\\work\\XCamView\\XCamView\\panel\\chatpanel\\chatwidget.h"], "directory": "D:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/.qtc_clangd", "file": "D:/work/XCamView/XCamView/panel/chatpanel/chatwidget.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-DWIN32", "-D_WINDOWS", "-GR", "-EHsc", "-DNDEBUG", "-clang:-std=c++17", "-MD", "-Zc:__cplusplus", "-permissive-", "-utf-8", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\cmake_pch.hxx", "-fms-compatibility-version=19.44", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NO_DEBUG", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DQT_XML_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\XCamView_autogen\\include", "-ID:\\work\\XCamView\\XCamView\\lib\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtCore", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtWidgets", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtGui", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtXml", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtSql", "/clang:-isystem", "/clang:E:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/clang:-<PERSON>", "/clang:-<PERSON>x<PERSON>", "/TP", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\qtc_cmake_pch.hxx", "D:\\work\\XCamView\\XCamView\\panel\\login\\logindialog.h"], "directory": "D:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/.qtc_clangd", "file": "D:/work/XCamView/XCamView/panel/login/logindialog.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-DWIN32", "-D_WINDOWS", "-GR", "-EHsc", "-DNDEBUG", "-clang:-std=c++17", "-MD", "-Zc:__cplusplus", "-permissive-", "-utf-8", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\cmake_pch.hxx", "-fms-compatibility-version=19.44", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NO_DEBUG", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DQT_XML_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\XCamView_autogen\\include", "-ID:\\work\\XCamView\\XCamView\\lib\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtCore", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtWidgets", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtGui", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtXml", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtSql", "/clang:-isystem", "/clang:E:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/clang:-<PERSON>", "/clang:-<PERSON>x<PERSON>", "/TP", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\qtc_cmake_pch.hxx", "D:\\work\\XCamView\\XCamView\\panel\\measure\\tpcalibrationdlg.h"], "directory": "D:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/.qtc_clangd", "file": "D:/work/XCamView/XCamView/panel/measure/tpcalibrationdlg.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-DWIN32", "-D_WINDOWS", "-GR", "-EHsc", "-DNDEBUG", "-clang:-std=c++17", "-MD", "-Zc:__cplusplus", "-permissive-", "-utf-8", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\cmake_pch.hxx", "-fms-compatibility-version=19.44", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NO_DEBUG", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DQT_XML_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\XCamView_autogen\\include", "-ID:\\work\\XCamView\\XCamView\\lib\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtCore", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtWidgets", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtGui", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtXml", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtSql", "/clang:-isystem", "/clang:E:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/clang:-<PERSON>", "/clang:-<PERSON>x<PERSON>", "/TP", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\qtc_cmake_pch.hxx", "D:\\work\\XCamView\\XCamView\\panel\\measure\\tptexteditdlg.h"], "directory": "D:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/.qtc_clangd", "file": "D:/work/XCamView/XCamView/panel/measure/tptexteditdlg.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-DWIN32", "-D_WINDOWS", "-GR", "-EHsc", "-DNDEBUG", "-clang:-std=c++17", "-MD", "-Zc:__cplusplus", "-permissive-", "-utf-8", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\cmake_pch.hxx", "-fms-compatibility-version=19.44", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NO_DEBUG", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DQT_XML_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\XCamView_autogen\\include", "-ID:\\work\\XCamView\\XCamView\\lib\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtCore", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtWidgets", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtGui", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtXml", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtSql", "/clang:-isystem", "/clang:E:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/clang:-<PERSON>", "/clang:-<PERSON>x<PERSON>", "/TP", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\qtc_cmake_pch.hxx", "D:\\work\\XCamView\\XCamView\\panel\\monitorpanel\\monitordialog.h"], "directory": "D:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/.qtc_clangd", "file": "D:/work/XCamView/XCamView/panel/monitorpanel/monitordialog.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-DWIN32", "-D_WINDOWS", "-GR", "-EHsc", "-DNDEBUG", "-clang:-std=c++17", "-MD", "-Zc:__cplusplus", "-permissive-", "-utf-8", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\cmake_pch.hxx", "-fms-compatibility-version=19.44", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NO_DEBUG", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DQT_XML_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\XCamView_autogen\\include", "-ID:\\work\\XCamView\\XCamView\\lib\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtCore", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtWidgets", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtGui", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtXml", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtSql", "/clang:-isystem", "/clang:E:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/clang:-<PERSON>", "/clang:-<PERSON>x<PERSON>", "/TP", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\qtc_cmake_pch.hxx", "D:\\work\\XCamView\\XCamView\\panel\\monitorpanel\\monitorwidget.h"], "directory": "D:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/.qtc_clangd", "file": "D:/work/XCamView/XCamView/panel/monitorpanel/monitorwidget.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-DWIN32", "-D_WINDOWS", "-GR", "-EHsc", "-DNDEBUG", "-clang:-std=c++17", "-MD", "-Zc:__cplusplus", "-permissive-", "-utf-8", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\cmake_pch.hxx", "-fms-compatibility-version=19.44", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NO_DEBUG", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DQT_XML_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\XCamView_autogen\\include", "-ID:\\work\\XCamView\\XCamView\\lib\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtCore", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtWidgets", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtGui", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtXml", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtSql", "/clang:-isystem", "/clang:E:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/clang:-<PERSON>", "/clang:-<PERSON>x<PERSON>", "/TP", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\qtc_cmake_pch.hxx", "D:\\work\\XCamView\\XCamView\\panel\\collapsewidget.h"], "directory": "D:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/.qtc_clangd", "file": "D:/work/XCamView/XCamView/panel/collapsewidget.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-DWIN32", "-D_WINDOWS", "-GR", "-EHsc", "-DNDEBUG", "-clang:-std=c++17", "-MD", "-Zc:__cplusplus", "-permissive-", "-utf-8", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\cmake_pch.hxx", "-fms-compatibility-version=19.44", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NO_DEBUG", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DQT_XML_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\XCamView_autogen\\include", "-ID:\\work\\XCamView\\XCamView\\lib\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtCore", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtWidgets", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtGui", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtXml", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtSql", "/clang:-isystem", "/clang:E:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/clang:-<PERSON>", "/clang:-<PERSON>x<PERSON>", "/TP", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\qtc_cmake_pch.hxx", "D:\\work\\XCamView\\XCamView\\panel\\leftpanel.h"], "directory": "D:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/.qtc_clangd", "file": "D:/work/XCamView/XCamView/panel/leftpanel.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-DWIN32", "-D_WINDOWS", "-GR", "-EHsc", "-DNDEBUG", "-clang:-std=c++17", "-MD", "-Zc:__cplusplus", "-permissive-", "-utf-8", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\cmake_pch.hxx", "-fms-compatibility-version=19.44", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NO_DEBUG", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DQT_XML_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\XCamView_autogen\\include", "-ID:\\work\\XCamView\\XCamView\\lib\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtCore", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtWidgets", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtGui", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtXml", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtSql", "/clang:-isystem", "/clang:E:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/clang:-<PERSON>", "/clang:-<PERSON>x<PERSON>", "/TP", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\qtc_cmake_pch.hxx", "D:\\work\\XCamView\\XCamView\\global.h"], "directory": "D:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/.qtc_clangd", "file": "D:/work/XCamView/XCamView/global.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-DWIN32", "-D_WINDOWS", "-GR", "-EHsc", "-DNDEBUG", "-clang:-std=c++17", "-MD", "-Zc:__cplusplus", "-permissive-", "-utf-8", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\cmake_pch.hxx", "-fms-compatibility-version=19.44", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NO_DEBUG", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DQT_XML_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IE:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\XCamView_autogen\\include", "-ID:\\work\\XCamView\\XCamView\\lib\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtCore", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtWidgets", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtGui", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtXml", "/clang:-isystem", "/clang:E:\\Qt\\6.10.0\\msvc2022_64\\include\\QtSql", "/clang:-isystem", "/clang:E:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:D:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/clang:-<PERSON>", "/clang:-<PERSON>x<PERSON>", "/TP", "/FI", "D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Release\\CMakeFiles\\XCamView.dir\\qtc_cmake_pch.hxx", "D:\\work\\XCamView\\XCamView\\predef.h"], "directory": "D:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Release/.qtc_clangd", "file": "D:/work/XCamView/XCamView/predef.h"}]