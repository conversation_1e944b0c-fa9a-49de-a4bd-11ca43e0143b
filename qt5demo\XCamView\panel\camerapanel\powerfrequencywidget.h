#ifndef POWERFREQUENCYWIDGET_H
#define POWERFREQUENCYWIDGET_H

#include <QButtonGroup>
#include <QRadioButton>
#include "../collapsewidget.h"

class PowerFrequencyWidget : public CollapseWidget
{
    Q_OBJECT
public:
    explicit PowerFrequencyWidget(QWidget* parent = nullptr);

    void Notify(int notify);

protected slots:
    void OnHzBGpClicked(int id);

private:
    void EnableBtns(bool bEnabled);

    QRadioButton*   m_pDCRb;
    QRadioButton*   m_pAC50Rb;
    QRadioButton*   m_pAC60Rb;
    QButtonGroup*   m_pHzBGp;
};

#endif
