#ifndef PREVIEWVIEW_H
#define PREVIEWVIEW_H

#include "displayview.h"

class AuxRect;

class PreviewWidget : public DisplayWidget
{
    Q_OBJECT
public:
    explicit PreviewWidget(QWidget* parent = nullptr);

    void Notify(int notify) override;

private slots:
    void OnCamEvent(int event);
};

class PreviewView : public DisplayView
{
	Q_OBJECT
public:
    explicit PreviewView(QWidget* parent = nullptr);
};

#endif
