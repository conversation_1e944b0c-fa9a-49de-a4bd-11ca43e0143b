﻿#ifndef MONITORWIDGET_H
#define MONITORWIDGET_H

#include <QWidget>
#include <QTreeWidget>
#include <QTableWidget>
#include <QLabel>
#include <QFrame>
#include <QHeaderView>
#include <QUdpSocket>
#include <QTimer>
#include <QMap>
#include <QDateTime>
#include "../../global.h"  // 包含全局定义

class MonitorWidget : public QWidget
{
    Q_OBJECT
public:
    explicit MonitorWidget(QWidget *parent = nullptr);
    ~MonitorWidget(); // 添加析构函数

    // 添加 Notify 方法声明
    void Notify(int notify);

protected:
    // 添加必要的方法声明
    void SendUDPMessage(MessageType type, QString serverAddress = "");
    void sendUserListResponse();  // 发送用户列表响应
    void NewParticipantIn(QString userName, QString localHostName, QString ipAddress);
    void ParticipantLeft(QString userName, QString localHostName, QString time);
    void CheckHeartbeat();
    QString getIP();
    QString getUserName();
    QString getMessage();
    void closeEvent(QCloseEvent *event);

private slots:
    void onTreeItemClicked(QTreeWidgetItem* item, int column);
    void OnItemDoubleClicked(QTreeWidgetItem *item, int column);
    void OnReadUDPMessage();
    void OnSendHeartbeat();

private:
    void setupUI();
    void setupTitleBar();
    void setupTreeWidget();
    void setupTableWidget();
    void setupStatusBar();
    void updateOnlineCount();
    void addUserToTable(const QString& username, const QString& ip);
    QString GetDisplayIP(const QString& rawIP);

    // 原有组件
    QTreeWidget* m_pTreeWidget;
    QTableWidget* m_pTableWidget;
    QLabel* m_pNumLabel;
    
    // 新增美化组件
    QLabel* m_pTitleLabel;           // 标题标签
    QFrame* m_pTitleFrame;           // 标题框架
    QFrame* m_pTreeFrame;            // 树形控件框架
    QFrame* m_pTableFrame;           // 表格框架
    QFrame* m_pStatusFrame;          // 状态栏框架
    QLabel* m_pStatusIcon;           // 状态图标
    QLabel* m_pStatusText;           // 状态文本

    // UDP 通信相关
    QUdpSocket* udpSocket;
    qint16 port;
    QString localHostName;

    // 心跳管理
    QTimer* heartbeatTimer;
    const int HEARTBEAT_INTERVAL = 1500; // 心跳间隔1.5秒
    const int TIMEOUT_THRESHOLD = 3000;  // 超时阈值3秒
    QMap<QString, QDateTime> lastHeartbeatTime; // 主机名 -> 最后心跳时间
    QMap<QString, QString> userMap; // hostname -> ip,增加用户映射表，防止重复添加

    // 用户列表同步
    QStringList m_onlineUsers;  // 在线用户IP列表
    QString m_sLocalIP;         // 本地IP地址

    // 消息处理防护
    QDateTime m_lastUserListResponseTime; // 最后处理用户列表响应的时间
};

#endif // MONITORWIDGET_H

