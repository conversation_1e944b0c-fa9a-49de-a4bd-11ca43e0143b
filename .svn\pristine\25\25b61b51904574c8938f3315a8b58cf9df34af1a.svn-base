#include "global.h"
#include "tchar.h"
#include <chrono>
#include <QCoreApplication>
#include <QFile>
#include <QFileInfo>
#include <QTextStream>
#include <QDir>

#ifdef _WIN32
#include"windows.h"
#endif

QFontMetrics* g_pFontMetrics = nullptr;
bool g_bWBDlgHidden = false;
int g_WBMode = WB_AUTO;
QList<Calibration> g_caliList;
int g_caliIndex = 0;

int g_zoom[] = { 10, 15, 20, 25, 33, 50, 67, 75, 100, 150, 200, 300, 400 };
int g_zoomcnt = sizeof(g_zoom) / sizeof(g_zoom[0]);
int g_zoomIndex = g_zoomcnt + 2;
double g_zoomScale = 1.0;

double g_zoomWheel[] = { 5, 10, 12.5, 20, 25, 30, 33.3, 40, 50, 60, 66.7, 70, 75, 80, 90, 100,
                        150, 200, 300, 400, 500, 600, 700, 800, 1000, 1200, 1300, 1400, 1500, 1600 };
int g_zoomWheelCnt = sizeof(g_zoomWheel) / sizeof(g_zoomWheel[0]);

int GetZoomWheelIndex(double scale, bool bUp)
{
    scale *= 100;
    for(int i = 0; i < g_zoomWheelCnt; ++i)
    {
        if(fabs(scale - g_zoomWheel[i]) < DF_EP)
            return i;
        else if(scale < g_zoomWheel[i])
        {
            if(0 == i)
                return i;
            else
            {
                if(bUp)
                    return i;
                else
                    return i - 1;
            }
        }
    }
    return g_zoomWheelCnt - 1;
}

int g_measureActType = ACT_NONE;

int g_minResWidth = 100000000;
int g_minResHeight = 100000000;
int g_maxResWidth = 0;
int g_maxResHeight = 0;

/************** Log ******************/
static FILE* g_logfile = nullptr;
#ifdef QT_DEBUG
static LogLevel s_logLevel = Log_Debug;
#else
static LogLevel s_logLevel = Log_Info;
#endif

static void InitLog(const TCHAR* dllPath)
{
    TCHAR szPath[MAX_PATH];
    _tcscpy(szPath, dllPath);
    TCHAR* pc = _tcsrchr(szPath, '.');
    if (pc)
    {
        _tcscpy(pc + 1, _T("log"));
        if (_taccess(szPath, 0) == 0)
            g_logfile = _tfopen(szPath, _T("wt"));
    }
}

void LogInit()
{
    QFile file(QFileInfo(QCoreApplication::applicationFilePath()).absoluteDir().absolutePath() + "//LogLevel.txt");
    if(file.open(QIODevice::ReadOnly | QIODevice::Text))
    {
        QTextStream in(&file);
        QString str = in.readLine();
        if(0 == str.compare("debug", Qt::CaseInsensitive))
            s_logLevel = Log_Debug;
        else if(0 == str.compare("info", Qt::CaseInsensitive))
            s_logLevel = Log_Info;
        else if(0 == str.compare("warning", Qt::CaseInsensitive))
            s_logLevel = Log_Warning;
        else if(0 == str.compare("error", Qt::CaseInsensitive))
            s_logLevel = Log_Error;
        else if(0 == str.compare("critical", Qt::CaseInsensitive))
            s_logLevel = Log_Critical;
        else
            s_logLevel = Log_Info;
        file.close();
    }

    wchar_t path[MAX_PATH] = { 0 };
    if (GetModuleFileNameW(NULL, path, MAX_PATH))
        InitLog(path);
}

static unsigned get_thread_real_id()
{
#if defined(_WIN32)
    return GetCurrentThreadId();
#elif defined(__APPLE__)
    return pthread_mach_thread_np(pthread_self());
#else
    return syscall(__NR_gettid);
#endif
}

static void LogStr(const char* str)
{
    if (g_logfile)
    {
        const std::chrono::time_point<std::chrono::system_clock> nt = std::chrono::system_clock::now();
        const time_t tt = std::chrono::system_clock::to_time_t(nt);
        tm ttm;
        localtime_s(&ttm, &tt);
        fprintf(g_logfile, "[%02hu%02huT%02hu:%02hu:%02hu.%03hu][%u]%s\n", ttm.tm_mon + 1, ttm.tm_mday, ttm.tm_hour, ttm.tm_min, ttm.tm_sec,
                (int)(std::chrono::duration_cast<std::chrono::milliseconds>(nt.time_since_epoch()).count() % 1000), get_thread_real_id(), str);
        fflush(g_logfile);
    }
}

void Log(LogLevel level, const char *format, ...)
{
    if(g_logfile && level >= s_logLevel)
    {
        char str[2048];
        {
            va_list valist;
            va_start(valist, format);
            vsprintf(str, format, valist);
            va_end(valist);
        }
        LogStr(str);
    }
}

void Log(LogLevel level, const QString& str)
{
    if (g_logfile && level >= s_logLevel)
    {
        QByteArray byteArray = str.toUtf8();
        const char* val = byteArray.constData();
        LogStr(val);
    }
}
