@echo off
echo 检查数据库配置文件位置...
echo.

set DEBUG_CONFIG1=D:\work\XCamView\build-XCamView-Desktop_Qt_5_14_2_MSVC2017_64bit-Debug\debug\config\database.ini
set DEBUG_CONFIG2=D:\work\XCamView\build-XCamView-Desktop_Qt_5_14_2_MSVC2017_64bit-Debug\debug\database.ini
set RELEASE_CONFIG1=D:\work\XCamView\build-XCamView-Desktop_Qt_5_14_2_MSVC2017_64bit-Release\release\config\database.ini
set RELEASE_CONFIG2=D:\work\XCamView\build-XCamView-Desktop_Qt_5_14_2_MSVC2017_64bit-Release\release\database.ini

echo === Debug版本配置文件检查 ===
echo.
echo 优先级1: %DEBUG_CONFIG1%
if exist "%DEBUG_CONFIG1%" (
    echo ✅ 存在
    echo 内容:
    type "%DEBUG_CONFIG1%"
) else (
    echo ❌ 不存在
)
echo.

echo 优先级2: %DEBUG_CONFIG2%
if exist "%DEBUG_CONFIG2%" (
    echo ✅ 存在
    echo 内容:
    type "%DEBUG_CONFIG2%"
) else (
    echo ❌ 不存在
)
echo.

echo === Release版本配置文件检查 ===
echo.
echo 优先级1: %RELEASE_CONFIG1%
if exist "%RELEASE_CONFIG1%" (
    echo ✅ 存在
    echo 内容:
    type "%RELEASE_CONFIG1%"
) else (
    echo ❌ 不存在
)
echo.

echo 优先级2: %RELEASE_CONFIG2%
if exist "%RELEASE_CONFIG2%" (
    echo ✅ 存在
    echo 内容:
    type "%RELEASE_CONFIG2%"
) else (
    echo ❌ 不存在
)
echo.

echo === 建议 ===
echo.
echo 推荐配置文件位置:
echo Debug:   %DEBUG_CONFIG1%
echo Release: %RELEASE_CONFIG1%
echo.
echo 如果配置文件不存在，请运行: tools\deploy_config.bat
echo.
pause
