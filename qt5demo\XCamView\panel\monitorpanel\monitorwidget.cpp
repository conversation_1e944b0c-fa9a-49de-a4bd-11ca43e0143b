﻿#include "monitorwidget.h"
#include "../../mainwindow.h"
#include <QCloseEvent>
#include <QVBoxLayout>
#include <QUdpSocket>
#include <QHostInfo>
#include <QDateTime>
#include <QProcess>
#include <QTableView>
#include <QHeaderView>
#include <QDebug> // 添加调试支持
#include <QHBoxLayout>
#include <QGridLayout>
#include <QApplication>
#include <QStyle>
#include <QFont>
#include <QPalette>
#include <QColor>
#include <QNetworkInterface>
#include <QDataStream>

MonitorWidget::MonitorWidget(QWidget *parent)
    : QWidget(parent)
{
    qDebug() << "MonitorWidget: 开始初始化...";
    
    setupUI();
    
    qDebug() << "MonitorWidget: 初始化 UDP 通信...";
    // 初始化本地IP
    m_sLocalIP = getIP();

    // 初始化 UDP 通信
    udpSocket = new QUdpSocket(this);
    port = 12312;
    udpSocket->bind(port, QUdpSocket::ShareAddress | QUdpSocket::ReuseAddressHint);
    connect(udpSocket, &QUdpSocket::readyRead, this, &MonitorWidget::OnReadUDPMessage);
    
    // 首先添加自己到用户列表
    QString myUserName = getUserName();
    QString myIP = getIP();
    NewParticipantIn(myUserName, myUserName, myIP);
    qDebug() << "MonitorWidget: 添加自己到用户列表:" << myUserName << "(" << myIP << ")";

    // 广播自身加入
    SendUDPMessage(NewParticipant);

    // 请求当前用户列表
    QTimer::singleShot(500, this, [this]() {
        SendUDPMessage(UserListRequest);
        qDebug() << "MonitorWidget: 请求当前在线用户列表";
    });
    
    qDebug() << "MonitorWidget: 初始化心跳定时器...";
    // 初始化心跳发送定时器
    heartbeatTimer = new QTimer(this);
    connect(heartbeatTimer, &QTimer::timeout, this, &MonitorWidget::OnSendHeartbeat);
    heartbeatTimer->start(HEARTBEAT_INTERVAL);

    // 初始化心跳检测定时器
    QTimer* checkTimer = new QTimer(this);
    connect(checkTimer, &QTimer::timeout, this, &MonitorWidget::CheckHeartbeat);
    checkTimer->start(HEARTBEAT_INTERVAL); // 每1.5秒检查一次超时
    
    qDebug() << "MonitorWidget: 初始化完成";
}

void MonitorWidget::setupUI()
{
    qDebug() << "MonitorWidget: setupUI 开始...";
    
    // 设置窗口属性
    setWindowTitle("在线用户监控");
    setMinimumSize(450, 600);  // 增加最小尺寸
    
    qDebug() << "MonitorWidget: 创建主布局...";
    // 创建主布局
    QVBoxLayout* mainLayout = new QVBoxLayout(this);
    mainLayout->setSpacing(8);  // 减少间距
    mainLayout->setContentsMargins(12, 12, 12, 12);  // 减少边距
    
    qDebug() << "MonitorWidget: 设置标题栏...";
    // 设置标题栏
    setupTitleBar();
    mainLayout->addWidget(m_pTitleFrame);
    
    qDebug() << "MonitorWidget: 设置树形控件...";
    // 设置树形控件区域
    setupTreeWidget();
    mainLayout->addWidget(m_pTreeFrame);
    
    qDebug() << "MonitorWidget: 设置表格...";
    // 设置表格区域
    setupTableWidget();
    mainLayout->addWidget(m_pTableWidget, 1);  // 给表格区域更多空间
    
    qDebug() << "MonitorWidget: 设置状态栏...";
    // 设置状态栏
    setupStatusBar();
    mainLayout->addWidget(m_pStatusFrame);

    qDebug() << "MonitorWidget: 设置布局...";
    setLayout(mainLayout);
    
    qDebug() << "MonitorWidget: setupUI 完成";
}

void MonitorWidget::setupTitleBar()
{
    qDebug() << "MonitorWidget: setupTitleBar 开始...";
    
    m_pTitleFrame = new QFrame();
    m_pTitleFrame->setFrameStyle(QFrame::Box);
    m_pTitleFrame->setStyleSheet(
        "QFrame { "
        "   background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
        "       stop:0 #4A90E2, stop:1 #357ABD); "
        "   border: 1px solid #2E5A8A; "
        "   border-radius: 6px; "
        "   padding: 6px; "
        "}"
    );
    
    QHBoxLayout* titleLayout = new QHBoxLayout(m_pTitleFrame);
    titleLayout->setContentsMargins(12, 8, 12, 8);
    
    m_pTitleLabel = new QLabel("📊 在线用户监控");
    m_pTitleLabel->setStyleSheet(
        "QLabel { "
        "   color: white; "
        "   font-size: 15px; "
        "   font-weight: bold; "
        "   background: transparent; "
        "}"
    );
    
    titleLayout->addWidget(m_pTitleLabel);
    titleLayout->addStretch();
    
    qDebug() << "MonitorWidget: setupTitleBar 完成";
}

void MonitorWidget::setupTreeWidget()
{
    qDebug() << "MonitorWidget: setupTreeWidget 开始...";
    
    m_pTreeFrame = new QFrame();
    m_pTreeFrame->setFrameStyle(QFrame::Box);
    m_pTreeFrame->setStyleSheet(
        "QFrame { "
        "   background-color: #F8F9FA; "
        "   border: 1px solid #DEE2E6; "
        "   border-radius: 6px; "
        "   padding: 8px; "
        "}"
    );
    
    QVBoxLayout* treeLayout = new QVBoxLayout(m_pTreeFrame);
    treeLayout->setContentsMargins(8, 8, 8, 8);
    treeLayout->setSpacing(6);
    
    QLabel* treeTitle = new QLabel("� 分组选择");
    treeTitle->setStyleSheet(
        "QLabel { "
        "   color: #495057; "
        "   font-size: 13px; "
        "   font-weight: bold; "
        "   margin-bottom: 4px; "
        "}"
    );
    treeLayout->addWidget(treeTitle);
    
    m_pTreeWidget = new QTreeWidget();
    m_pTreeWidget->setMaximumHeight(450);
    m_pTreeWidget->setStyleSheet(
        "QTreeWidget { "
        "   background-color: white; "
        "   border: 1px solid #CED4DA; "
        "   border-radius: 4px; "
        "   padding: 4px; "
        "}"
        "QTreeWidget::item { "
        "   padding: 6px; "
        "   border-radius: 2px; "
        "   margin: 1px; "
        "   height: 20px; "
        "}"
        "QTreeWidget::item:selected { "
        "   background-color: #E3F2FD; "
        "   color: #1976D2; "
        "}"
        "QTreeWidget::item:hover { "
        "   background-color: #F5F5F5; "
        "}"
    );
    
    // 设置树形控件内容
    m_pTreeWidget->setHeaderHidden(true);
    m_pTreeWidget->setRootIsDecorated(true);
    
    QTreeWidgetItem* group1 = new QTreeWidgetItem(m_pTreeWidget);
    group1->setText(0, "1~9");
    group1->setIcon(0, style()->standardIcon(QStyle::SP_ComputerIcon));
    
    // 为1~9组添加子项
    for(int i = 1; i <= 9; i++) {
        QTreeWidgetItem* child = new QTreeWidgetItem(group1);
        child->setText(0, QString("位置 %1").arg(i));
        child->setIcon(0, style()->standardIcon(QStyle::SP_ComputerIcon));
    }
    
    QTreeWidgetItem* group2 = new QTreeWidgetItem(m_pTreeWidget);
    group2->setText(0, "10~18");
    group2->setIcon(0, style()->standardIcon(QStyle::SP_ComputerIcon));
    
    // 为10~18组添加子项
    for(int i = 10; i <= 18; i++) {
        QTreeWidgetItem* child = new QTreeWidgetItem(group2);
        child->setText(0, QString("位置 %1").arg(i));
        child->setIcon(0, style()->standardIcon(QStyle::SP_ComputerIcon));
    }
    
    // 默认收起所有分组
    m_pTreeWidget->collapseAll();
    
    connect(m_pTreeWidget, &QTreeWidget::itemClicked, 
            this, &MonitorWidget::onTreeItemClicked);
    connect(m_pTreeWidget, &QTreeWidget::itemDoubleClicked, 
            this, &MonitorWidget::OnItemDoubleClicked);
    
    treeLayout->addWidget(m_pTreeWidget);
    
    qDebug() << "MonitorWidget: setupTreeWidget 完成";
}

void MonitorWidget::setupTableWidget()
{
    qDebug() << "MonitorWidget: setupTableWidget 开始...";
    
    // 直接创建表格控件，不包QFrame
    m_pTableWidget = new QTableWidget();
    m_pTableWidget->setMinimumHeight(200);
    m_pTableWidget->setContentsMargins(0, 0, 0, 0);
    m_pTableWidget->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Expanding);

    // 设置表格列
    m_pTableWidget->setColumnCount(2);
    m_pTableWidget->setHorizontalHeaderLabels(QStringList() << "用户名" << "IP地址");

    // 表头和内容宽度完全一致
    QHeaderView* header = m_pTableWidget->horizontalHeader();
    header->setSectionResizeMode(0, QHeaderView::Stretch);
    header->setSectionResizeMode(1, QHeaderView::Stretch);
    header->setDefaultAlignment(Qt::AlignCenter);

    // 隐藏垂直表头
    QHeaderView* vHeader = m_pTableWidget->verticalHeader();
    vHeader->setVisible(false);

    // 行高
    m_pTableWidget->verticalHeader()->setDefaultSectionSize(32);

    // 禁止横向滚动条
    m_pTableWidget->setHorizontalScrollBarPolicy(Qt::ScrollBarAlwaysOff);

    // 去掉边框样式
    m_pTableWidget->setFrameStyle(QFrame::NoFrame);

    // 禁止单元格获得焦点，防止选中时内容左对齐
    m_pTableWidget->setFocusPolicy(Qt::NoFocus);
    // 禁止选中，彻底消除选中行左对齐问题
    m_pTableWidget->setSelectionMode(QAbstractItemView::NoSelection);

    // 更新样式表，移除text-align和qproperty-textAlignment
    m_pTableWidget->setStyleSheet(
        "QTableWidget {"
        "  background: #fff;"
        "  border: 1px solid #e0e0e0;"
        "  font-size: 14px;"
        "  selection-background-color: #e3f2fd;"
        "  gridline-color: #f0f0f0;"
        "}"
        "QHeaderView::section {"
        "  background: #f8f9fa;"
        "  color: #495057;"
        "  font-weight: bold;"
        "  font-size: 14px;"
        "  border: none;"
        "  border-bottom: 2px solid #e0e0e0;"
        "  height: 32px;"
        "  padding: 0;"
        "  margin: 0;"
        "}"
        "QTableWidget::item {"
        "  font-size: 14px;"
        "  height: 32px;"
        "  padding: 0;"
        "  margin: 0;"
        "}"
        "QTableWidget::item:selected {"
        "  background: #e3f2fd;"
        "  color: #1976d2;"
        "}"
    );

    qDebug() << "MonitorWidget: setupTableWidget 完成";
}

void MonitorWidget::setupStatusBar()
{
    qDebug() << "MonitorWidget: setupStatusBar 开始...";
    
    m_pStatusFrame = new QFrame();
    m_pStatusFrame->setFrameStyle(QFrame::Box);
    m_pStatusFrame->setStyleSheet(
        "QFrame { "
        "   background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
        "       stop:0 #F8F9FA, stop:1 #E9ECEF); "
        "   border: 1px solid #DEE2E6; "
        "   border-radius: 6px; "
        "   padding: 6px; "
        "}"
    );
    
    QHBoxLayout* statusLayout = new QHBoxLayout(m_pStatusFrame);
    statusLayout->setContentsMargins(12, 6, 12, 6);
    
    m_pStatusIcon = new QLabel("🟢");
    m_pStatusIcon->setStyleSheet(
        "QLabel { "
        "   font-size: 11px; "
        "   background: transparent; "
        "}"
    );
    
    m_pStatusText = new QLabel("系统运行正常");
    m_pStatusText->setStyleSheet(
        "QLabel { "
        "   color: #495057; "
        "   font-size: 11px; "
        "   background: transparent; "
        "}"
    );
    
    m_pNumLabel = new QLabel("在线用户: 0");
    m_pNumLabel->setStyleSheet(
        "QLabel { "
        "   color: #6C757D; "
        "   font-size: 11px; "
        "   background: transparent; "
        "}"
    );
    
    statusLayout->addWidget(m_pStatusIcon);
    statusLayout->addWidget(m_pStatusText);
    statusLayout->addStretch();
    statusLayout->addWidget(m_pNumLabel);
    
    qDebug() << "MonitorWidget: setupStatusBar 完成";
}

void MonitorWidget::onTreeItemClicked(QTreeWidgetItem* item, int column)
{
    if (item) {
        QString groupName = item->text(0);
        
        // 如果点击的是分组项（父项），切换展开/收起状态
        if (item->parent() == nullptr) {
            if (item->isExpanded()) {
                m_pTreeWidget->collapseItem(item);
            } else {
                m_pTreeWidget->expandItem(item);
            }
            qDebug() << "切换分组:" << groupName;
        } else {
            // 如果点击的是子项，处理位置选择
            qDebug() << "选择了位置:" << groupName;
        }
    }
}

void MonitorWidget::updateOnlineCount()
{
    qDebug() << "MonitorWidget: updateOnlineCount 开始...";
    int count = m_pTableWidget->rowCount();
    qDebug() << "MonitorWidget: 表格行数:" << count;
    if (m_pNumLabel) {
        m_pNumLabel->setText(QString("在线用户: %1").arg(count));
        qDebug() << "MonitorWidget: 更新在线用户数完成";
    } else {
        qDebug() << "MonitorWidget: 错误 - m_pNumLabel 为空!";
    }
}

void MonitorWidget::addUserToTable(const QString& username, const QString& ip)
{
    qDebug() << "MonitorWidget: addUserToTable 开始 - 用户名:" << username << "IP:" << ip;
    
    if (!m_pTableWidget) {
        qDebug() << "MonitorWidget: 错误 - m_pTableWidget 为空!";
        return;
    }
    
    int row = m_pTableWidget->rowCount();
    qDebug() << "MonitorWidget: 当前行数:" << row;
    
    m_pTableWidget->insertRow(row);
    qDebug() << "MonitorWidget: 插入新行完成";
    
    QTableWidgetItem* userItem = new QTableWidgetItem(username);
    QTableWidgetItem* ipItem = new QTableWidgetItem(ip);
    userItem->setTextAlignment(Qt::AlignCenter);
    ipItem->setTextAlignment(Qt::AlignCenter);
    m_pTableWidget->setItem(row, 0, userItem);
    m_pTableWidget->setItem(row, 1, ipItem);
    qDebug() << "MonitorWidget: 设置表格项完成";
    
    // 强制所有内容都居中（包括动态数据）
    for (int r = 0; r < m_pTableWidget->rowCount(); ++r) {
        for (int c = 0; c < m_pTableWidget->columnCount(); ++c) {
            QTableWidgetItem* item = m_pTableWidget->item(r, c);
            if (item) item->setTextAlignment(Qt::AlignCenter);
        }
    }
    
    // 强制刷新表格视图
    m_pTableWidget->viewport()->update();

    updateOnlineCount();
    qDebug() << "MonitorWidget: addUserToTable 完成";
}

// 添加缺少的方法实现
MonitorWidget::~MonitorWidget()
{
    if (heartbeatTimer) {
        heartbeatTimer->stop();
    }
}

void MonitorWidget::Notify(int notify)
{
    // 处理通知
    qDebug() << "MonitorWidget received notification:" << notify;
}

void MonitorWidget::SendUDPMessage(MessageType type, QString serverAddress)
{
    QByteArray datagram;
    QDataStream out(&datagram, QIODevice::WriteOnly);
    out << type << getUserName() << getIP();

    if (serverAddress.isEmpty()) {
        udpSocket->writeDatagram(datagram, QHostAddress::Broadcast, port);
    } else {
        udpSocket->writeDatagram(datagram, QHostAddress(serverAddress), port);
    }
}

void MonitorWidget::sendUserListResponse()
{
    QByteArray datagram;
    QDataStream out(&datagram, QIODevice::WriteOnly);
    out.setVersion(QDataStream::Qt_5_14);

    // 发送用户列表响应
    out << UserListResponse << getUserName() << getIP();

    // 添加在线用户列表（包括自己）
    int totalUsers = m_onlineUsers.size();
    out << totalUsers;

    qDebug() << "MonitorWidget: 准备发送用户列表响应，包含" << totalUsers << "个用户:";

    for (const QString& ip : m_onlineUsers) {
        QString userName = "未知用户";
        // 从userMap中查找用户名
        for (auto it = userMap.begin(); it != userMap.end(); ++it) {
            if (it.value() == ip) {
                userName = it.key();
                break;
            }
        }
        out << ip << userName;
        qDebug() << "  - " << userName << "(" << ip << ")";
    }

    // 广播响应
    udpSocket->writeDatagram(datagram, QHostAddress::Broadcast, port);

    qDebug() << "MonitorWidget: 用户列表响应已发送";
}

void MonitorWidget::NewParticipantIn(QString userName, QString localHostName, QString ipAddress)
{
    // 检查是否已经存在（通过IP地址检查，更可靠）
    bool userExists = false;
    for (int i = 0; i < m_pTableWidget->rowCount(); ++i) {
        if (m_pTableWidget->item(i, 1) && m_pTableWidget->item(i, 1)->text() == GetDisplayIP(ipAddress)) {
            userExists = true;
            qDebug() << "MonitorWidget: 用户已存在于表格中:" << userName << "(" << ipAddress << ")";
            break;
        }
    }

    if (!userExists && !userMap.contains(localHostName)) {
        userMap[localHostName] = ipAddress;

        // 添加到在线用户列表（包括自己）
        if (!m_onlineUsers.contains(ipAddress)) {
            m_onlineUsers.append(ipAddress);
        }

        int row = m_pTableWidget->rowCount();
        m_pTableWidget->insertRow(row);

        QTableWidgetItem* userItem = new QTableWidgetItem(userName);
        QTableWidgetItem* ipItem = new QTableWidgetItem(GetDisplayIP(ipAddress));
        userItem->setTextAlignment(Qt::AlignCenter);
        ipItem->setTextAlignment(Qt::AlignCenter);

        // 如果是自己，设置不同的显示样式
        if (ipAddress == m_sLocalIP) {
            QFont boldFont = userItem->font();
            boldFont.setBold(true);
            userItem->setFont(boldFont);
            ipItem->setFont(boldFont);
            userItem->setToolTip("这是您自己");
            ipItem->setToolTip("这是您自己");
        }

        m_pTableWidget->setItem(row, 0, userItem);
        m_pTableWidget->setItem(row, 1, ipItem);

        // 更新在线人数
        m_pNumLabel->setText(QString("在线用户: %1").arg(m_pTableWidget->rowCount()));

        // 记录心跳时间
        lastHeartbeatTime[localHostName] = QDateTime::currentDateTime();

        qDebug() << "MonitorWidget: 成功添加用户:" << userName << "(" << ipAddress << ") 表格行数:" << m_pTableWidget->rowCount() << "在线列表:" << m_onlineUsers.size();
        qDebug() << "MonitorWidget: 当前在线用户列表:" << m_onlineUsers;

        // 强制刷新表格显示
        m_pTableWidget->viewport()->update();
    } else {
        qDebug() << "MonitorWidget: 跳过重复用户:" << userName << "(" << ipAddress << ") 已存在:" << userExists << "映射存在:" << userMap.contains(localHostName);
    }
}

void MonitorWidget::ParticipantLeft(QString userName, QString localHostName, QString time)
{
    Q_UNUSED(time);

    // 获取用户IP
    QString ipAddress = userMap.value(localHostName);

    // 从表格中移除用户
    for (int i = 0; i < m_pTableWidget->rowCount(); ++i) {
        if (m_pTableWidget->item(i, 0)->text() == userName) {
            m_pTableWidget->removeRow(i);
            break;
        }
    }

    // 从在线用户列表中移除
    if (!ipAddress.isEmpty()) {
        m_onlineUsers.removeAll(ipAddress);
    }

    // 从映射表中移除
    userMap.remove(localHostName);
    lastHeartbeatTime.remove(localHostName);

    // 更新在线人数
    m_pNumLabel->setText(QString("在线用户: %1").arg(m_pTableWidget->rowCount()));

    qDebug() << "MonitorWidget: 用户离开:" << userName << "(" << ipAddress << ") 当前在线:" << m_onlineUsers.size();
}

void MonitorWidget::CheckHeartbeat()
{
    QDateTime currentTime = QDateTime::currentDateTime();
    QStringList timeoutUsers;

    // 检查所有用户的心跳超时
    for (auto it = lastHeartbeatTime.begin(); it != lastHeartbeatTime.end(); ++it) {
        qint64 timeDiff = it.value().msecsTo(currentTime);
        if (timeDiff > TIMEOUT_THRESHOLD) {
            timeoutUsers.append(it.key());
            qDebug() << "MonitorWidget: 检测到用户超时:" << it.key() << "超时时间:" << timeDiff << "ms";
        }
    }

    // 移除超时用户
    for (const QString& hostname : timeoutUsers) {
        QString ipAddress = userMap.value(hostname);

        qDebug() << "MonitorWidget: 移除超时用户:" << hostname << "(" << ipAddress << ")";

        // 从表格中移除
        for (int i = 0; i < m_pTableWidget->rowCount(); ++i) {
            QTableWidgetItem* ipItem = m_pTableWidget->item(i, 1);
            if (ipItem && ipItem->text() == GetDisplayIP(ipAddress)) {
                m_pTableWidget->removeRow(i);
                qDebug() << "MonitorWidget: 从表格移除用户，行号:" << i;
                break;
            }
        }

        // 从在线用户列表中移除
        m_onlineUsers.removeAll(ipAddress);

        // 从映射表中移除
        lastHeartbeatTime.remove(hostname);
        userMap.remove(hostname);
    }

    if (!timeoutUsers.isEmpty()) {
        // 更新在线人数
        m_pNumLabel->setText(QString("在线用户: %1").arg(m_pTableWidget->rowCount()));
        qDebug() << "MonitorWidget: 心跳检测完成，移除" << timeoutUsers.size() << "个超时用户，当前在线:" << m_pTableWidget->rowCount();
    }
}

void MonitorWidget::OnSendHeartbeat()
{
    // 发送心跳包
    SendUDPMessage(Heartbeat);

    // 更新自己的心跳时间（防止自己被超时移除）
    QString myUserName = getUserName();
    lastHeartbeatTime[myUserName] = QDateTime::currentDateTime();

    // qDebug() << "MonitorWidget: 发送心跳包:" << myUserName;
}

QString MonitorWidget::getIP()
{
    QString ipAddress;
    QList<QHostAddress> ipAddressesList = QNetworkInterface::allAddresses();
    for (int i = 0; i < ipAddressesList.size(); ++i) {
        if (ipAddressesList.at(i) != QHostAddress::LocalHost &&
            ipAddressesList.at(i).toIPv4Address()) {
            ipAddress = ipAddressesList.at(i).toString();
            break;
        }
    }
    if (ipAddress.isEmpty())
        ipAddress = QHostAddress(QHostAddress::LocalHost).toString();
    return ipAddress;
}

QString MonitorWidget::getUserName()
{
    return QHostInfo::localHostName();
}

QString MonitorWidget::getMessage()
{
    return QString("Hello from %1").arg(getUserName());
}

void MonitorWidget::closeEvent(QCloseEvent *event)
{
    SendUDPMessage(Participantleft);
    event->accept();
}

void MonitorWidget::OnItemDoubleClicked(QTreeWidgetItem *item, int column)
{
    if (item) {
        qDebug() << "Double clicked on item:" << item->text(0);
    }
}

void MonitorWidget::OnReadUDPMessage()
{
    while (udpSocket->hasPendingDatagrams()) {
        QByteArray datagram;
        datagram.resize(udpSocket->pendingDatagramSize());
        QHostAddress sender;
        quint16 senderPort;
        
        udpSocket->readDatagram(datagram.data(), datagram.size(), &sender, &senderPort);

        QDataStream in(datagram);
        in.setVersion(QDataStream::Qt_5_14); // 设置数据流版本保持一致

        MessageType type;
        QString userName, ipAddress;

        in >> type >> userName >> ipAddress;

        // 检查基本数据是否有效
        if (userName.isEmpty() && ipAddress.isEmpty() && type != Heartbeat) {
            qDebug() << "MonitorWidget: 收到无效消息，跳过";
            continue;
        }
        
        switch (type) {
        case Heartbeat:
            // 更新心跳时间（只处理其他用户的心跳）
            if (ipAddress != m_sLocalIP) {
                lastHeartbeatTime[userName] = QDateTime::currentDateTime();
                // qDebug() << "MonitorWidget: 收到心跳:" << userName << "(" << ipAddress << ")";
            }
            break;
        case Message:
            // 处理消息
            break;
        case NewParticipant:
            // 只有当不是自己时才处理新用户加入
            if (ipAddress != m_sLocalIP) {
                NewParticipantIn(userName, userName, ipAddress);
                // 新用户加入时，发送用户列表响应
                sendUserListResponse();
                qDebug() << "MonitorWidget: 处理新用户加入:" << userName << "(" << ipAddress << ")";
            } else {
                qDebug() << "MonitorWidget: 跳过自己的NewParticipant消息";
            }
            break;
        case Participantleft:
            ParticipantLeft(userName, userName, "");
            break;
        case FileName:
            // 处理文件名
            break;
        case Refuse:
            // 处理拒绝消息
            break;

        case UserListRequest:
            {
                QString requestUserName, requestIP;
                in >> requestUserName >> requestIP;

                // 如果不是自己的请求，发送用户列表响应
                if (requestIP != m_sLocalIP) {
                    qDebug() << "MonitorWidget: 收到来自" << requestUserName << "(" << requestIP << ")的用户列表请求";
                    qDebug() << "MonitorWidget: 当前在线用户数:" << m_onlineUsers.size() << "表格行数:" << m_pTableWidget->rowCount();
                    sendUserListResponse();
                } else {
                    qDebug() << "MonitorWidget: 跳过自己的用户列表请求";
                }
            }
            break;

        case UserListResponse:
            {
                // 防护：避免过于频繁的用户列表响应处理
                QDateTime now = QDateTime::currentDateTime();
                if (m_lastUserListResponseTime.isValid() &&
                    m_lastUserListResponseTime.msecsTo(now) < 100) {
                    qDebug() << "MonitorWidget: 用户列表响应处理过于频繁，跳过";
                    break;
                }
                m_lastUserListResponseTime = now;

                QString responseUserName, responseIP;
                in >> responseUserName >> responseIP;

                // 检查响应数据是否有效
                if (responseUserName.isEmpty() || responseIP.isEmpty()) {
                    qDebug() << "MonitorWidget: 收到无效的用户列表响应，跳过";
                    break;
                }

                // 如果不是自己的响应，处理用户列表
                if (responseIP != m_sLocalIP) {
                    int userCount;
                    in >> userCount;

                    // 检查用户数量是否合理
                    if (userCount < 0 || userCount > 100) {
                        qDebug() << "MonitorWidget: 用户数量异常:" << userCount << "，跳过处理";
                        break;
                    }

                    qDebug() << "MonitorWidget: 收到来自" << responseUserName << "(" << responseIP << ")的用户列表响应，包含" << userCount << "个用户";

                    // 处理用户列表
                    for (int i = 0; i < userCount; ++i) {
                        QString userIP, userUserName;
                        in >> userIP >> userUserName;

                        // 检查用户数据是否有效
                        if (userIP.isEmpty() || userUserName.isEmpty()) {
                            qDebug() << "MonitorWidget: 跳过无效用户数据:" << userUserName << "(" << userIP << ")";
                            continue;
                        }

                        // 添加到本地用户列表（如果不存在且不是自己）
                        if (!m_onlineUsers.contains(userIP) && userIP != m_sLocalIP) {
                            NewParticipantIn(userUserName, userUserName, userIP);
                            qDebug() << "MonitorWidget: 从用户列表响应中添加用户:" << userUserName << "(" << userIP << ")";
                        } else if (userIP == m_sLocalIP) {
                            qDebug() << "MonitorWidget: 跳过自己的用户信息:" << userUserName << "(" << userIP << ")";
                        } else {
                            qDebug() << "MonitorWidget: 用户已存在，跳过:" << userUserName << "(" << userIP << ")";
                        }
                    }

                    qDebug() << "MonitorWidget: 用户列表同步完成，当前在线用户数:" << m_onlineUsers.size();
                } else {
                    qDebug() << "MonitorWidget: 跳过自己的用户列表响应";
                }
            }
            break;
        }
    }
}

QString MonitorWidget::GetDisplayIP(const QString& rawIP)
{
    // 处理IP格式显示
    return rawIP;
}
