#include "imageview.h"
#include "measure/tpmeasuremanager.h"
#include <QPainter>
#include <QPaintEvent>
#include <QWheelEvent>
#include <QHBoxLayout>

ImageWidget::ImageWidget(const QImage* pImage, QWidget* parent)
    : DisplayWidget(parent)
{
    SetImage(pImage);
}

void ImageWidget::SetImage(const QImage *pImage)
{
    if(pImage)
    {
        if(nullptr == m_pImage)
            m_pImage = new QImage();
        *m_pImage = *pImage;
    }
    else
    {
        const int width = g_pCameraManager->GetImageWidth();
        const int height = g_pCameraManager->GetImageHeight();
        if(m_pImage && (m_pImage->width() != width || m_pImage->height() != height))
        {
            delete m_pImage;
            m_pImage = nullptr;
        }
        if(nullptr == m_pImage)
            m_pImage = new QImage(width, height, QImage::Format_RGB888);
        if(m_pImage)
            g_pCameraManager->GetImage(m_pImage->bits());
    }
    m_ptCenter = m_pImage->rect().center();
    m_pMeasureManager->setMSize(m_pImage->width(), m_pImage->height());
    DoZoomChanged();
    update();
}

int ImageView::s_cnt = 0;

ImageView::ImageView(const QImage* pImage, QWidget *parent)
    : DisplayView(new ImageWidget(pImage), parent)
{
    ++s_cnt;
}

ImageView::~ImageView()
{
    --s_cnt;
}

void ImageView::SetImage(const QImage *pImage)
{
    ImageWidget* pWidget = qobject_cast<ImageWidget*>(m_pViewWidget);
    if(pWidget)
        pWidget->SetImage(pImage);
}
