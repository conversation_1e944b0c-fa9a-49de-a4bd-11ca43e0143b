﻿#include "cameralistwidget.h"
#include <QPushButton>
#include <QPainter>

class CamListBtn : public QPushButton
{
public:
    CamListBtn(const QString& text, QWidget* parent = nullptr);

protected:
    void paintEvent(QPaintEvent*) override;
    void enterEvent(QEnterEvent*) override;
    void leaveEvent(QEvent*) override;

private:
    bool bEnter_;
};

CamListBtn::CamListBtn(const QString& text, QWidget* parent)
    : QPushButton(text, parent), bEnter_(false)
{
    setStyleSheet("QPushButton{color: rgb(0, 0, 0);}");
}

void CamListBtn::paintEvent(QPaintEvent*)
{
    QPainter painter(this);
    QRect rc = this->rect();
    if (bEnter_)
    {
        QFont font = painter.font();
        font.setUnderline(true);
        painter.setFont(font);
    }
    painter.drawText(rc, Qt::AlignLeft | Qt::AlignVCenter, this->text());
}

void CamListBtn::enterEvent(QEnterEvent*)
{
    if (isEnabled())
    {
        bEnter_ = true;
        setCursor(Qt::PointingHandCursor);
        update();
    }
}

void CamListBtn::leaveEvent(QEvent*)
{
    if (isEnabled())
    {
        bEnter_ = false;
        update();
    }
}

CameraListWidget::CameraListWidget(QWidget* parent)
    : CollapseWidget(QIcon(":/images/camera.png"), g_i18n->value("ids_camera_list"), parent)
{
    buttonGroup_ = new QButtonGroup(this);
    connect(buttonGroup_, SIGNAL(idClicked(int)), this, SLOT(OnButtonGroupClicked(int)));

    mainLyt_ = new QVBoxLayout();
    setLayout(mainLyt_);

    HotPlug();
}

void CameraListWidget::Notify(int notify)
{
    if(NOTIFY_DEV_CHANGED == notify)
        HotPlug();
}

void CameraListWidget::OnButtonGroupClicked(int id)
{
    if(g_cameras[id].name != g_pCameraManager->GetCamInfo().name)
    {
        g_pCameraManager->Close();
        g_pCameraManager->Open(g_cameras[id]);
        g_pCameraManager->Start();
    }
}

void CameraListWidget::HotPlug()
{
    for (int i = 0; i < btns_.count(); ++i)
    {
        buttonGroup_->removeButton(btns_[i]);
        mainLyt_->removeWidget(btns_[i]);
        delete btns_[i];
    }
    btns_.clear();

    for (int i = 0; i < hFrames_.count(); ++i)
    {
        mainLyt_->removeWidget(hFrames_[i]);
        delete hFrames_[i];
    }
    hFrames_.clear();

    g_cameras = g_pCameraManager->Enum();
    if (g_cameras.count() > 0)
    {
        for (int i = 0; i < g_cameras.count(); ++i)
        {
            btns_.append(new CamListBtn(g_cameras[i].name));
            buttonGroup_->addButton(btns_[i], i);
        }
    }
    else
    {
        btns_.append(new CamListBtn(g_i18n->value("ids_nodevice")));
        btns_[0]->setEnabled(false);
    }

    for (int i = 0; i < btns_.count(); ++i)
    {
        if (0 == i)
        {
            QFrame* hframe = new QFrame(this);
            hframe->setFrameShape(QFrame::HLine);
            hframe->setStyleSheet("QFrame{color:gray;}");
            hFrames_.append(hframe);
            mainLyt_->addWidget(hframe);
        }
        mainLyt_->addWidget(btns_[i]);
        {
            QFrame* hframe = new QFrame(this);
            hframe->setFrameShape(QFrame::HLine);
            hframe->setStyleSheet("QFrame{color:gray;}");
            hFrames_.append(hframe);
            mainLyt_->addWidget(hframe);
        }
    }
}
