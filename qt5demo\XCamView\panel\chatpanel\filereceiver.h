#ifndef FILERECEIVER_H
#define FILERECEIVER_H

#include <QObject>
#include <QTcpSocket>
#include <QFile>
#include <QTime>
#include <QHostAddress>

class FileReceiver : public QObject
{
    Q_OBJECT
public:
    explicit FileReceiver(QObject *parent = nullptr);
    ~FileReceiver();

    void startReceiving(const QHostAddress &senderAddress,
                       const QString &saveDirectory,
                       quint16 port = 6688);

    void stopReceiving();

signals:
    // 传输进度 (已接收字节, 总字节, 传输速度MB/s)
    void progressUpdated(qint64 received, qint64 total, double speedMBs);

    // 传输完成
    void transferFinished(const QString &filePath);

    // 错误信息
    void errorOccurred(const QString &errorMessage);

    // 状态变化
    void statusChanged(const QString &status);

private slots:
    void readIncomingData();
    void handleSocketError(QAbstractSocket::SocketError socketError);

private:
    void processFileHeader();
    void saveToFile(const QByteArray &data);

    QTcpSocket *m_socket = nullptr;
    QFile *m_file = nullptr;

    qint64 m_totalBytes = 0;
    qint64 m_bytesReceived = 0;
    qint64 m_fileNameSize = 0;
    QString m_fileName;
    QString m_savePath;

    QByteArray m_inBlock;
    QTime m_timer;
    bool m_headerProcessed = false;
};

#endif // FILERECEIVER_H
