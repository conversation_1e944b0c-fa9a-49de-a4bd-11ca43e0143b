#include "view.h"
#include "../../mainwindow.h"
#include <QMouseEvent>
#include <QPainter>
#include <QPaintEvent>
#include <QWheelEvent>
#include <QResizeEvent>
#include <QHBoxLayout>
#include <QVBoxLayout>

ViewWidget::ViewWidget(QWidget* parent)
    : QWidget(parent), m_pImage(nullptr), m_fScale(1.0), m_defCurShape(Qt::ArrowCursor)
{
    setMouseTracking(true);
}

ViewWidget::~ViewWidget()
{
    if(m_pImage)
        delete m_pImage;
}

void ViewWidget::Notify(int notify)
{
    Q_UNUSED(notify);
}

void ViewWidget::SetOffsetX(int x)
{
    m_ptOffSet.setX(x);
    update();
}

void ViewWidget::SetOffsetY(int y)
{
    m_ptOffSet.setY(y);
    update();
}

QPoint ViewWidget::GetOffsetPt() const
{
    return m_ptOffSet;
}

void ViewWidget::SetOffsetPt(const QPoint &pt)
{
    m_ptOffSet = pt;
    update();
}

double ViewWidget::GetScale() const
{
    return m_fScale;
}

void ViewWidget::SetScale(double scale)
{
    m_fScale = scale;
    update();
}

QSize ViewWidget::GetImageSize()
{
    if(m_pImage)
        return m_pImage->size();
    return QSize();
}

QSize ViewWidget::GetWinowSize()
{
    return this->size();
}

QRgb ViewWidget::GetImageValue(int x, int y)
{
    if(m_pImage)
        return m_pImage->pixel(x, y);
    return 0;
}

void ViewWidget::SetDefCurShape(Qt::CursorShape defCurShape)
{
    setCursor(defCurShape);
    m_defCurShape = defCurShape;
}

void ViewWidget::FitToWinWidth()
{
    if(m_pImage)
    {
        m_fScale = static_cast<double>(this->width() - 20) / m_pImage->width();
        update();
    }
}

void ViewWidget::FitToWinHeight()
{
    if(m_pImage)
    {
        m_fScale = static_cast<double>(this->height() - 20) / m_pImage->height();
        update();
    }
}

void ViewWidget::FitToWindow()
{
    if(m_pImage)
    {
        const int width = m_pImage->width();
        const int height = m_pImage->height();
        const int winWidth = this->width() - 20;
        const int winHeight = this->height() - 20;
        double scaleX = static_cast<double>(winWidth) / width;
        double scaleY = static_cast<double>(winHeight) / height;
        m_fScale = scaleX < scaleY ? scaleX : scaleY;
        update();
    }
}

void ViewWidget::paintEvent(QPaintEvent *event)
{
    QPainter painter(this);
    painter.fillRect(event->rect(), QColor(171, 171, 171));
    painter.save();
    painter.translate(event->rect().center());
    painter.scale(m_fScale, m_fScale);
    painter.translate(-m_ptOffSet);
    painter.drawImage(m_pImage->rect().translated(-m_ptCenter), *m_pImage);
    paint(painter);
    painter.restore();
}

void ViewWidget::wheelEvent(QWheelEvent *event)
{
    if (event->angleDelta().y() > 0)
    {
        int index = GetZoomWheelIndex(m_fScale, false) + 1;
        m_fScale = g_zoomWheel[index < g_zoomWheelCnt ? index : g_zoomWheelCnt - 1] / 100;
    }
    else
    {
        int index = GetZoomWheelIndex(m_fScale, true) - 1;
        m_fScale = g_zoomWheel[index > 0 ? index : 0] / 100;
    }
    update();
    event->accept();
    emit UpdateScrollBar();
    emit ScaleChanged(m_fScale);
    g_pMainWindow->SetZoomScale(m_fScale);
}

void ViewWidget::paint(QPainter &painter)
{
    Q_UNUSED(painter);
}

QPointF ViewWidget::MapToImageCoord(const QPointF &pt)
{
    if(!m_pImage)
        return QPointF();

    QPointF lpt = pt - this->rect().center();
    const int width = m_pImage->width();
    const int height = m_pImage->height();
    lpt = lpt / m_fScale + m_ptOffSet + m_ptCenter;
    if (lpt.x() < 0)
        lpt.setX(0);
    if (lpt.y() < 0)
        lpt.setY(0);
    if (lpt.x() > width)
        lpt.setX(width);
    if (lpt.y() > height)
        lpt.setY(height);
    lpt = QPointF(lpt.toPoint());
    return lpt;
}

void ViewWidget::DoZoomChanged()
{
    if(g_zoomIndex >= 0)
    {
        if(g_zoomIndex < g_zoomcnt)
            m_fScale = g_zoom[g_zoomIndex] / 100.0;
        else
        {
            switch(g_zoomIndex - g_zoomcnt)
            {
            case FIT_TO_WIDTH: FitToWinWidth(); break;
            case FIT_TO_HEIGHT: FitToWinHeight(); break;
            case FIT_TO_WINDOW: FitToWindow(); break;
            default: break;
            }
            g_pMainWindow->SetZoomScale(m_fScale);
        }
        g_zoomScale = m_fScale;
    }
    else
    {
        m_fScale = g_zoomScale;
    }
    DoZoomChangedExt();
    update();
    emit UpdateScrollBar();
}

void ViewWidget::DoZoomChangedExt()
{

}

View::View(ViewWidget* pViewWidget, QWidget *parent)
    : QWidget(parent), m_pHorScrollBar(nullptr), m_pVerScrollBar(nullptr), m_pViewWidget(pViewWidget)
    , m_bLeftPressed(false)
{
    setMouseTracking(true);
    m_pHorScrollBar = new QScrollBar(Qt::Horizontal);
    m_pVerScrollBar = new QScrollBar(Qt::Vertical);

    QHBoxLayout* hlyt1 = new QHBoxLayout();
    hlyt1->setSpacing(0);
    hlyt1->setContentsMargins(0, 0, 0, 0);
    hlyt1->addWidget(m_pViewWidget);
    hlyt1->addWidget(m_pVerScrollBar);
    QHBoxLayout* hlyt2 = new QHBoxLayout;
    hlyt2->setContentsMargins(0, 0, 0, 0);
    hlyt2->setSpacing(0);
    hlyt2->addWidget(m_pHorScrollBar);
    hlyt2->setContentsMargins(0, 0, m_pVerScrollBar->sizeHint().width(), 0);
    QVBoxLayout* vlyt = new QVBoxLayout;
    vlyt->setContentsMargins(0, 0, 0, 0);
    vlyt->setSpacing(0);
    vlyt->addLayout(hlyt1);
    vlyt->addLayout(hlyt2);
    setLayout(vlyt);

    connect(m_pViewWidget, SIGNAL(UpdateScrollBar()), this, SLOT(OnUpdateScrollBar()));
    connect(m_pHorScrollBar, SIGNAL(valueChanged(int)), this, SLOT(OnHorScrollBarChanged(int)));
    connect(m_pVerScrollBar, SIGNAL(valueChanged(int)), this, SLOT(OnVerScrollBarChanged(int)));

    m_pHorScrollBar->close();
    m_pVerScrollBar->close();
}

void View::Notify(int notify)
{
    if(m_pViewWidget)
        m_pViewWidget->Notify(notify);
}

void View::resizeEvent(QResizeEvent *event)
{
    QWidget::resizeEvent(event);
    OnUpdateScrollBar();
}

void View::mousePressEvent(QMouseEvent *event)
{
    if(event->button() == Qt::LeftButton)
    {
        m_bLeftPressed = true;
        if(m_pHorScrollBar->isVisible() || m_pVerScrollBar->isVisible())
            m_begPt = event->pos();
        UpdateCursor();
    }
    QWidget::mousePressEvent(event);
}

void View::mouseReleaseEvent(QMouseEvent *event)
{
    if(event->button() == Qt::LeftButton)
    {
        m_bLeftPressed = false;
        UpdateCursor();
    }
    QWidget::mouseReleaseEvent(event);
}

void View::mouseMoveEvent(QMouseEvent *event)
{
    if(m_bLeftPressed && (m_pHorScrollBar->isVisible() || m_pVerScrollBar->isVisible()))
    {
        const QPoint ptOffset = m_pViewWidget->GetOffsetPt();
        QPoint newOffset =  ptOffset + (m_begPt - event->pos()) / m_pViewWidget->GetScale();
        if(m_pHorScrollBar->isVisible())
        {
            if(newOffset.x() < m_pHorScrollBar->minimum()) newOffset.setX(m_pHorScrollBar->minimum());
            if(newOffset.x() > m_pHorScrollBar->maximum()) newOffset.setX(m_pHorScrollBar->maximum());
            m_pHorScrollBar->blockSignals(true);
            m_pHorScrollBar->setValue(newOffset.x());
            m_pHorScrollBar->blockSignals(false);
        }
        if(m_pVerScrollBar->isVisible())
        {
            if(newOffset.y() < m_pVerScrollBar->minimum()) newOffset.setY(m_pVerScrollBar->minimum());
            if(newOffset.y() > m_pVerScrollBar->maximum()) newOffset.setY(m_pVerScrollBar->maximum());
            m_pVerScrollBar->blockSignals(true);
            m_pVerScrollBar->setValue(newOffset.y());
            m_pVerScrollBar->blockSignals(false);
        }
        m_begPt = event->pos();
        m_pViewWidget->SetOffsetPt(newOffset);
    }
    QWidget::mouseMoveEvent(event);
}

void View::OnUpdateScrollBar()
{
    const QSize imgSize = m_pViewWidget->GetImageSize();
    const QSize winSize = m_pViewWidget->GetWinowSize();
    const double scale = m_pViewWidget->GetScale();
    const double winWidth = winSize.width() / scale;
    const double winHeight = winSize.height() / scale;
    const double wDelta = imgSize.width() - winWidth;
    const double hDelta = imgSize.height() - winHeight;
    const QPoint offsetPt = m_pViewWidget->GetOffsetPt();
    if(wDelta > 0)
    {
        m_pHorScrollBar->show();
        m_pHorScrollBar->setRange(-wDelta / 2, wDelta / 2);
        if(offsetPt.x() != m_pHorScrollBar->value())
            m_pHorScrollBar->setValue(offsetPt.x());
    }
    else
    {
        OnHorScrollBarChanged(0);
        m_pHorScrollBar->close();
    }

    if(hDelta > 0)
    {
        m_pVerScrollBar->show();
        m_pVerScrollBar->setRange(-hDelta / 2, hDelta / 2);
        if(offsetPt.y() != m_pVerScrollBar->value())
            m_pVerScrollBar->setValue(offsetPt.y());
    }
    else
    {
        OnVerScrollBarChanged(0);
        m_pVerScrollBar->close();
    }
    UpdateCursor();
}

void View::OnHorScrollBarChanged(int x)
{
    m_pViewWidget->SetOffsetX(x);
}

void View::OnVerScrollBarChanged(int y)
{
    m_pViewWidget->SetOffsetY(y);
}

void View::UpdateCursor()
{
    if(m_pHorScrollBar->isVisible() || m_pVerScrollBar->isVisible())
    {
        if(m_bLeftPressed)
            m_pViewWidget->SetDefCurShape(Qt::ClosedHandCursor);
        else
            m_pViewWidget->SetDefCurShape(Qt::OpenHandCursor);
    }
    else
        m_pViewWidget->SetDefCurShape(Qt::ArrowCursor);
}
