<?xml version="1.0" encoding="utf-8"?>
<i18n>
	<str id="ids_message" val="Message"/>
	<str id="ids_no_uvcham_dll" val="Not found uvcham.dll"/>
	<str id="ids_cam_not_connect" val="Camera has been not connected!"/>
	<str id="ids_unknow_error" val="Unknow Error"/>
	<str id="ids_file" val="File"/>
	<str id="ids_open_image" val="Open Image"/>
	<str id="ids_save" val="Save"/>
	<str id="ids_quicksave" val="Quick Save"/>
	<str id="ids_preferences" val="Preferences"/>
	<str id="ids_help" val="Help"/>
	<str id="ids_about" val="About"/>
	<str id="ids_camera" val="Camera"/>
	<str id="ids_camera_list" val="Camera List"/>
	<str id="ids_nodevice" val="No Camera"/>
	<str id="ids_exposure_gain" val="Exposure &amp;amp; Gain"/>
	<str id="ids_auto_exposure" val="Auto Exposure"/>
	<str id="ids_expo_target" val="Exposure Target"/>
	<str id="ids_expo_time" val="Exposure Time"/>
	<str id="ids_gain" val="Gain"/>
	<str id="ids_defaults" val="Defaults"/>
	<str id="ids_colon" val=":"/>
	<str id="ids_resolution" val="Resolution"/>
	<str id="ids_live" val="Live"/>
	<str id="ids_white_balance" val="White Balance"/>
	<str id="ids_red" val="Red"/>
	<str id="ids_green" val="Green"/>
	<str id="ids_blue" val="Blue"/>
	<str id="ids_global_auto" val="Global Auto"/>
	<str id="ids_manual" val="Manual"/>
	<str id="ids_coloradjust" val="Color Adjustment"/>
	<str id="ids_hue" val="Hue"/>
	<str id="ids_saturation" val="Saturation"/>
	<str id="ids_brightness" val="Brightness"/>
	<str id="ids_contrast" val="Contrast"/>
	<str id="ids_gamma" val="Gamma"/>
	<str id="ids_power_frequency" val="Power Frequency (Anti-flicker)"/>
	<str id="ids_ac50" val="AC (50 Hz)"/>
	<str id="ids_ac60" val="AC (60 Hz)"/>
	<str id="ids_dc" val="DC"/>
	<str id="ids_chat_room" val="Chat Room"/>
	<str id="ids_monitor" val="Monitor"/>
	<str id="ids_measurement" val="Measurement"/>
	<str id="ids_obj_select" val="Object Select"/>
	<str id="ids_point" val="Point"/>
	<str id="ids_arrow" val="Arrow"/>
	<str id="ids_angle" val="Angle"/>
	<str id="ids_angle3p" val="Angle (3 Points)"/>
	<str id="ids_angle4p" val="Angle (4 Points)"/>
	<str id="ids_vertical" val="Vertical"/>
	<str id="ids_vertical3pt" val="Vertical (3 Points)"/>
	<str id="ids_vertical4pt" val="Vertical (4 Points)"/>
	<str id="ids_rectangle" val="Rectangle"/>
	<str id="ids_circle" val="Circle"/>
	<str id="ids_circleCR" val="Circle (Center+Radius)"/>
	<str id="ids_circle2pt" val="Circle (2 Points)"/>
	<str id="ids_circle3pt" val="Circle (3 Points)"/>
	<str id="ids_twocircles" val="Two Circles"/>
	<str id="ids_twocirclesCR" val="Two Circles (Center+Radius)"/>
	<str id="ids_twocircles3pt" val="Two Circles (3 Points)"/>
	<str id="ids_ellipse" val="Ellipse"/>
	<str id="ids_line" val="Line"/>
	<str id="ids_arbline" val="Arbline"/>
	<str id="ids_vline" val="Vertical Line"/>
	<str id="ids_hline" val="Horizontal Line"/>
	<str id="ids_annulus" val="Annulus"/>
	<str id="ids_arc" val="Arc"/>
	<str id="ids_parallel" val="Parallel"/>
	<str id="ids_parallel1" val="Parallel (4 Points)"/>
	<str id="ids_parallel2" val="Parallel (8 Points)"/>
	<str id="ids_curve" val="Curve"/>
	<str id="ids_polygon" val="Polygon"/>
	<str id="ids_text" val="Text"/>
	<str id="ids_scalebar" val="Scale Bar"/>
	<str id="ids_delete" val="Delete"/>
	<str id="ids_calibration" val="Calibration"/>
	<str id="ids_send" val="Send"/>
	<str id="ids_name" val="Name"/>
	<str id="ids_password" val="Password"/>
	<str id="ids_ok" val="OK"/>
	<str id="ids_cancel" val="Cancel"/>
	<str id="ids_apply" val="Apply"/>
	<str id="ids_login" val="Login"/>
	<str id="ids_teaching" val="Teaching"/>
	<str id="ids_same_screen" val="Same Screen Teaching"/>
	<str id="ids_electronic_whiteboard" val="Electronic Whiteboard"/>
	<str id="ids_black_screen_silence" val="Black Screen Silence"/>
	<str id="ids_fit_to_width" val="Fit to Width"/>
	<str id="ids_fit_to_height" val="Fit to Height"/>
	<str id="ids_fit_to_window" val="Fit to Window"/>
	<str id="ids_zoom" val="Zoom"/>
	<str id="ids_unit" val="Unit"/>
	<str id="ids_pixel" val="Pixel"/>
	<str id="ids_nm" val="Nanometer"/>
	<str id="ids_um" val="Micrometer"/>
	<str id="ids_abbrum" val="µm"/>
	<str id="ids_mm" val="Millimeter"/>
	<str id="ids_cm" val="Centimeter"/>
	<str id="ids_m" val="Meter"/>
	<str id="ids_in" val="Inch"/>
	<str id="ids_beilv" val="Magnification"/>
	<str id="ids_videocalibratemax1" val="Please calibrate in maximized size.\r\nThe Current size: %1 x %2. The maximum size: %3 x %4.\r\nPlease use the camera sidebar to set the resolution."/>
	<str id="ids_videozoomfloat" val="The current zoom level is %1%. To get the exact number, please set the zoom level to 100%. Still to continue?"/>
	<str id="ids_calibrate" val="Calibrate"/>
	<str id="ids_calibration_ac" val="Actual Length"/>
	<str id="ids_calibration_pi" val="Pixel"/>
	<str id="ids_calibration_pm" val="Pixel/Meter"/>
	<str id="ids_calibratedlgmagnificationexists" val="The magnification &quot;%1&quot; already exists."/>
	<str id="ids_snap" val="Snap"/>
	<str id="ids_record" val="Record"/>
	<str id="ids_stop" val="Stop"/>
</i18n>
