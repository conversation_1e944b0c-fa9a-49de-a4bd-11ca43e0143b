﻿// chatwidget.cpp
#include "chatwidget.h"
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QMessageBox>
#include <QDateTime>
#include <QNetworkInterface>
#include <QProcess>
#include <QDebug>
#include <QApplication>
#include <QStyle>
#include <QScrollBar>

//带有不同参数的两个构造函数
ChatWidget::ChatWidget(QWidget* parent)
    : QWidget(parent), m_dbHandler(nullptr), m_nOnlineCount(0), m_currentMode(ChatMode_Chat)
{
    qDebug() << "ChatWidget: 开始初始化（无数据库）...";

    // Get user info
    m_sUserName = getUserName();
    m_sLocalIp = getIP();

    // 初始化时将自己添加到在线用户列表
    m_onlineUsers.append(m_sLocalIp);
    m_userNameMap[m_sLocalIp] = m_sUserName;
    m_nOnlineCount = 1;

    // 初始化文件传输组件
    m_pFileReceiver = new FileReceiver(this);
    m_pFileSender = new FileSender(this);

    // 初始化传输设置
    m_transferPort = 6688;           // 默认传输端口
    m_requestTimeoutSec = 30;        // 默认30秒超时
    m_maxFileSize = 1024LL * 1024 * 1024; // 默认1GB文件大小限制

    // 初始化请求超时定时器
    m_pRequestTimeoutTimer = new QTimer(this);
    m_pRequestTimeoutTimer->setSingleShot(true);
    m_pRequestTimeoutTimer->setInterval(m_requestTimeoutSec * 1000);
    connect(m_pRequestTimeoutTimer, &QTimer::timeout, this, &ChatWidget::onRequestTimeout);

    // 注意：文件传输的信号连接在实际传输开始时进行，避免重复连接

    qDebug() << "ChatWidget: 用户信息 - 用户名:" << m_sUserName << "IP:" << m_sLocalIp;

    // Setup UI
    setupUI();

    // 初始化UI后立即显示正确的在线人数
    updateOnlineCount(m_nOnlineCount);

    // Initialize UDP
    m_pUdpSocket = new QUdpSocket(this);
    m_uPort = 12315;

    // Bind to port with reuse options
    if(m_pUdpSocket->bind(m_uPort, QUdpSocket::ShareAddress | QUdpSocket::ReuseAddressHint)) {
        connect(m_pUdpSocket, &QUdpSocket::readyRead,
                this, &ChatWidget::processPendingDatagrams);
        updateStatus(tr("已连接到端口 %1").arg(m_uPort));

        // 首先添加自己到用户列表
        if (!m_onlineUsers.contains(m_sLocalIp)) {
            m_onlineUsers.append(m_sLocalIp);
            m_userNameMap[m_sLocalIp] = m_sUserName;
            m_nOnlineCount = m_onlineUsers.size();
            updateOnlineCount(m_nOnlineCount);
            qDebug() << "ChatWidget: 添加自己到用户列表:" << m_sUserName << "(" << m_sLocalIp << ")";
        }

        // Broadcast join notification
        sendMessage(NewParticipant);

        // Request current user list from existing users
        QTimer::singleShot(500, this, [this]() {
            sendMessage(UserListRequest);
            qDebug() << "ChatWidget: 请求当前在线用户列表";
        });

        qDebug() << "ChatWidget: UDP 绑定成功，端口:" << m_uPort;
    } else {
        updateStatus(tr("绑定失败: %1").arg(m_pUdpSocket->errorString()));
        qDebug() << "ChatWidget: UDP 绑定失败:" << m_pUdpSocket->errorString();
    }

    qDebug() << "ChatWidget: 初始化完成（无数据库）";
}

ChatWidget::ChatWidget(TpdatabaseHandler* dbHandler, QWidget* parent)
    : QWidget(parent), m_dbHandler(dbHandler), m_nOnlineCount(0), m_currentMode(ChatMode_Chat)
{
    qDebug() << "ChatWidget: 开始初始化（带数据库）...";

    // Get user info
    m_sUserName = getUserName();
    m_sLocalIp = getIP();

    // 初始化时将自己添加到在线用户列表
    m_onlineUsers.append(m_sLocalIp);
    m_userNameMap[m_sLocalIp] = m_sUserName;
    m_nOnlineCount = 1;

    // 初始化文件传输组件
    m_pFileReceiver = new FileReceiver(this);
    m_pFileSender = new FileSender(this);

    // 初始化传输设置
    m_transferPort = 6688;           // 默认传输端口
    m_requestTimeoutSec = 30;        // 默认30秒超时
    m_maxFileSize = 1024LL * 1024 * 1024; // 默认1GB文件大小限制

    // 初始化请求超时定时器
    m_pRequestTimeoutTimer = new QTimer(this);
    m_pRequestTimeoutTimer->setSingleShot(true);
    m_pRequestTimeoutTimer->setInterval(m_requestTimeoutSec * 1000);
    connect(m_pRequestTimeoutTimer, &QTimer::timeout, this, &ChatWidget::onRequestTimeout);

    // 注意：文件传输的信号连接在实际传输开始时进行，避免重复连接

    qDebug() << "ChatWidget: 用户信息 - 用户名:" << m_sUserName << "IP:" << m_sLocalIp;

    // Setup UI
    setupUI();

    // 初始化UI后立即显示正确的在线人数
    updateOnlineCount(m_nOnlineCount);

    qDebug() << "ChatWidget: 初始化 UDP...";
    // Initialize UDP
    m_pUdpSocket = new QUdpSocket(this);
    m_uPort = 12315;

    // Bind to port with reuse options
    if(m_pUdpSocket->bind(m_uPort, QUdpSocket::ShareAddress | QUdpSocket::ReuseAddressHint)) {
        connect(m_pUdpSocket, &QUdpSocket::readyRead,
                this, &ChatWidget::processPendingDatagrams);
        updateStatus(tr("已连接到端口 %1").arg(m_uPort));

        // Broadcast join notification
        sendMessage(NewParticipant);

        // Request current user list from existing users
        QTimer::singleShot(500, this, [this]() {
            sendMessage(UserListRequest);
            qDebug() << "ChatWidget: 请求当前在线用户列表";
        });

        qDebug() << "ChatWidget: UDP 绑定成功，端口:" << m_uPort;
    } else {
        updateStatus(tr("绑定失败: %1").arg(m_pUdpSocket->errorString()));
        qDebug() << "ChatWidget: UDP 绑定失败:" << m_pUdpSocket->errorString();
    }

    qDebug() << "ChatWidget: 初始化完成（带数据库）";
}


ChatWidget::~ChatWidget()
{
    // Broadcast leave notification
    sendMessage(Participantleft);
    if (m_pUdpSocket) {
        m_pUdpSocket->close();
        delete m_pUdpSocket;
    }
}

void ChatWidget::Notify(int notify)
{
    Q_UNUSED(notify);
    // Optional: Implement any needed notifications
}

void ChatWidget::setupUI()
{
    qDebug() << "ChatWidget: setupUI 开始...";

    // 设置窗口属性
    setMinimumSize(400, 500);

    // 创建主布局
    QVBoxLayout* mainLayout = new QVBoxLayout(this);
    mainLayout->setSpacing(8);
    mainLayout->setContentsMargins(12, 12, 12, 12);

    // 设置标题栏（包含模式切换按钮）
    setupTitleBar();
    mainLayout->addWidget(m_pTitleFrame);

    // 创建堆叠窗口用于模式切换
    m_pStackedWidget = new QStackedWidget();

    // 设置聊天区域
    setupChatArea();
    m_pStackedWidget->addWidget(m_pChatWidget);

    // 设置文件传输区域
    setupFileTransferArea();
    m_pStackedWidget->addWidget(m_pFileTransferWidget);

    // 默认显示聊天模式
    m_pStackedWidget->setCurrentIndex(ChatMode_Chat);

    mainLayout->addWidget(m_pStackedWidget, 1);  // 给主内容区域更多空间

    // 设置状态栏
    setupStatusBar();
    mainLayout->addWidget(m_pStatusFrame);

    setLayout(mainLayout);

    qDebug() << "ChatWidget: setupUI 完成";
}

void ChatWidget::setupTitleBar()
{
    qDebug() << "ChatWidget: setupTitleBar 开始...";

    m_pTitleFrame = new QFrame();
    m_pTitleFrame->setFrameStyle(QFrame::Box);
    m_pTitleFrame->setStyleSheet(
        "QFrame { "
        "   background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
        "       stop:0 #28A745, stop:1 #1E7E34); "
        "   border: 1px solid #155724; "
        "   border-radius: 6px; "
        "   padding: 6px; "
        "}"
    );

    QHBoxLayout* titleLayout = new QHBoxLayout(m_pTitleFrame);
    titleLayout->setContentsMargins(12, 8, 12, 8);
    titleLayout->setSpacing(8);

    // 创建模式切换按钮
    m_pChatModeBtn = new QPushButton("💬 聊天");
    m_pFileTransferModeBtn = new QPushButton("📁 文件传输");

    // 设置按钮样式
    QString activeButtonStyle =
        "QPushButton { "
        "   background: rgba(255, 255, 255, 0.3); "
        "   color: white; "
        "   border: 2px solid rgba(255, 255, 255, 0.5); "
        "   border-radius: 4px; "
        "   padding: 4px 12px; "
        "   font-size: 12px; "
        "   font-weight: bold; "
        "}"
        "QPushButton:hover { "
        "   background: rgba(255, 255, 255, 0.4); "
        "}";

    QString inactiveButtonStyle =
        "QPushButton { "
        "   background: transparent; "
        "   color: rgba(255, 255, 255, 0.8); "
        "   border: 2px solid transparent; "
        "   border-radius: 4px; "
        "   padding: 4px 12px; "
        "   font-size: 12px; "
        "}"
        "QPushButton:hover { "
        "   background: rgba(255, 255, 255, 0.2); "
        "   color: white; "
        "}";

    // 设置初始状态（聊天模式激活）
    m_pChatModeBtn->setStyleSheet(activeButtonStyle);
    m_pFileTransferModeBtn->setStyleSheet(inactiveButtonStyle);

    // 连接信号
    connect(m_pChatModeBtn, &QPushButton::clicked, this, &ChatWidget::onChatModeClicked);
    connect(m_pFileTransferModeBtn, &QPushButton::clicked, this, &ChatWidget::onFileTransferModeClicked);

    titleLayout->addWidget(m_pChatModeBtn);
    titleLayout->addWidget(m_pFileTransferModeBtn);
    titleLayout->addStretch();

    qDebug() << "ChatWidget: setupTitleBar 完成";
}

void ChatWidget::setupMessageArea()
{
    qDebug() << "ChatWidget: setupMessageArea 开始...";

    m_pMessageFrame = new QFrame();
    m_pMessageFrame->setFrameStyle(QFrame::Box);
    m_pMessageFrame->setStyleSheet(
        "QFrame { "
        "   background-color: #F8F9FA; "
        "   border: 1px solid #DEE2E6; "
        "   border-radius: 6px; "
        "   padding: 8px; "
        "}"
    );

    QVBoxLayout* messageLayout = new QVBoxLayout(m_pMessageFrame);
    messageLayout->setContentsMargins(8, 8, 8, 8);
    messageLayout->setSpacing(6);

    QLabel* messageTitle = new QLabel("📝 消息记录");
    messageTitle->setStyleSheet(
        "QLabel { "
        "   color: #495057; "
        "   font-size: 13px; "
        "   font-weight: bold; "
        "   margin-bottom: 4px; "
        "   background: transparent; "
        "}"
    );
    messageLayout->addWidget(messageTitle);

    m_pMsgTEdit = new QTextEdit();
    m_pMsgTEdit->setReadOnly(true);
    m_pMsgTEdit->setMinimumHeight(200);
    m_pMsgTEdit->setStyleSheet(
        "QTextEdit { "
        "   background-color: white; "
        "   border: 1px solid #CED4DA; "
        "   border-radius: 4px; "
        "   padding: 8px; "
        "   font-size: 13px; "
        "   line-height: 1.4; "
        "}"
        "QScrollBar:vertical { "
        "   background: #F8F9FA; "
        "   width: 12px; "
        "   border-radius: 6px; "
        "}"
        "QScrollBar::handle:vertical { "
        "   background: #CED4DA; "
        "   border-radius: 6px; "
        "   min-height: 20px; "
        "}"
        "QScrollBar::handle:vertical:hover { "
        "   background: #ADB5BD; "
        "}"
    );

    messageLayout->addWidget(m_pMsgTEdit);

    qDebug() << "ChatWidget: setupMessageArea 完成";
}

void ChatWidget::setupInputArea()
{
    qDebug() << "ChatWidget: setupInputArea 开始...";

    m_pInputFrame = new QFrame();
    m_pInputFrame->setFrameStyle(QFrame::Box);
    m_pInputFrame->setStyleSheet(
        "QFrame { "
        "   background-color: #F8F9FA; "
        "   border: 1px solid #DEE2E6; "
        "   border-radius: 6px; "
        "   padding: 8px; "
        "}"
    );

    QVBoxLayout* inputMainLayout = new QVBoxLayout(m_pInputFrame);
    inputMainLayout->setContentsMargins(8, 8, 8, 8);
    inputMainLayout->setSpacing(6);

    QLabel* inputTitle = new QLabel("✏️ 输入消息");
    inputTitle->setStyleSheet(
        "QLabel { "
        "   color: #495057; "
        "   font-size: 13px; "
        "   font-weight: bold; "
        "   margin-bottom: 4px; "
        "   background: transparent; "
        "}"
    );
    inputMainLayout->addWidget(inputTitle);

    QHBoxLayout* inputLayout = new QHBoxLayout();
    inputLayout->setSpacing(8);

    m_pInputEdit = new QLineEdit();
    m_pInputEdit->setPlaceholderText("输入消息内容，按回车发送...");
    m_pInputEdit->setStyleSheet(
        "QLineEdit { "
        "   background-color: white; "
        "   border: 2px solid #CED4DA; "
        "   border-radius: 6px; "
        "   padding: 8px 12px; "
        "   font-size: 13px; "
        "   min-height: 16px; "
        "}"
        "QLineEdit:focus { "
        "   border-color: #28A745; "
        "   outline: none; "
        "}"
        "QLineEdit:hover { "
        "   border-color: #ADB5BD; "
        "}"
    );

    // 安装事件过滤器以处理回车键
    m_pInputEdit->installEventFilter(this);

    m_pSendBtn = new QPushButton("发送");
    m_pSendBtn->setStyleSheet(
        "QPushButton { "
        "   background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
        "       stop:0 #28A745, stop:1 #1E7E34); "
        "   color: white; "
        "   border: none; "
        "   border-radius: 6px; "
        "   padding: 8px 16px; "
        "   font-size: 13px; "
        "   font-weight: bold; "
        "   min-width: 60px; "
        "}"
        "QPushButton:hover { "
        "   background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
        "       stop:0 #218838, stop:1 #1C7430); "
        "}"
        "QPushButton:pressed { "
        "   background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
        "       stop:0 #1E7E34, stop:1 #155724); "
        "}"
        "QPushButton:disabled { "
        "   background: #6C757D; "
        "   color: #ADB5BD; "
        "}"
    );

    connect(m_pSendBtn, &QPushButton::clicked, this, &ChatWidget::onSendButtonClicked);

    inputLayout->addWidget(m_pInputEdit, 1);
    inputLayout->addWidget(m_pSendBtn);

    inputMainLayout->addLayout(inputLayout);

    qDebug() << "ChatWidget: setupInputArea 完成";
}

void ChatWidget::setupChatArea()
{
    qDebug() << "ChatWidget: setupChatArea 开始...";

    m_pChatWidget = new QWidget();
    QVBoxLayout* chatLayout = new QVBoxLayout(m_pChatWidget);
    chatLayout->setSpacing(8);
    chatLayout->setContentsMargins(0, 0, 0, 0);

    // 设置消息显示区域
    setupMessageArea();
    chatLayout->addWidget(m_pMessageFrame, 1);

    // 设置输入区域
    setupInputArea();
    chatLayout->addWidget(m_pInputFrame);

    qDebug() << "ChatWidget: setupChatArea 完成";
}

void ChatWidget::setupFileTransferArea()
{
    qDebug() << "ChatWidget: setupFileTransferArea 开始...";

    m_pFileTransferWidget = new QWidget();
    QVBoxLayout* fileTransferLayout = new QVBoxLayout(m_pFileTransferWidget);
    fileTransferLayout->setSpacing(8);
    fileTransferLayout->setContentsMargins(0, 0, 0, 0);

    // 设置用户列表区域
    m_pUserListFrame = new QFrame();
    m_pUserListFrame->setFrameStyle(QFrame::Box);
    m_pUserListFrame->setStyleSheet(
        "QFrame { "
        "   background-color: #F8F9FA; "
        "   border: 1px solid #DEE2E6; "
        "   border-radius: 6px; "
        "   padding: 8px; "
        "}"
    );

    QVBoxLayout* userListLayout = new QVBoxLayout(m_pUserListFrame);
    userListLayout->setContentsMargins(8, 8, 8, 8);
    userListLayout->setSpacing(6);

    QLabel* userListTitle = new QLabel("👥 在线用户列表");
    userListTitle->setStyleSheet(
        "QLabel { "
        "   color: #495057; "
        "   font-size: 13px; "
        "   font-weight: bold; "
        "   margin-bottom: 4px; "
        "   background: transparent; "
        "}"
    );
    userListLayout->addWidget(userListTitle);

    // 创建用户列表表格
    m_pUserListTable = new QTableWidget();
    m_pUserListTable->setColumnCount(2);
    m_pUserListTable->setHorizontalHeaderLabels(QStringList() << "用户名" << "IP地址");
    m_pUserListTable->setMinimumHeight(200);

    // 设置表格样式（参考MonitorWidget）
    QHeaderView* header = m_pUserListTable->horizontalHeader();
    header->setSectionResizeMode(0, QHeaderView::Stretch);
    header->setSectionResizeMode(1, QHeaderView::Stretch);
    header->setDefaultAlignment(Qt::AlignCenter);

    QHeaderView* vHeader = m_pUserListTable->verticalHeader();
    vHeader->setVisible(false);

    m_pUserListTable->verticalHeader()->setDefaultSectionSize(32);
    m_pUserListTable->setHorizontalScrollBarPolicy(Qt::ScrollBarAlwaysOff);
    m_pUserListTable->setFrameStyle(QFrame::NoFrame);
    m_pUserListTable->setFocusPolicy(Qt::NoFocus);
    m_pUserListTable->setSelectionBehavior(QAbstractItemView::SelectRows);

    m_pUserListTable->setStyleSheet(
        "QTableWidget {"
        "  background: #fff;"
        "  border: 1px solid #e0e0e0;"
        "  font-size: 14px;"
        "  selection-background-color: #e3f2fd;"
        "  gridline-color: #f0f0f0;"
        "}"
        "QHeaderView::section {"
        "  background: #f8f9fa;"
        "  color: #495057;"
        "  font-weight: bold;"
        "  font-size: 14px;"
        "  border: none;"
        "  border-bottom: 2px solid #e0e0e0;"
        "  height: 32px;"
        "  padding: 0;"
        "  margin: 0;"
        "}"
        "QTableWidget::item {"
        "  font-size: 14px;"
        "  height: 32px;"
        "  padding: 0;"
        "  margin: 0;"
        "}"
        "QTableWidget::item:selected {"
        "  background: #e3f2fd;"
        "  color: #1976d2;"
        "}"
    );

    // 连接双击信号
    connect(m_pUserListTable, &QTableWidget::cellDoubleClicked,
            this, &ChatWidget::onUserListDoubleClicked);

    userListLayout->addWidget(m_pUserListTable, 1);

    // 设置传输进度区域
    m_pTransferFrame = new QFrame();
    m_pTransferFrame->setFrameStyle(QFrame::Box);
    m_pTransferFrame->setStyleSheet(
        "QFrame { "
        "   background-color: #F8F9FA; "
        "   border: 1px solid #DEE2E6; "
        "   border-radius: 6px; "
        "   padding: 8px; "
        "}"
    );

    QVBoxLayout* transferLayout = new QVBoxLayout(m_pTransferFrame);
    transferLayout->setContentsMargins(8, 8, 8, 8);
    transferLayout->setSpacing(6);

    QLabel* transferTitle = new QLabel("📊 传输进度");
    transferTitle->setStyleSheet(
        "QLabel { "
        "   color: #495057; "
        "   font-size: 13px; "
        "   font-weight: bold; "
        "   margin-bottom: 4px; "
        "   background: transparent; "
        "}"
    );
    transferLayout->addWidget(transferTitle);

    // 传输状态标签
    m_pTransferStatusLabel = new QLabel("暂无传输任务");
    m_pTransferStatusLabel->setStyleSheet(
        "QLabel { "
        "   color: #6C757D; "
        "   font-size: 12px; "
        "   background: transparent; "
        "}"
    );
    transferLayout->addWidget(m_pTransferStatusLabel);

    // 进度条
    m_pProgressBar = new QProgressBar();
    m_pProgressBar->setVisible(false);
    m_pProgressBar->setStyleSheet(
        "QProgressBar { "
        "   border: 1px solid #CED4DA; "
        "   border-radius: 4px; "
        "   text-align: center; "
        "   font-size: 11px; "
        "   background-color: white; "
        "}"
        "QProgressBar::chunk { "
        "   background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
        "       stop:0 #28A745, stop:1 #1E7E34); "
        "   border-radius: 3px; "
        "}"
    );
    transferLayout->addWidget(m_pProgressBar);

    // 取消传输按钮
    m_pCancelTransferBtn = new QPushButton("取消传输");
    m_pCancelTransferBtn->setVisible(false);
    m_pCancelTransferBtn->setStyleSheet(
        "QPushButton { "
        "   background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
        "       stop:0 #DC3545, stop:1 #C82333); "
        "   color: white; "
        "   border: none; "
        "   border-radius: 4px; "
        "   padding: 6px 12px; "
        "   font-size: 12px; "
        "   font-weight: bold; "
        "}"
        "QPushButton:hover { "
        "   background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
        "       stop:0 #C82333, stop:1 #BD2130); "
        "}"
        "QPushButton:pressed { "
        "   background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
        "       stop:0 #BD2130, stop:1 #B21F2D); "
        "}"
    );

    connect(m_pCancelTransferBtn, &QPushButton::clicked,
            this, &ChatWidget::onCancelTransferClicked);

    transferLayout->addWidget(m_pCancelTransferBtn);

    // 设置传输历史区域
    setupHistoryArea();

    // 添加到主布局 - 用户列表和历史记录各占一半，传输进度保持固定
    fileTransferLayout->addWidget(m_pUserListFrame, 2);  // 用户列表占一半空间
    fileTransferLayout->addWidget(m_pTransferFrame, 1);   // 传输进度区域（固定）
    fileTransferLayout->addWidget(m_pHistoryFrame, 2);    // 传输历史区域占一半空间

    qDebug() << "ChatWidget: setupFileTransferArea 完成";
}

void ChatWidget::setupHistoryArea()
{
    qDebug() << "ChatWidget: setupHistoryArea 开始...";

    m_pHistoryFrame = new QFrame();
    m_pHistoryFrame->setFrameStyle(QFrame::Box);
    m_pHistoryFrame->setMaximumHeight(300);  // 允许历史记录区域有更多空间
    m_pHistoryFrame->setStyleSheet(
        "QFrame { "
        "   background-color: #F8F9FA; "
        "   border: 1px solid #DEE2E6; "
        "   border-radius: 6px; "
        "   padding: 6px; "
        "}"
    );

    QVBoxLayout* historyLayout = new QVBoxLayout(m_pHistoryFrame);
    historyLayout->setContentsMargins(8, 6, 8, 6);  // 减少上下边距
    historyLayout->setSpacing(4);  // 减少间距

    // 标题和清空按钮
    QHBoxLayout* historyTitleLayout = new QHBoxLayout();

    QLabel* historyTitle = new QLabel("📋 传输历史");
    historyTitle->setStyleSheet(
        "QLabel { "
        "   color: #495057; "
        "   font-size: 12px; "
        "   font-weight: bold; "
        "   margin-bottom: 2px; "
        "   background: transparent; "
        "   max-height: 20px; "
        "}"
    );
    historyTitle->setMaximumHeight(20);  // 限制标题高度

    m_pClearHistoryBtn = new QPushButton("清空");
    m_pClearHistoryBtn->setStyleSheet(
        "QPushButton { "
        "   background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
        "       stop:0 #6C757D, stop:1 #5A6268); "
        "   color: white; "
        "   border: none; "
        "   border-radius: 3px; "
        "   padding: 2px 6px; "
        "   font-size: 10px; "
        "   font-weight: bold; "
        "   max-width: 40px; "
        "   max-height: 18px; "
        "}"
        "QPushButton:hover { "
        "   background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
        "       stop:0 #5A6268, stop:1 #495057); "
        "}"
    );

    connect(m_pClearHistoryBtn, &QPushButton::clicked,
            this, &ChatWidget::onClearHistoryClicked);

    // 设置按钮
    m_pSettingsBtn = new QPushButton("⚙️");
    m_pSettingsBtn->setStyleSheet(
        "QPushButton { "
        "   background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
        "       stop:0 #17A2B8, stop:1 #138496); "
        "   color: white; "
        "   border: none; "
        "   border-radius: 3px; "
        "   padding: 2px 6px; "
        "   font-size: 10px; "
        "   font-weight: bold; "
        "   max-width: 25px; "
        "   max-height: 18px; "
        "}"
        "QPushButton:hover { "
        "   background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
        "       stop:0 #138496, stop:1 #117A8B); "
        "}"
    );

    connect(m_pSettingsBtn, &QPushButton::clicked,
            this, &ChatWidget::onSettingsClicked);

    historyTitleLayout->addWidget(historyTitle);
    historyTitleLayout->addStretch();
    historyTitleLayout->addWidget(m_pSettingsBtn);
    historyTitleLayout->addWidget(m_pClearHistoryBtn);

    historyLayout->addLayout(historyTitleLayout);

    // 创建历史记录表格
    m_pHistoryTable = new QTableWidget();
    m_pHistoryTable->setColumnCount(6);
    m_pHistoryTable->setHorizontalHeaderLabels(
        QStringList() << "文件名" << "对方" << "大小" << "状态" << "速度" << "时间");
    m_pHistoryTable->setMinimumHeight(120);
    m_pHistoryTable->setMaximumHeight(250);

    // 设置表格样式 - 保持紧凑布局，支持水平滚动
    QHeaderView* historyHeader = m_pHistoryTable->horizontalHeader();
    historyHeader->setSectionResizeMode(0, QHeaderView::Fixed);        // 文件名
    historyHeader->setSectionResizeMode(1, QHeaderView::Fixed);        // 对方
    historyHeader->setSectionResizeMode(2, QHeaderView::Fixed);        // 大小
    historyHeader->setSectionResizeMode(3, QHeaderView::Fixed);        // 状态
    historyHeader->setSectionResizeMode(4, QHeaderView::Fixed);        // 速度
    historyHeader->setSectionResizeMode(5, QHeaderView::Fixed);        // 时间
    historyHeader->setDefaultAlignment(Qt::AlignCenter);

    // 设置适中的列宽，支持水平滚动查看完整内容
    historyHeader->resizeSection(0, 120); // 文件名列宽
    historyHeader->resizeSection(1, 80);  // 对方列宽
    historyHeader->resizeSection(2, 70);  // 大小列宽
    historyHeader->resizeSection(3, 60);  // 状态列宽
    historyHeader->resizeSection(4, 80);  // 速度列宽
    historyHeader->resizeSection(5, 90);  // 时间列宽

    QHeaderView* historyVHeader = m_pHistoryTable->verticalHeader();
    historyVHeader->setVisible(false);

    m_pHistoryTable->verticalHeader()->setDefaultSectionSize(26);  // 与表头高度保持一致
    m_pHistoryTable->setHorizontalScrollBarPolicy(Qt::ScrollBarAsNeeded);
    m_pHistoryTable->setVerticalScrollBarPolicy(Qt::ScrollBarAsNeeded);
    m_pHistoryTable->setFrameStyle(QFrame::NoFrame);
    m_pHistoryTable->setFocusPolicy(Qt::NoFocus);
    m_pHistoryTable->setSelectionMode(QAbstractItemView::NoSelection);
    m_pHistoryTable->setAlternatingRowColors(true);

    // 不设置最小宽度，让表格可以在容器内滚动

    m_pHistoryTable->setStyleSheet(
        "QTableWidget {"
        "  background: #fff;"
        "  border: 1px solid #e0e0e0;"
        "  font-size: 12px;"
        "  gridline-color: #f0f0f0;"
        "  alternate-background-color: #f8f9fa;"
        "}"
        "QHeaderView::section {"
        "  background: #f8f9fa;"
        "  color: #495057;"
        "  font-weight: bold;"
        "  font-size: 11px;"
        "  border: none;"
        "  border-bottom: 1px solid #e0e0e0;"
        "  height: 26px;"
        "  padding: 4px 6px;"
        "  text-align: center;"
        "}"
        "QTableWidget::item {"
        "  font-size: 11px;"
        "  height: 26px;"
        "  padding: 4px 6px;"
        "  border: none;"
        "}"
    );

    historyLayout->addWidget(m_pHistoryTable);

    qDebug() << "ChatWidget: setupHistoryArea 完成";
}

void ChatWidget::setupStatusBar()
{
    qDebug() << "ChatWidget: setupStatusBar 开始...";

    m_pStatusFrame = new QFrame();
    m_pStatusFrame->setFrameStyle(QFrame::Box);
    m_pStatusFrame->setStyleSheet(
        "QFrame { "
        "   background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
        "       stop:0 #F8F9FA, stop:1 #E9ECEF); "
        "   border: 1px solid #DEE2E6; "
        "   border-radius: 6px; "
        "   padding: 6px; "
        "}"
    );

    QHBoxLayout* statusLayout = new QHBoxLayout(m_pStatusFrame);
    statusLayout->setContentsMargins(12, 6, 12, 6);

    m_pStatusIcon = new QLabel("🟢");
    m_pStatusIcon->setStyleSheet(
        "QLabel { "
        "   font-size: 11px; "
        "   background: transparent; "
        "}"
    );

    m_pStatusLabel = new QLabel("系统运行正常");
    m_pStatusLabel->setStyleSheet(
        "QLabel { "
        "   color: #495057; "
        "   font-size: 11px; "
        "   background: transparent; "
        "}"
    );

    m_pOnlineCountLabel = new QLabel("在线用户: 0");
    m_pOnlineCountLabel->setStyleSheet(
        "QLabel { "
        "   color: #6C757D; "
        "   font-size: 11px; "
        "   background: transparent; "
        "}"
    );

    statusLayout->addWidget(m_pStatusIcon);
    statusLayout->addWidget(m_pStatusLabel);
    statusLayout->addStretch();
    statusLayout->addWidget(m_pOnlineCountLabel);

    qDebug() << "ChatWidget: setupStatusBar 完成";
}

bool ChatWidget::eventFilter(QObject *obj, QEvent *event)
{
    if (obj == m_pInputEdit && event->type() == QEvent::KeyPress) {
        QKeyEvent *keyEvent = static_cast<QKeyEvent*>(event);

        // 检查是否按下回车键
        if (keyEvent->key() == Qt::Key_Return || keyEvent->key() == Qt::Key_Enter) {
            // 检查是否同时按下Shift键（用于换行，虽然QLineEdit不支持多行）
            if (keyEvent->modifiers() & Qt::ShiftModifier) {
                // Shift+回车：在QLineEdit中不做特殊处理，让默认行为处理
                return QWidget::eventFilter(obj, event);
            } else {
                // 单独回车：发送消息
                sendTextMessage();
                return true; // 事件已处理，不再传递
            }
        }
    }

    // 对于其他事件，调用基类的事件过滤器
    return QWidget::eventFilter(obj, event);
}

void ChatWidget::switchToMode(ChatMode mode)
{
    if (m_currentMode == mode) return;

    m_currentMode = mode;
    m_pStackedWidget->setCurrentIndex(mode);

    // 更新按钮样式
    QString activeButtonStyle =
        "QPushButton { "
        "   background: rgba(255, 255, 255, 0.3); "
        "   color: white; "
        "   border: 2px solid rgba(255, 255, 255, 0.5); "
        "   border-radius: 4px; "
        "   padding: 4px 12px; "
        "   font-size: 12px; "
        "   font-weight: bold; "
        "}"
        "QPushButton:hover { "
        "   background: rgba(255, 255, 255, 0.4); "
        "}";

    QString inactiveButtonStyle =
        "QPushButton { "
        "   background: transparent; "
        "   color: rgba(255, 255, 255, 0.8); "
        "   border: 2px solid transparent; "
        "   border-radius: 4px; "
        "   padding: 4px 12px; "
        "   font-size: 12px; "
        "}"
        "QPushButton:hover { "
        "   background: rgba(255, 255, 255, 0.2); "
        "   color: white; "
        "}";

    if (mode == ChatMode_Chat) {
        m_pChatModeBtn->setStyleSheet(activeButtonStyle);
        m_pFileTransferModeBtn->setStyleSheet(inactiveButtonStyle);
    } else {
        m_pChatModeBtn->setStyleSheet(inactiveButtonStyle);
        m_pFileTransferModeBtn->setStyleSheet(activeButtonStyle);

        // 切换到文件传输模式时更新用户列表
        updateUserList();
    }

    qDebug() << "ChatWidget: 切换到模式" << (mode == ChatMode_Chat ? "聊天" : "文件传输");
}

void ChatWidget::onChatModeClicked()
{
    switchToMode(ChatMode_Chat);
}

void ChatWidget::onFileTransferModeClicked()
{
    switchToMode(ChatMode_FileTransfer);
}

void ChatWidget::updateUserList()
{
    if (!m_pUserListTable) return;

    // 清空现有行
    m_pUserListTable->setRowCount(0);

    // 添加所有在线用户（包括自己）
    int row = 0;
    for (const QString& ip : m_onlineUsers) {
        m_pUserListTable->insertRow(row);

        QString userName = m_userNameMap.value(ip, "未知用户");

        QTableWidgetItem* userItem = new QTableWidgetItem(userName);
        QTableWidgetItem* ipItem = new QTableWidgetItem(ip);

        userItem->setTextAlignment(Qt::AlignCenter);
        ipItem->setTextAlignment(Qt::AlignCenter);

        // 如果是自己，设置特殊样式
        if (ip == m_sLocalIp) {
            QFont boldFont = userItem->font();
            boldFont.setBold(true);
            userItem->setFont(boldFont);
            ipItem->setFont(boldFont);

            // 设置背景色突出显示
            userItem->setBackground(QColor(230, 255, 230)); // 淡绿色
            ipItem->setBackground(QColor(230, 255, 230));

            userItem->setToolTip("这是您自己（无法向自己发送文件）");
            ipItem->setToolTip("这是您自己（无法向自己发送文件）");
        }

        m_pUserListTable->setItem(row, 0, userItem);
        m_pUserListTable->setItem(row, 1, ipItem);

        row++;
    }

    qDebug() << "ChatWidget: 更新用户列表，共" << row << "个用户（包括自己）";
}

void ChatWidget::onUserListDoubleClicked(int row, int column)
{
    Q_UNUSED(column);

    if (row < 0 || row >= m_pUserListTable->rowCount()) return;

    QTableWidgetItem* ipItem = m_pUserListTable->item(row, 1);
    if (!ipItem) return;

    QString targetIP = ipItem->text();
    QString userName = m_pUserListTable->item(row, 0)->text();

    qDebug() << "ChatWidget: 双击用户" << userName << "(" << targetIP << ")";

    // 检查是否是自己
    if (targetIP == m_sLocalIp) {
        QMessageBox::information(this, "提示", "无法向自己发送文件！");
        return;
    }

    // 弹出文件选择对话框
    QString filePath = QFileDialog::getOpenFileName(
        this,
        tr("选择要发送的文件"),
        QString(),
        tr("所有文件 (*.*)")
    );

    if (!filePath.isEmpty()) {
        startFileTransfer(filePath, targetIP);
    }
}

void ChatWidget::onCancelTransferClicked()
{
    // 取消当前传输
    if (m_pFileSender) {
        m_pFileSender->stopSending();
    }
    if (m_pFileReceiver) {
        m_pFileReceiver->stopReceiving();
    }

    // 清空传输相关变量
    m_currentTransferFile.clear();
    m_transferTargetIP.clear();

    // 隐藏传输相关控件
    m_pProgressBar->setVisible(false);
    m_pCancelTransferBtn->setVisible(false);
    m_pTransferStatusLabel->setText("传输已取消");

    qDebug() << "ChatWidget: 用户取消了文件传输";
}

bool ChatWidget::isTransferInProgress() const
{
    return !m_currentTransferFile.isEmpty() || !m_transferTargetIP.isEmpty();
}

void ChatWidget::startFileTransfer(const QString& filePath, const QString& targetIP)
{
    // 检查是否已有传输在进行
    if (isTransferInProgress()) {
        QMessageBox::warning(this, "传输忙碌", "当前已有文件传输在进行中，请等待完成后再试。");
        return;
    }

    QFileInfo fileInfo(filePath);
    if (!fileInfo.exists()) {
        QMessageBox::warning(this, "错误", "文件不存在！");
        return;
    }

    // 检查文件大小限制
    qint64 fileSize = fileInfo.size();
    if (fileSize > m_maxFileSize) {
        QString sizeStr;
        if (m_maxFileSize >= 1024 * 1024 * 1024) {
            sizeStr = QString("%1 GB").arg(m_maxFileSize / (1024.0 * 1024.0 * 1024.0), 0, 'f', 1);
        } else {
            sizeStr = QString("%1 MB").arg(m_maxFileSize / (1024.0 * 1024.0), 0, 'f', 1);
        }

        QMessageBox::warning(this, "文件过大",
            tr("文件大小超过限制 (%1)，无法传输。\n当前限制: %2")
            .arg(fileSize / (1024.0 * 1024.0), 0, 'f', 1)
            .arg(sizeStr));
        return;
    }

    // 大文件警告（超过100MB）
    if (fileSize > 100 * 1024 * 1024) {
        int ret = QMessageBox::question(this, "大文件警告",
            tr("文件大小较大 (%1 MB)，传输可能需要较长时间。是否继续？")
            .arg(fileSize / (1024.0 * 1024.0), 0, 'f', 1));
        if (ret != QMessageBox::Yes) {
            return;
        }
    }

    m_currentTransferFile = filePath;
    m_transferTargetIP = targetIP;

    // 发送文件传输请求
    sendFileTransferRequest(targetIP, filePath);

    // 启动超时定时器
    m_pRequestTimeoutTimer->start();

    // 更新UI状态
    m_pTransferStatusLabel->setText(tr("正在发送传输请求到 %1...").arg(targetIP));

    qDebug() << "ChatWidget: 开始文件传输请求，文件:" << filePath << "目标:" << targetIP;
}

void ChatWidget::sendFileTransferRequest(const QString& targetIP, const QString& filePath)
{
    Q_UNUSED(targetIP); // 目标IP通过广播发送，这里不直接使用
    QFileInfo fileInfo(filePath);

    QByteArray datagram;
    QDataStream out(&datagram, QIODevice::WriteOnly);

    // 使用FileName消息类型发送文件传输请求
    out << FileName << m_sUserName << m_sLocalIp << fileInfo.fileName() << fileInfo.size();

    // 广播文件传输请求
    if (m_pUdpSocket) {
        m_pUdpSocket->writeDatagram(datagram, QHostAddress::Broadcast, m_uPort);
    }

    qDebug() << "ChatWidget: 发送文件传输请求，文件:" << fileInfo.fileName()
             << "大小:" << fileInfo.size() << "字节";
}

void ChatWidget::handleFileTransferRequest(const QString& senderIP, const QString& fileName, qint64 fileSize)
{
    QString senderName = m_userNameMap.value(senderIP, "未知用户");

    // 格式化文件大小
    QString fileSizeStr;
    if (fileSize < 1024) {
        fileSizeStr = QString("%1 字节").arg(fileSize);
    } else if (fileSize < 1024 * 1024) {
        fileSizeStr = QString("%1 KB").arg(fileSize / 1024.0, 0, 'f', 1);
    } else {
        fileSizeStr = QString("%1 MB").arg(fileSize / (1024.0 * 1024.0), 0, 'f', 1);
    }

    // 弹出确认对话框
    QMessageBox msgBox(this);
    msgBox.setWindowTitle("文件传输请求");
    msgBox.setText(tr("%1 (%2) 想要发送文件给您").arg(senderName).arg(senderIP));
    msgBox.setInformativeText(tr("文件名: %1\n文件大小: %2\n\n是否接受此文件？").arg(fileName).arg(fileSizeStr));
    msgBox.setStandardButtons(QMessageBox::Yes | QMessageBox::No);
    msgBox.setDefaultButton(QMessageBox::Yes);
    msgBox.setIcon(QMessageBox::Question);

    int result = msgBox.exec();

    if (result == QMessageBox::Yes) {
        // 选择保存位置
        QString savePath = QFileDialog::getExistingDirectory(
            this,
            tr("选择文件保存位置"),
            QDir::homePath()
        );

        if (!savePath.isEmpty()) {
            acceptFileTransfer(senderIP, savePath);
        } else {
            rejectFileTransfer(senderIP);
        }
    } else {
        rejectFileTransfer(senderIP);
    }
}

void ChatWidget::acceptFileTransfer(const QString& senderIP, const QString& savePath)
{
    // 发送接受消息
    QByteArray datagram;
    QDataStream out(&datagram, QIODevice::WriteOnly);
    out << Message << m_sUserName << m_sLocalIp << QString("ACCEPT_FILE_TRANSFER");

    if (m_pUdpSocket) {
        m_pUdpSocket->writeDatagram(datagram, QHostAddress(senderIP), m_uPort);
    }

    // 启动文件接收
    m_pFileReceiver->startReceiving(QHostAddress(senderIP), savePath, m_transferPort);

    // 记录传输开始时间
    m_transferStartTime = QDateTime::currentDateTime();

    // 断开之前的连接，然后重新连接文件传输信号
    m_pFileReceiver->disconnect(this);
    connect(m_pFileReceiver, &FileReceiver::progressUpdated,
            this, &ChatWidget::onFileTransferProgress);
    connect(m_pFileReceiver, QOverload<const QString&>::of(&FileReceiver::transferFinished),
            this, QOverload<const QString&>::of(&ChatWidget::onFileTransferFinished));
    connect(m_pFileReceiver, &FileReceiver::errorOccurred,
            this, &ChatWidget::onFileTransferError);

    // 更新UI
    m_pTransferStatusLabel->setText(tr("正在接收来自 %1 的文件...").arg(senderIP));
    m_pProgressBar->setVisible(true);
    m_pCancelTransferBtn->setVisible(true);

    qDebug() << "ChatWidget: 接受文件传输，发送方:" << senderIP << "保存路径:" << savePath;
}

void ChatWidget::rejectFileTransfer(const QString& senderIP)
{
    // 发送拒绝消息
    QByteArray datagram;
    QDataStream out(&datagram, QIODevice::WriteOnly);
    out << Refuse << m_sUserName << m_sLocalIp << QString("REJECT_FILE_TRANSFER");

    if (m_pUdpSocket) {
        m_pUdpSocket->writeDatagram(datagram, QHostAddress(senderIP), m_uPort);
    }

    qDebug() << "ChatWidget: 拒绝文件传输，发送方:" << senderIP;
}

void ChatWidget::onFileTransferProgress(qint64 sent, qint64 total, double speedMBs)
{
    if (total > 0) {
        // 确保进度不超过100%，并处理边界情况
        int percentage = qMin(100, (int)((sent * 100) / total));

        // 如果已发送字节数等于或超过总字节数，强制设为100%
        if (sent >= total) {
            percentage = 100;
        }

        m_pProgressBar->setValue(percentage);

        // 格式化已传输和总大小
        QString sentStr, totalStr;
        if (sent < 1024 * 1024) {
            sentStr = QString("%1 KB").arg(sent / 1024.0, 0, 'f', 1);
        } else {
            sentStr = QString("%1 MB").arg(sent / (1024.0 * 1024.0), 0, 'f', 1);
        }

        if (total < 1024 * 1024) {
            totalStr = QString("%1 KB").arg(total / 1024.0, 0, 'f', 1);
        } else {
            totalStr = QString("%1 MB").arg(total / (1024.0 * 1024.0), 0, 'f', 1);
        }

        // 计算剩余时间
        QString etaStr = "";
        if (speedMBs > 0 && sent < total) {
            qint64 remainingBytes = total - sent;
            double remainingMB = remainingBytes / (1024.0 * 1024.0);
            int etaSeconds = (int)(remainingMB / speedMBs);

            if (etaSeconds < 60) {
                etaStr = tr("剩余 %1 秒").arg(etaSeconds);
            } else if (etaSeconds < 3600) {
                etaStr = tr("剩余 %1 分 %2 秒").arg(etaSeconds / 60).arg(etaSeconds % 60);
            } else {
                etaStr = tr("剩余 %1 小时").arg(etaSeconds / 3600);
            }
        } else if (percentage == 100) {
            etaStr = tr("完成");
        }

        QString statusText = tr("传输进度: %1% (%2/%3) - %4 MB/s %5")
            .arg(percentage)
            .arg(sentStr)
            .arg(totalStr)
            .arg(speedMBs, 0, 'f', 2)
            .arg(etaStr);

        m_pTransferStatusLabel->setText(statusText);

        // 更新进度条显示文本
        m_pProgressBar->setFormat(tr("%1% - %2 MB/s").arg(percentage).arg(speedMBs, 0, 'f', 1));
    }
}

void ChatWidget::onFileTransferFinished()
{
    // 强制设置进度条为100%并显示一段时间
    m_pProgressBar->setValue(100);
    m_pProgressBar->setVisible(true);

    // 延迟隐藏进度条，让用户看到100%
    QTimer::singleShot(2000, this, [this]() {
        m_pProgressBar->setVisible(false);
        m_pCancelTransferBtn->setVisible(false);
    });

    m_pTransferStatusLabel->setText("文件传输完成！");

    // 创建传输记录（发送方）
    if (!m_currentTransferFile.isEmpty() && !m_transferTargetIP.isEmpty()) {
        FileTransferRecord record;
        QFileInfo fileInfo(m_currentTransferFile);
        record.fileName = fileInfo.fileName();
        record.peerName = m_userNameMap.value(m_transferTargetIP, "未知用户");
        record.peerIP = m_transferTargetIP;
        record.fileSize = fileInfo.size();
        record.startTime = m_transferStartTime;
        record.endTime = QDateTime::currentDateTime();
        record.isSuccess = true;
        record.isSender = true;
        record.errorMessage = "";

        // 计算平均传输速度
        qint64 elapsedMs = m_transferStartTime.msecsTo(record.endTime);
        if (elapsedMs > 0) {
            record.avgSpeed = (record.fileSize / (1024.0 * 1024.0)) / (elapsedMs / 1000.0);
        } else {
            record.avgSpeed = 0.0;
        }

        addTransferRecord(record);
    }

    // 清空传输相关变量
    m_currentTransferFile.clear();
    m_transferTargetIP.clear();

    QMessageBox::information(this, "传输完成", "文件传输已成功完成！");

    qDebug() << "ChatWidget: 文件传输完成";
}

void ChatWidget::onFileTransferFinished(const QString& filePath)
{
    // 强制设置进度条为100%并显示一段时间
    m_pProgressBar->setValue(100);
    m_pProgressBar->setVisible(true);

    // 延迟隐藏进度条，让用户看到100%
    QTimer::singleShot(2000, this, [this]() {
        m_pProgressBar->setVisible(false);
        m_pCancelTransferBtn->setVisible(false);
    });

    m_pTransferStatusLabel->setText("文件接收完成！");

    // 创建传输记录（接收方）
    if (!m_transferTargetIP.isEmpty()) {
        FileTransferRecord record;
        QFileInfo fileInfo(filePath);
        record.fileName = fileInfo.fileName();
        record.peerName = m_userNameMap.value(m_transferTargetIP, "未知用户");
        record.peerIP = m_transferTargetIP;
        record.fileSize = fileInfo.size();
        record.startTime = m_transferStartTime;
        record.endTime = QDateTime::currentDateTime();
        record.isSuccess = true;
        record.isSender = false;
        record.errorMessage = "";

        // 计算平均传输速度
        qint64 elapsedMs = m_transferStartTime.msecsTo(record.endTime);
        if (elapsedMs > 0) {
            record.avgSpeed = (record.fileSize / (1024.0 * 1024.0)) / (elapsedMs / 1000.0);
        } else {
            record.avgSpeed = 0.0;
        }

        addTransferRecord(record);
    }

    // 清空传输相关变量
    m_currentTransferFile.clear();
    m_transferTargetIP.clear();

    QMessageBox::information(this, "接收完成",
        tr("文件接收已成功完成！\n保存位置: %1").arg(filePath));

    qDebug() << "ChatWidget: 文件接收完成，保存到:" << filePath;
}

void ChatWidget::onFileTransferError(const QString& error)
{
    m_pProgressBar->setVisible(false);
    m_pCancelTransferBtn->setVisible(false);
    m_pTransferStatusLabel->setText(tr("传输错误: %1").arg(error));

    // 创建失败的传输记录
    if (!m_currentTransferFile.isEmpty() || !m_transferTargetIP.isEmpty()) {
        FileTransferRecord record;

        if (!m_currentTransferFile.isEmpty()) {
            // 发送方错误
            QFileInfo fileInfo(m_currentTransferFile);
            record.fileName = fileInfo.fileName();
            record.fileSize = fileInfo.size();
            record.isSender = true;
        } else {
            // 接收方错误（文件名可能未知）
            record.fileName = "未知文件";
            record.fileSize = 0;
            record.isSender = false;
        }

        record.peerName = m_userNameMap.value(m_transferTargetIP, "未知用户");
        record.peerIP = m_transferTargetIP;
        record.startTime = m_transferStartTime;
        record.endTime = QDateTime::currentDateTime();
        record.isSuccess = false;
        record.errorMessage = error;
        record.avgSpeed = 0.0;

        addTransferRecord(record);
    }

    // 清空传输相关变量
    m_currentTransferFile.clear();
    m_transferTargetIP.clear();

    QMessageBox::critical(this, "传输错误", tr("文件传输失败：\n%1").arg(error));

    qDebug() << "ChatWidget: 文件传输错误:" << error;
}

void ChatWidget::onRequestTimeout()
{
    // 传输请求超时
    m_pTransferStatusLabel->setText("传输请求超时，对方可能不在线");

    // 清空传输相关变量
    m_currentTransferFile.clear();
    m_transferTargetIP.clear();

    QMessageBox::warning(this, "请求超时",
        "文件传输请求超时，对方可能不在线或网络连接有问题。");

    qDebug() << "ChatWidget: 文件传输请求超时";
}

void ChatWidget::addTransferRecord(const FileTransferRecord& record)
{
    m_transferHistory.append(record);

    // 限制历史记录数量，保留最近的100条
    if (m_transferHistory.size() > 100) {
        m_transferHistory.removeFirst();
    }

    updateHistoryTable();

    qDebug() << "ChatWidget: 添加传输记录:" << record.fileName
             << (record.isSuccess ? "成功" : "失败");
}

void ChatWidget::updateHistoryTable()
{
    if (!m_pHistoryTable) return;

    m_pHistoryTable->setRowCount(0);

    // 按时间倒序显示（最新的在上面）
    for (int i = m_transferHistory.size() - 1; i >= 0; --i) {
        const FileTransferRecord& record = m_transferHistory[i];

        int row = m_pHistoryTable->rowCount();
        m_pHistoryTable->insertRow(row);

        // 文件名（带图标）
        QString fileIcon = getFileTypeIcon(record.fileName);
        QTableWidgetItem* fileNameItem = new QTableWidgetItem(fileIcon + " " + record.fileName);
        fileNameItem->setTextAlignment(Qt::AlignLeft | Qt::AlignVCenter);

        // 对方用户
        QString peerText = record.isSender ?
            QString("→ %1").arg(record.peerName) :
            QString("← %1").arg(record.peerName);
        QTableWidgetItem* peerItem = new QTableWidgetItem(peerText);
        peerItem->setTextAlignment(Qt::AlignCenter);

        // 文件大小
        QString sizeText;
        if (record.fileSize < 1024) {
            sizeText = QString("%1 B").arg(record.fileSize);
        } else if (record.fileSize < 1024 * 1024) {
            sizeText = QString("%1 KB").arg(record.fileSize / 1024.0, 0, 'f', 1);
        } else {
            sizeText = QString("%1 MB").arg(record.fileSize / (1024.0 * 1024.0), 0, 'f', 1);
        }
        QTableWidgetItem* sizeItem = new QTableWidgetItem(sizeText);
        sizeItem->setTextAlignment(Qt::AlignCenter);

        // 状态
        QTableWidgetItem* statusItem = new QTableWidgetItem(record.isSuccess ? "✅ 成功" : "❌ 失败");
        statusItem->setTextAlignment(Qt::AlignCenter);
        if (record.isSuccess) {
            statusItem->setForeground(QColor("#28A745"));
        } else {
            statusItem->setForeground(QColor("#DC3545"));
            statusItem->setToolTip(record.errorMessage);
        }

        // 传输速度
        QString speedText = record.isSuccess ?
            QString("%1 MB/s").arg(record.avgSpeed, 0, 'f', 2) : "-";
        QTableWidgetItem* speedItem = new QTableWidgetItem(speedText);
        speedItem->setTextAlignment(Qt::AlignCenter);

        // 时间
        QString timeText = record.endTime.toString("MM-dd hh:mm");
        QTableWidgetItem* timeItem = new QTableWidgetItem(timeText);
        timeItem->setTextAlignment(Qt::AlignCenter);
        timeItem->setToolTip(record.endTime.toString("yyyy-MM-dd hh:mm:ss"));

        m_pHistoryTable->setItem(row, 0, fileNameItem);
        m_pHistoryTable->setItem(row, 1, peerItem);
        m_pHistoryTable->setItem(row, 2, sizeItem);
        m_pHistoryTable->setItem(row, 3, statusItem);
        m_pHistoryTable->setItem(row, 4, speedItem);
        m_pHistoryTable->setItem(row, 5, timeItem);
    }

    qDebug() << "ChatWidget: 更新历史记录表格，共" << m_transferHistory.size() << "条记录";
}

void ChatWidget::onClearHistoryClicked()
{
    if (m_transferHistory.isEmpty()) return;

    int ret = QMessageBox::question(this, "清空历史",
        "确定要清空所有传输历史记录吗？此操作不可撤销。",
        QMessageBox::Yes | QMessageBox::No,
        QMessageBox::No);

    if (ret == QMessageBox::Yes) {
        m_transferHistory.clear();
        updateHistoryTable();
        qDebug() << "ChatWidget: 清空传输历史记录";
    }
}

QString ChatWidget::getFileTypeIcon(const QString& fileName) const
{
    QString extension = QFileInfo(fileName).suffix().toLower();

    // 图片文件
    if (extension == "jpg" || extension == "jpeg" || extension == "png" ||
        extension == "gif" || extension == "bmp" || extension == "svg") {
        return "🖼️";
    }
    // 视频文件
    else if (extension == "mp4" || extension == "avi" || extension == "mkv" ||
             extension == "mov" || extension == "wmv" || extension == "flv") {
        return "🎬";
    }
    // 音频文件
    else if (extension == "mp3" || extension == "wav" || extension == "flac" ||
             extension == "aac" || extension == "ogg") {
        return "🎵";
    }
    // 文档文件
    else if (extension == "doc" || extension == "docx") {
        return "📄";
    }
    else if (extension == "pdf") {
        return "📕";
    }
    else if (extension == "xls" || extension == "xlsx") {
        return "📊";
    }
    else if (extension == "ppt" || extension == "pptx") {
        return "📋";
    }
    else if (extension == "txt") {
        return "📝";
    }
    // 压缩文件
    else if (extension == "zip" || extension == "rar" || extension == "7z" ||
             extension == "tar" || extension == "gz") {
        return "🗜️";
    }
    // 代码文件
    else if (extension == "cpp" || extension == "c" || extension == "h" ||
             extension == "java" || extension == "py" || extension == "js" ||
             extension == "html" || extension == "css") {
        return "💻";
    }
    // 可执行文件
    else if (extension == "exe" || extension == "msi" || extension == "app" ||
             extension == "deb" || extension == "rpm") {
        return "⚙️";
    }
    // 默认文件图标
    else {
        return "📁";
    }
}

void ChatWidget::onSettingsClicked()
{
    // 创建设置对话框
    QDialog settingsDialog(this);
    settingsDialog.setWindowTitle("文件传输设置");
    settingsDialog.setFixedSize(400, 300);

    QVBoxLayout* mainLayout = new QVBoxLayout(&settingsDialog);
    mainLayout->setSpacing(15);
    mainLayout->setContentsMargins(20, 20, 20, 20);

    // 传输端口设置
    QHBoxLayout* portLayout = new QHBoxLayout();
    QLabel* portLabel = new QLabel("传输端口:");
    portLabel->setMinimumWidth(100);
    QSpinBox* portSpinBox = new QSpinBox();
    portSpinBox->setRange(1024, 65535);
    portSpinBox->setValue(m_transferPort);
    portLayout->addWidget(portLabel);
    portLayout->addWidget(portSpinBox);
    portLayout->addStretch();

    // 超时时间设置
    QHBoxLayout* timeoutLayout = new QHBoxLayout();
    QLabel* timeoutLabel = new QLabel("请求超时(秒):");
    timeoutLabel->setMinimumWidth(100);
    QSpinBox* timeoutSpinBox = new QSpinBox();
    timeoutSpinBox->setRange(10, 300);
    timeoutSpinBox->setValue(m_requestTimeoutSec);
    timeoutLayout->addWidget(timeoutLabel);
    timeoutLayout->addWidget(timeoutSpinBox);
    timeoutLayout->addStretch();

    // 文件大小限制设置
    QHBoxLayout* sizeLayout = new QHBoxLayout();
    QLabel* sizeLabel = new QLabel("文件大小限制(MB):");
    sizeLabel->setMinimumWidth(100);
    QSpinBox* sizeSpinBox = new QSpinBox();
    sizeSpinBox->setRange(1, 10240); // 1MB到10GB
    sizeSpinBox->setValue(m_maxFileSize / (1024 * 1024));
    sizeLayout->addWidget(sizeLabel);
    sizeLayout->addWidget(sizeSpinBox);
    sizeLayout->addStretch();

    // 说明文本
    QLabel* infoLabel = new QLabel(
        "注意：\n"
        "• 修改端口后需要重启应用才能生效\n"
        "• 请确保防火墙允许所选端口\n"
        "• 文件大小限制仅对发送方有效"
    );
    infoLabel->setStyleSheet(
        "QLabel { "
        "   color: #6C757D; "
        "   font-size: 11px; "
        "   background: #F8F9FA; "
        "   border: 1px solid #DEE2E6; "
        "   border-radius: 4px; "
        "   padding: 8px; "
        "}"
    );

    // 按钮
    QHBoxLayout* buttonLayout = new QHBoxLayout();
    QPushButton* okBtn = new QPushButton("确定");
    QPushButton* cancelBtn = new QPushButton("取消");

    okBtn->setStyleSheet(
        "QPushButton { "
        "   background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
        "       stop:0 #28A745, stop:1 #1E7E34); "
        "   color: white; "
        "   border: none; "
        "   border-radius: 4px; "
        "   padding: 6px 16px; "
        "   font-weight: bold; "
        "}"
        "QPushButton:hover { "
        "   background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
        "       stop:0 #218838, stop:1 #1C7430); "
        "}"
    );

    cancelBtn->setStyleSheet(
        "QPushButton { "
        "   background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
        "       stop:0 #6C757D, stop:1 #5A6268); "
        "   color: white; "
        "   border: none; "
        "   border-radius: 4px; "
        "   padding: 6px 16px; "
        "   font-weight: bold; "
        "}"
        "QPushButton:hover { "
        "   background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
        "       stop:0 #5A6268, stop:1 #495057); "
        "}"
    );

    connect(okBtn, &QPushButton::clicked, &settingsDialog, &QDialog::accept);
    connect(cancelBtn, &QPushButton::clicked, &settingsDialog, &QDialog::reject);

    buttonLayout->addStretch();
    buttonLayout->addWidget(okBtn);
    buttonLayout->addWidget(cancelBtn);

    // 添加到主布局
    mainLayout->addLayout(portLayout);
    mainLayout->addLayout(timeoutLayout);
    mainLayout->addLayout(sizeLayout);
    mainLayout->addWidget(infoLabel);
    mainLayout->addStretch();
    mainLayout->addLayout(buttonLayout);

    // 显示对话框
    if (settingsDialog.exec() == QDialog::Accepted) {
        // 保存设置
        m_transferPort = portSpinBox->value();
        m_requestTimeoutSec = timeoutSpinBox->value();
        m_maxFileSize = (qint64)sizeSpinBox->value() * 1024 * 1024;

        // 更新超时定时器间隔
        m_pRequestTimeoutTimer->setInterval(m_requestTimeoutSec * 1000);

        qDebug() << "ChatWidget: 设置已更新 - 端口:" << m_transferPort
                 << "超时:" << m_requestTimeoutSec << "大小限制:" << m_maxFileSize;

        QMessageBox::information(this, "设置已保存",
            "设置已保存成功！\n端口修改需要重启应用才能生效。");
    }
}

void ChatWidget::sendTextMessage()
{
    QString message = m_pInputEdit->text().trimmed();
    if(message.isEmpty()) {
        return; // 静默处理空消息，不显示警告
    }

    // 发送消息
    sendMessage(Message);
}

void ChatWidget::sendMessage(MessageType type)
{
    QByteArray datagram;
    QDataStream out(&datagram, QIODevice::WriteOnly);

    switch(type) {
    case Message:
        {
            QString message = m_pInputEdit->text().trimmed();
            if(message.isEmpty()) {
                return; // 静默处理空消息
            }

            out << type << m_sUserName << m_sLocalIp << message;

            // 保存自己发送的消息到数据库
            saveMessageToDatabase(message, m_sLocalIp);

            // Display locally with formatted style
            appendFormattedMessage(
                QDateTime::currentDateTime().toString("hh:mm:ss"),
                "您",
                message,
                false
            );

            // 清空输入框
            m_pInputEdit->clear();
        }
        break;

    case NewParticipant:
        out << type << m_sUserName << QHostInfo::localHostName() << m_sLocalIp;
        updateStatus(tr("正在广播加入消息"));
        appendFormattedMessage(
            QDateTime::currentDateTime().toString("hh:mm:ss"),
            "系统",
            tr("您已加入聊天室"),
            true
        );

        // 在线人数已在构造函数中设置，这里不需要重复设置
        qDebug() << "ChatWidget: 广播加入消息，当前在线:" << m_nOnlineCount;
        break;

    case Participantleft:
        out << type << m_sUserName << QHostInfo::localHostName();
        updateStatus(tr("正在广播离开消息"));

        // 自己离开时，清空用户列表并重置在线人数
        m_onlineUsers.clear();
        m_nOnlineCount = 0;
        updateOnlineCount(m_nOnlineCount);
        qDebug() << "ChatWidget: 广播离开消息，在线人数重置为0";
        break;

    case UserListRequest:
        out << type << m_sUserName << m_sLocalIp;
        qDebug() << "ChatWidget: 广播用户列表请求";
        break;

    case UserListResponse:
        // 发送当前用户列表给请求者
        out << type << m_sUserName << m_sLocalIp;
        // 添加在线用户列表
        out << m_onlineUsers.size();
        for (const QString& ip : m_onlineUsers) {
            out << ip << m_userNameMap.value(ip, "未知用户");
        }
        qDebug() << "ChatWidget: 发送用户列表响应，包含" << m_onlineUsers.size() << "个用户";
        break;

    default: // For simplicity, ignore other types
        return;
    }

    // Send via broadcast
    if (m_pUdpSocket) {
        m_pUdpSocket->writeDatagram(datagram, QHostAddress::Broadcast, m_uPort);
    }
}

void ChatWidget::processPendingDatagrams()
{
    while (m_pUdpSocket && m_pUdpSocket->hasPendingDatagrams()) {
        QByteArray datagram;
        datagram.resize(m_pUdpSocket->pendingDatagramSize());
        QHostAddress sender;
        quint16 senderPort;

        m_pUdpSocket->readDatagram(datagram.data(), datagram.size(), &sender, &senderPort);

        QDataStream in(&datagram, QIODevice::ReadOnly);
        in.setVersion(QDataStream::Qt_5_14); // 设置数据流版本保持一致

        int msgType;
        in >> msgType;

        switch (msgType) {
        case Message:
            {
                QString userName, ipAddress, message;
                in >> userName >> ipAddress >> message;

                // 添加过滤：如果是自己发送的消息，则跳过显示
                if(ipAddress == getIP()) {
                    break; // 跳过自己发送的消息
                }

                // 检查是否是文件传输确认消息
                if (message == "ACCEPT_FILE_TRANSFER") {
                    // 对方接受了文件传输，开始发送文件
                    if (!m_currentTransferFile.isEmpty() && ipAddress == m_transferTargetIP) {
                        // 停止超时定时器
                        m_pRequestTimeoutTimer->stop();

                        // 启动文件发送
                        m_pFileSender->startSending(m_currentTransferFile, m_transferPort);

                        // 断开之前的连接，然后重新连接文件传输信号
                        m_pFileSender->disconnect(this);
                        connect(m_pFileSender, &FileSender::progressUpdated,
                                this, &ChatWidget::onFileTransferProgress);
                        connect(m_pFileSender, &FileSender::transferFinished,
                                this, QOverload<>::of(&ChatWidget::onFileTransferFinished));
                        connect(m_pFileSender, &FileSender::errorOccurred,
                                this, &ChatWidget::onFileTransferError);

                        // 记录传输开始时间
                        m_transferStartTime = QDateTime::currentDateTime();

                        // 更新UI
                        m_pTransferStatusLabel->setText(tr("正在发送文件到 %1...").arg(ipAddress));
                        m_pProgressBar->setVisible(true);
                        m_pCancelTransferBtn->setVisible(true);

                        qDebug() << "ChatWidget: 开始发送文件到" << ipAddress;
                    }
                    break; // 不显示这个系统消息
                }

                saveMessageToDatabase(message, ipAddress);

                // 使用格式化的消息显示
                appendFormattedMessage(
                    QDateTime::currentDateTime().toString("hh:mm:ss"),
                    userName,
                    message,
                    false
                );
            }
            break;

        case NewParticipant:
            {
                QString userName, hostName, ipAddress;
                in >> userName >> hostName >> ipAddress;

                // 处理所有用户加入（包括自己）
                // 检查用户是否已经在列表中，避免重复添加
                if (!m_onlineUsers.contains(ipAddress)) {
                    m_onlineUsers.append(ipAddress);
                    m_userNameMap[ipAddress] = userName; // 保存用户名映射
                    m_nOnlineCount = m_onlineUsers.size();

                    // 如果不是自己，则显示加入消息
                    if(ipAddress != getIP()) {

                        updateStatus(tr("%1 从 %2 加入了聊天室").arg(userName).arg(ipAddress));
                        appendFormattedMessage(
                            QDateTime::currentDateTime().toString("hh:mm:ss"),
                            "系统",
                            tr("%1 加入了聊天室").arg(userName),
                            true
                        );

                        // 新用户加入时，发送用户列表响应
                        sendUserListResponse(ipAddress);
                    }

                    updateOnlineCount(m_nOnlineCount);

                    // 如果当前在文件传输模式，更新用户列表
                    if (m_currentMode == ChatMode_FileTransfer) {
                        updateUserList();
                    }

                    qDebug() << "ChatWidget: 用户加入，当前在线:" << m_nOnlineCount << "用户列表:" << m_onlineUsers;
                } else {
                    // 更新用户名映射（用户名可能会变化）
                    m_userNameMap[ipAddress] = userName;
                }
            }
            break;

        case Participantleft:
            {
                QString userName, hostName;
                in >> userName >> hostName;

                // 注意：离开消息中没有IP地址，这里需要通过用户名或主机名来识别
                // 由于协议限制，我们暂时使用简单的递减方式
                // 更好的方式是在协议中包含IP地址或使用心跳机制

                updateStatus(tr("%1 离开了聊天室").arg(userName));
                appendFormattedMessage(
                    QDateTime::currentDateTime().toString("hh:mm:ss"),
                    "系统",
                    tr("%1 离开了聊天室").arg(userName),
                    true
                );

                // 简单递减在线人数（不包括自己）
                if (m_nOnlineCount > 1) {
                    m_nOnlineCount--;
                    updateOnlineCount(m_nOnlineCount);

                    // 如果当前在文件传输模式，更新用户列表
                    if (m_currentMode == ChatMode_FileTransfer) {
                        updateUserList();
                    }

                    qDebug() << "ChatWidget: 用户离开，当前在线:" << m_nOnlineCount;
                }
            }
            break;

        case FileName:
            {
                QString userName, ipAddress, fileName;
                qint64 fileSize;
                in >> userName >> ipAddress >> fileName >> fileSize;

                // 如果不是自己发送的文件传输请求
                if (ipAddress != getIP()) {
                    handleFileTransferRequest(ipAddress, fileName, fileSize);
                }
            }
            break;

        case Refuse:
            {
                QString userName, ipAddress, message;
                in >> userName >> ipAddress >> message;

                // 如果是文件传输被拒绝
                if (message == "REJECT_FILE_TRANSFER" && ipAddress != getIP()) {
                    // 停止超时定时器
                    m_pRequestTimeoutTimer->stop();

                    // 清空传输相关变量
                    m_currentTransferFile.clear();
                    m_transferTargetIP.clear();

                    m_pTransferStatusLabel->setText(tr("%1 拒绝了文件传输").arg(userName));
                    QMessageBox::information(this, "传输被拒绝",
                        tr("%1 拒绝了您的文件传输请求").arg(userName));
                }
            }
            break;

        case UserListRequest:
            {
                QString userName, ipAddress;
                in >> userName >> ipAddress;

                // 如果不是自己的请求，发送用户列表响应
                if (ipAddress != m_sLocalIp) {
                    qDebug() << "ChatWidget: 收到来自" << userName << "(" << ipAddress << ")的用户列表请求";
                    sendUserListResponse(ipAddress);
                }
            }
            break;

        case UserListResponse:
            {
                QString userName, ipAddress;
                in >> userName >> ipAddress;

                // 检查响应数据是否有效
                if (userName.isEmpty() || ipAddress.isEmpty()) {
                    qDebug() << "ChatWidget: 收到无效的用户列表响应，跳过";
                    break;
                }

                // 如果不是自己的响应，处理用户列表
                if (ipAddress != m_sLocalIp) {
                    int userCount;
                    in >> userCount;

                    // 检查用户数量是否合理
                    if (userCount < 0 || userCount > 100) {
                        qDebug() << "ChatWidget: 用户数量异常:" << userCount << "，跳过处理";
                        break;
                    }

                    qDebug() << "ChatWidget: 收到来自" << userName << "(" << ipAddress << ")的用户列表响应，包含" << userCount << "个用户";

                    // 处理用户列表
                    for (int i = 0; i < userCount; ++i) {
                        QString userIp, userUserName;
                        in >> userIp >> userUserName;

                        // 检查用户数据是否有效
                        if (userIp.isEmpty() || userUserName.isEmpty()) {
                            qDebug() << "ChatWidget: 跳过无效用户数据:" << userUserName << "(" << userIp << ")";
                            continue;
                        }

                        // 添加到本地用户列表（如果不存在）
                        if (!m_onlineUsers.contains(userIp)) {
                            m_onlineUsers.append(userIp);
                            m_userNameMap[userIp] = userUserName;
                            qDebug() << "ChatWidget: 从用户列表响应中添加用户:" << userUserName << "(" << userIp << ")";
                        }
                    }

                    // 更新在线人数和界面
                    m_nOnlineCount = m_onlineUsers.size();
                    updateOnlineCount(m_nOnlineCount);

                    // 如果当前在文件传输模式，更新用户列表
                    if (m_currentMode == ChatMode_FileTransfer) {
                        updateUserList();
                    }

                    qDebug() << "ChatWidget: 用户列表同步完成，当前在线:" << m_nOnlineCount << "用户列表:" << m_onlineUsers;
                } else {
                    qDebug() << "ChatWidget: 跳过自己的用户列表响应";
                }
            }
            break;
        }
    }
}

void ChatWidget::onSendButtonClicked()
{
    sendTextMessage();
}

void ChatWidget::sendUserListResponse(const QString& targetIP)
{
    if (!m_pUdpSocket) return;

    QByteArray datagram;
    QDataStream out(&datagram, QIODevice::WriteOnly);
    out.setVersion(QDataStream::Qt_5_14);

    // 发送用户列表响应
    out << UserListResponse << m_sUserName << m_sLocalIp;
    // 添加在线用户列表
    out << m_onlineUsers.size();
    for (const QString& ip : m_onlineUsers) {
        out << ip << m_userNameMap.value(ip, "未知用户");
    }

    // 广播响应（所有人都能收到，但只有请求者会处理）
    m_pUdpSocket->writeDatagram(datagram, QHostAddress::Broadcast, m_uPort);

    qDebug() << "ChatWidget: 发送用户列表响应给" << targetIP << "，包含" << m_onlineUsers.size() << "个用户";
}

void ChatWidget::updateStatus(const QString& message)
{
    if (m_pStatusLabel) {
        m_pStatusLabel->setText(message);
    }
    qDebug() << "ChatWidget Status:" << message;
}

void ChatWidget::updateOnlineCount(int count)
{
    if (m_pOnlineCountLabel) {
        m_pOnlineCountLabel->setText(QString("在线用户: %1").arg(count));
    }
}

void ChatWidget::appendFormattedMessage(const QString& time, const QString& sender, const QString& message, bool isSystem)
{
    if (!m_pMsgTEdit) return;

    QString formattedMessage;

    if (isSystem) {
        // 系统消息样式
        formattedMessage = QString(
            "<div style='margin: 4px 0; padding: 6px 10px; background-color: #E3F2FD; border-left: 3px solid #2196F3; border-radius: 4px;'>"
            "<span style='color: #1976D2; font-weight: bold; font-size: 11px;'>[%1]</span> "
            "<span style='color: #424242; font-size: 12px;'>%2</span>"
            "</div>"
        ).arg(time).arg(message);
    } else if (sender == "您") {
        // 自己发送的消息样式
        formattedMessage = QString(
            "<div style='margin: 4px 0; padding: 6px 10px; background-color: #E8F5E8; border-left: 3px solid #4CAF50; border-radius: 4px;'>"
            "<span style='color: #2E7D32; font-weight: bold; font-size: 11px;'>[%1]</span> "
            "<span style='color: #1B5E20; font-weight: bold; font-size: 12px;'>[%2]</span> "
            "<span style='color: #424242; font-size: 12px;'>%3</span>"
            "</div>"
        ).arg(time).arg(sender).arg(message);
    } else {
        // 其他用户消息样式
        formattedMessage = QString(
            "<div style='margin: 4px 0; padding: 6px 10px; background-color: #F5F5F5; border-left: 3px solid #9E9E9E; border-radius: 4px;'>"
            "<span style='color: #616161; font-weight: bold; font-size: 11px;'>[%1]</span> "
            "<span style='color: #1976D2; font-weight: bold; font-size: 12px;'>[%2]</span> "
            "<span style='color: #424242; font-size: 12px;'>%3</span>"
            "</div>"
        ).arg(time).arg(sender).arg(message);
    }

    m_pMsgTEdit->append(formattedMessage);

    // 自动滚动到底部
    QScrollBar *scrollBar = m_pMsgTEdit->verticalScrollBar();
    scrollBar->setValue(scrollBar->maximum());
}

QString ChatWidget::getIP()
{
    QString localHostName = QHostInfo::localHostName();
    QHostInfo info = QHostInfo::fromName(localHostName);

    foreach(QHostAddress address, info.addresses()) {
        if(address.protocol() == QAbstractSocket::IPv4Protocol)
            return address.toString();
    }

    return tr("Unknown");
}

QString ChatWidget::getUserName()
{
    QStringList envVariables;
    envVariables << "USERNAME.*" << "USER.*" << "USERDOMAIN.*"
                 << "HOSTNAME.*" << "DOMAINNAME.*";

    QStringList environment = QProcess::systemEnvironment();
    foreach(QString pattern, envVariables) {
        int index = environment.indexOf(QRegExp(pattern));
        if(index != -1) {
            QStringList parts = environment.at(index).split('=');
            if(parts.size() == 2) return parts.at(1);
        }
    }

    return tr("Unknown User");
}

void ChatWidget::saveMessageToDatabase(const QString &message, const QString &senderIp)
{
    if (!m_dbHandler) {
        qDebug() << "Database handler is null, cannot save message";
        return;
    }

    // 获取数据库连接
    if (!m_dbHandler->isConnected()) {
        qDebug() << "Database is not connected";
        return;
    }

    QSqlDatabase db = m_dbHandler->database();

    QSqlQuery query(db);

    // 确保是纯文本
    QString plainText = message;

    // 去除HTML标签
    QTextDocument doc;
    doc.setHtml(message);
    plainText = doc.toPlainText();

    query.prepare("INSERT INTO udpmessage (message, sendip, sendtime) "
                 "VALUES (:message, :sendip, NOW())");
    query.bindValue(":message", plainText);
    query.bindValue(":sendip", senderIp);

    if (!query.exec()) {
        qDebug() << "保存消息失败:" << query.lastError().text();
    } else {
        qDebug() << "消息已保存:" << senderIp << ":" << plainText.left(20) << "...";
    }
}
