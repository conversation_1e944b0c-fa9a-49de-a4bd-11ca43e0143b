#ifndef DISPLAYVIEW_H
#define DISPLAYVIEW_H

#include "../view.h"
#include <QScrollBar>
#include <QImage>
#include <QSize>

class TpMeasureManager;
class AuxRect;

class DisplayWidget : public ViewWidget
{
    Q_OBJECT
public:
    explicit DisplayWidget(QWidget* parent = nullptr, bool bEnableAWBRoi = false);
    ~DisplayWidget();

    void EnableCalibration(bool bEnabled);
    TpMeasureManager* GetMeasureManager() const;

protected slots:
    void OnAuxRectChangedFini(const QRectF& newRect, const eAuxRectType type);
    void OnScaleChanged(double scale);

protected:
    void mouseMoveEvent(QMouseEvent* event) override;
    void mousePressEvent(QMouseEvent* event) override;
    void mouseReleaseEvent(QMouseEvent* event) override;

    void paint(QPainter &painter) override;
    void UpdateMeasureType();
    void DoZoomChangedExt() override;
    void UpdateAuxRectCursor(ePOS type);

    AuxRect*          m_pAWBRoiRect;
    bool              m_bAWBRoiBtnDown;
    bool              m_bMeasureBtnDown;
    TpMeasureManager* m_pMeasureManager;
};

class DisplayView : public View
{
    Q_OBJECT
public:
    explicit DisplayView(DisplayWidget* pDisplayWidget, QWidget* parent = nullptr);

    void EnableCalibration(bool bEnabled);
    TpMeasureManager* GetMeasureManager() const;
};

#endif
