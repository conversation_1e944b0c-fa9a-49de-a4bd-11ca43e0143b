#include "global.h"
#include "tchar.h"
#include <chrono>
#include <QCoreApplication>
#include <QFile>
#include <QFileInfo>
#include <QTextStream>
#include <QDir>

#ifdef _WIN32
#include"windows.h"
#endif

QFontMetrics* g_pFontMetrics = nullptr;
bool g_bWBDlgHidden = false;
int g_WBMode = WB_AUTO;
QList<Calibration> g_caliList;
int g_caliIndex = 0;

int g_zoom[] = { 10, 15, 20, 25, 33, 50, 67, 75, 100, 150, 200, 300, 400 };
int g_zoomcnt = sizeof(g_zoom) / sizeof(g_zoom[0]);
int g_zoomIndex = g_zoomcnt + 2;
double g_zoomScale = 1.0;

double g_zoomWheel[] = { 5, 10, 12.5, 20, 25, 30, 33.3, 40, 50, 60, 66.7, 70, 75, 80, 90, 100,
                        150, 200, 300, 400, 500, 600, 700, 800, 1000, 1200, 1300, 1400, 1500, 1600 };
int g_zoomWheelCnt = sizeof(g_zoomWheel) / sizeof(g_zoomWheel[0]);

int GetZoomWheelIndex(double scale, bool bUp)
{
    scale *= 100;
    for(int i = 0; i < g_zoomWheelCnt; ++i)
    {
        if(fabs(scale - g_zoomWheel[i]) < DF_EP)
            return i;
        else if(scale < g_zoomWheel[i])
        {
            if(0 == i)
                return i;
            else
            {
                if(bUp)
                    return i;
                else
                    return i - 1;
            }
        }
    }
    return g_zoomWheelCnt - 1;
}

int g_measureActType = ACT_NONE;

int g_minResWidth = 100000000;
int g_minResHeight = 100000000;
int g_maxResWidth = 0;
int g_maxResHeight = 0;
