#include <QApplication>
#include <QDebug>
#include <QTimer>
#include "../panel/login/tpdatabasehandler.h"

class DatabaseTester : public QObject
{
    Q_OBJECT
    
public:
    DatabaseTester(QObject* parent = nullptr) : QObject(parent)
    {
        m_dbHandler = new TpdatabaseHandler(this);
        
        // 连接信号
        connect(m_dbHandler, &TpdatabaseHandler::connectionStateChanged,
                this, &DatabaseTester::onConnectionStateChanged);
        connect(m_dbHandler, &TpdatabaseHandler::errorOccurred,
                this, &DatabaseTester::onErrorOccurred);
    }
    
    void testConnection()
    {
        qDebug() << "=== 数据库连接测试开始 ===";
        
        // 显示配置信息
        DatabaseConfig config = m_dbHandler->getConfig();
        qDebug() << "配置信息:";
        qDebug() << "  主机:" << config.hostName << ":" << config.port;
        qDebug() << "  数据库:" << config.databaseName;
        qDebug() << "  用户:" << config.userName;
        
        // 尝试连接
        if (m_dbHandler->connectToDatabase()) {
            qDebug() << "✅ 数据库连接成功!";
            
            // 测试用户验证
            testUserValidation();
            
            // 测试数据库查询
            testDatabaseQuery();
            
        } else {
            qDebug() << "❌ 数据库连接失败:" << m_dbHandler->lastError();
        }
        
        // 5秒后退出
        QTimer::singleShot(5000, qApp, &QApplication::quit);
    }

private slots:
    void onConnectionStateChanged(DatabaseConnectionState state)
    {
        QString stateStr;
        switch (state) {
            case DatabaseConnectionState::NotConnected:
                stateStr = "未连接";
                break;
            case DatabaseConnectionState::Connecting:
                stateStr = "连接中";
                break;
            case DatabaseConnectionState::Connected:
                stateStr = "已连接";
                break;
            case DatabaseConnectionState::ConnectionFailed:
                stateStr = "连接失败";
                break;
        }
        qDebug() << "🔄 连接状态变化:" << stateStr;
    }
    
    void onErrorOccurred(const QString& error)
    {
        qDebug() << "❌ 数据库错误:" << error;
    }

private:
    void testUserValidation()
    {
        qDebug() << "\n--- 测试用户验证 ---";
        
        // 测试有效用户（如果存在）
        if (m_dbHandler->validateUser("admin", "admin123")) {
            qDebug() << "✅ 用户验证成功: admin";
        } else {
            qDebug() << "ℹ️ 用户验证失败: admin（可能用户不存在）";
        }
        
        // 测试无效用户
        if (m_dbHandler->validateUser("invalid", "invalid")) {
            qDebug() << "❌ 意外：无效用户验证成功";
        } else {
            qDebug() << "✅ 正确：无效用户验证失败";
        }
    }
    
    void testDatabaseQuery()
    {
        qDebug() << "\n--- 测试数据库查询 ---";
        
        if (!m_dbHandler->isConnected()) {
            qDebug() << "❌ 数据库未连接，无法测试查询";
            return;
        }
        
        QSqlDatabase db = m_dbHandler->database();
        QSqlQuery query(db);
        
        // 测试简单查询
        if (query.exec("SELECT 1 as test_value")) {
            if (query.next()) {
                qDebug() << "✅ 基本查询测试成功，结果:" << query.value(0).toInt();
            }
        } else {
            qDebug() << "❌ 基本查询测试失败:" << query.lastError().text();
        }
        
        // 测试表查询（如果admin表存在）
        if (query.exec("SELECT COUNT(*) as user_count FROM admin")) {
            if (query.next()) {
                qDebug() << "✅ admin表查询成功，用户数量:" << query.value(0).toInt();
            }
        } else {
            qDebug() << "ℹ️ admin表查询失败（表可能不存在）:" << query.lastError().text();
        }
    }

private:
    TpdatabaseHandler* m_dbHandler;
};

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);
    
    qDebug() << "数据库连接测试工具";
    qDebug() << "==================";
    
    DatabaseTester tester;
    
    // 延迟启动测试，让应用程序完全初始化
    QTimer::singleShot(100, &tester, &DatabaseTester::testConnection);
    
    return app.exec();
}

#include "test_database_connection.moc"
