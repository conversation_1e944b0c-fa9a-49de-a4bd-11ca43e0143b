#include "TpDatabaseResult.h"
#include <QSqlError>
#include <QDebug>

TpDatabaseResult::TpDatabaseResult()
    : m_errorCode(TpDatabaseErrorCode::Success)
    , m_affectedRows(-1)
{
}

TpDatabaseResult::TpDatabaseResult(const QVariant& data)
    : m_errorCode(TpDatabaseErrorCode::Success)
    , m_data(data)
    , m_affectedRows(-1)
{
}

TpDatabaseResult::TpDatabaseResult(TpDatabaseErrorCode errorCode, const QString& errorMessage)
    : m_errorCode(errorCode)
    , m_errorMessage(errorMessage)
    , m_affectedRows(-1)
{
}

TpDatabaseResult::TpDatabaseResult(const TpDatabaseResult& other)
    : m_errorCode(other.m_errorCode)
    , m_errorMessage(other.m_errorMessage)
    , m_data(other.m_data)
    , m_affectedRows(other.m_affectedRows)
{
}

TpDatabaseResult& TpDatabaseResult::operator=(const TpDatabaseResult& other)
{
    if (this != &other) {
        m_errorCode = other.m_errorCode;
        m_errorMessage = other.m_errorMessage;
        m_data = other.m_data;
        m_affectedRows = other.m_affectedRows;
    }
    return *this;
}

TpDatabaseResult::~TpDatabaseResult()
{
}

bool TpDatabaseResult::isSuccess() const
{
    return m_errorCode == TpDatabaseErrorCode::Success;
}

TpDatabaseErrorCode TpDatabaseResult::errorCode() const
{
    return m_errorCode;
}

QString TpDatabaseResult::errorMessage() const
{
    return m_errorMessage;
}

QVariant TpDatabaseResult::data() const
{
    return m_data;
}

int TpDatabaseResult::affectedRows() const
{
    return m_affectedRows;
}

TpDatabaseResult TpDatabaseResult::success(const QVariant& data)
{
    return TpDatabaseResult(data);
}

TpDatabaseResult TpDatabaseResult::error(TpDatabaseErrorCode errorCode, const QString& errorMessage)
{
    return TpDatabaseResult(errorCode, errorMessage);
}

TpDatabaseResult TpDatabaseResult::fromSqlError(const QSqlError& sqlError)
{
    if (!sqlError.isValid()) {
        return success();
    }
    
    TpDatabaseErrorCode errorCode = TpDatabaseErrorCode::UnknownError;
    QString errorText = sqlError.text().toLower();
    
    // 根据SQL错误类型映射到我们的错误码
    if (errorText.contains("connection") || errorText.contains("connect")) {
        errorCode = TpDatabaseErrorCode::ConnectionFailed;
    } else if (errorText.contains("table") && errorText.contains("exist")) {
        errorCode = TpDatabaseErrorCode::TableNotFound;
    } else if (errorText.contains("duplicate") || errorText.contains("unique")) {
        errorCode = TpDatabaseErrorCode::DuplicateKey;
    } else if (errorText.contains("foreign key") || errorText.contains("constraint")) {
        errorCode = TpDatabaseErrorCode::ForeignKeyConstraint;
    } else if (errorText.contains("data too long") || errorText.contains("too long")) {
        errorCode = TpDatabaseErrorCode::DataTooLong;
    } else if (errorText.contains("access denied") || errorText.contains("permission")) {
        errorCode = TpDatabaseErrorCode::PermissionDenied;
    } else if (errorText.contains("timeout")) {
        errorCode = TpDatabaseErrorCode::TimeoutError;
    } else {
        errorCode = TpDatabaseErrorCode::QueryFailed;
    }
    
    QString message = QString("SQL Error [%1]: %2")
                     .arg(sqlError.nativeErrorCode())
                     .arg(sqlError.text());
    
    return TpDatabaseResult(errorCode, message);
}

QString TpDatabaseResult::errorCodeToString(TpDatabaseErrorCode errorCode)
{
    switch (errorCode) {
    case TpDatabaseErrorCode::Success:
        return "Success";
    case TpDatabaseErrorCode::ConnectionFailed:
        return "Connection Failed";
    case TpDatabaseErrorCode::QueryFailed:
        return "Query Failed";
    case TpDatabaseErrorCode::TransactionFailed:
        return "Transaction Failed";
    case TpDatabaseErrorCode::ConfigError:
        return "Configuration Error";
    case TpDatabaseErrorCode::ValidationError:
        return "Validation Error";
    case TpDatabaseErrorCode::PermissionDenied:
        return "Permission Denied";
    case TpDatabaseErrorCode::TableNotFound:
        return "Table Not Found";
    case TpDatabaseErrorCode::DuplicateKey:
        return "Duplicate Key";
    case TpDatabaseErrorCode::ForeignKeyConstraint:
        return "Foreign Key Constraint";
    case TpDatabaseErrorCode::DataTooLong:
        return "Data Too Long";
    case TpDatabaseErrorCode::InvalidParameter:
        return "Invalid Parameter";
    case TpDatabaseErrorCode::TimeoutError:
        return "Timeout Error";
    case TpDatabaseErrorCode::UnknownError:
    default:
        return "Unknown Error";
    }
}
