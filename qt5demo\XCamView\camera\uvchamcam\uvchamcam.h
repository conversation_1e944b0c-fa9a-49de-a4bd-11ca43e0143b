﻿#ifndef UVCHAMCAM_H
#define UVCHAMCAM_H

#include "../camera.h"
#include "uvchamloader.h"
#include <QMutex>
#include <QTimer>

class UvchamCam : public Camera
{
    Q_OBJECT

public:
    explicit UvchamCam(QObject* parent = nullptr);
    ~UvchamCam();

    virtual bool InitLoader();

    virtual QList<CameraSt> Enum();

    virtual bool Open(const CameraSt& info);
    virtual void Close();
    virtual void Stop();
    virtual HRESULT Start();
    virtual bool IsOpened();
    virtual bool IsRunning();

    virtual HRESULT PutPara(unsigned nId, int val);
    virtual HRESULT GetPara(unsigned nId, int* pVal);
    virtual HRESULT GetParaRange(unsigned nId, int* pMin, int* pMax, int* pDef);

    virtual int  GetImageWidth();
    virtual int  GetImageHeight();
    virtual void GetImage(void* pFrameBuffer);

private slots:
    void OnTimerOut();

private:
    static void __stdcall UVCHAM_CALLBACK(unsigned nEvent, void* pCallbackCtx);

    void PullImagePrivate();

    QMutex        m_mutex;
    QTimer        m_timer;
    UvchamLoader  m_uvchamLoader;
    HUvcham       m_hUvcham;
    bool          m_bOpened;
    bool          m_bRunning;
    uchar*        m_pData;
    int           m_width;
    int           m_height;
    int           m_preExpTime;
    int           m_preExpGain;
    int           m_preWBRed;
    int           m_preWBGreen;
    int           m_preWBBlue;
};

#endif
