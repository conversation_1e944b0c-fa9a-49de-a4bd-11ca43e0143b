#ifndef IMAGEVIEW_H
#define IMAGEVIEW_H

#include "displayview.h"

class ImageWidget : public DisplayWidget
{
    Q_OBJECT
public:
    explicit ImageWidget(const QImage* pImage, QWidget* parent = nullptr);

    void SetImage(const QImage* pImage);
};

class ImageView : public DisplayView
{
	Q_OBJECT
public:
    explicit ImageView(const QImage* pImage = nullptr, QWidget* parent = nullptr);
    ~ImageView();

    void SetImage(const QImage* pImage);

    static int s_cnt;
};

#endif
