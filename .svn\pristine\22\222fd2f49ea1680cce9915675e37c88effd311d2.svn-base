#include "tptexteditdlg.h"
#include <QGroupBox>
#include <QHBoxLayout>
#include <QPushButton>

TpTextEditDialog::TpTextEditDialog(QString content, QWidget* parent)
    : QDialog(parent)
{
    setWindowTitle(g_i18n->value("ids_text"));
    setWindowFlags(windowFlags() & ~Qt::WindowContextHelpButtonHint & ~Qt::WindowMaximizeButtonHint);
    QGroupBox* mainBox = new QGroupBox;
    m_textBox = new QTextEdit;
    m_textBox->setMaximumHeight(80);
    m_textBox->setMinimumWidth(120);
    m_textBox->setLineWrapMode(QTextEdit::NoWrap);
    QHBoxLayout* btnLyt = new QHBoxLayout;
    QPushButton* btnOk = new QPushButton(g_i18n->value("ids_ok"));
    QPushButton* btnCancel = new QPushButton(g_i18n->value("ids_cancel"));
    btnCancel->setAutoDefault(false);
    connect(btnOk,      &QPushButton::released, this, &TpTextEditDialog::onAccept);
    connect(btnCancel,  &QPushButton::released, this, &TpTextEditDialog::reject);
    btnLyt->addStretch(1);
    btnLyt->addWidget(btnCancel);
    btnLyt->addWidget(btnOk);
    QVBoxLayout* vlyt = new QVBoxLayout;
    vlyt->addWidget(m_textBox);
    mainBox->setLayout(vlyt);
    QVBoxLayout* vlyt1 = new QVBoxLayout;
    vlyt1->addWidget(mainBox);
    vlyt1->addLayout(btnLyt);
    setLayout(vlyt1);
    setFixedSize(sizeHint());
    setTextString(content);
    mainBox->setFocus();
}

QString TpTextEditDialog::getTextString()
{
    return m_textBox ? m_textBox->toPlainText() : QString();
}

void TpTextEditDialog::setTextString(const QString& string)
{
    if (m_textBox)
        m_textBox->setText(string);
}

void TpTextEditDialog::onAccept()
{
    m_textBox->toPlainText().isEmpty() ? reject() : accept();
}
