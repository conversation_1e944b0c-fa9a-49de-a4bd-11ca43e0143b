#include "mainwindow.h"
#include "panel/centralpanel/tptabwidget.h"
#include "panel/centralpanel/displayview/previewview.h"
#include "panel/centralpanel/displayview/imageview.h"
#include "panel/centralpanel/monitorview/monitorview.h"
#include "panel/measure/tpcalibrationdlg.h"
#include "panel/measure/tptexteditdlg.h"
#include "panel/leftpanel.h"
#include "panel/monitorpanel/monitordialog.h"
#include "module/TpSaveLoader/tpsaveloader.h"
#include "measure/tpmeasuredata.h"
#include "measure/tpmeasuremanager.h"
#include <QMenuBar>
#include <QMessageBox>
#include <QLineEdit>
#include <QVBoxLayout>
#include <QKeyEvent>
#include <QCursor>

#ifdef _WIN32
#include <Dbt.h>
#endif

MainWindow* g_pMainWindow = nullptr;

MainWindow::MainWindow(QWidget *parent)
    : QMainWindow(parent)
{
    InitMeasureData();
    g_saveLoader.LoadCalibration(&g_caliList);
    g_caliIndex = g_saveLoader.LoadCurCaliIndex();

    QMenuBar *menuBar = this->menuBar();

    m_pMenuFile = menuBar->addMenu(g_i18n->value("ids_file"));
    m_pActionOpen = m_pMenuFile->addAction(QIcon(":/images/open.png"), g_i18n->value("ids_open_image"));
    m_pActionSave = m_pMenuFile->addAction(QIcon(":/images/save.png"), g_i18n->value("ids_save"));
    m_pActionQuickSave = m_pMenuFile->addAction(QIcon(":/images/qsave.png"), g_i18n->value("ids_quicksave"));
    m_pMenuFile->addSeparator();
    m_pActionPreference = m_pMenuFile->addAction(g_i18n->value("ids_preferences"));

    m_pMenuMeasure = menuBar->addMenu(g_i18n->value("ids_measurement"));
    m_pActionSelect = m_pMenuMeasure->addAction(QIcon(":/images/measure/select.svg"), g_i18n->value("ids_obj_select"));
    m_pMenuMeasure->addSeparator();
    m_pActionPoint = m_pMenuMeasure->addAction(QIcon(":/images/measure/point.svg"), g_i18n->value("ids_point"));
    m_pActionArrow = m_pMenuMeasure->addAction(QIcon(":/images/measure/arrow.svg"), g_i18n->value("ids_arrow"));

    QMenu* pAngleMenu = m_pMenuMeasure->addMenu(QIcon(":/images/measure/angle.svg"), g_i18n->value("ids_angle"));
    m_pActionAngle3pt = pAngleMenu->addAction(QIcon(":/images/measure/angle1.svg"), g_i18n->value("ids_angle3p"));
    m_pActionAngle4pt = pAngleMenu->addAction(QIcon(":/images/measure/angle2.svg"), g_i18n->value("ids_angle4p"));
    m_pToolBtnAngle = new QToolButton(this);
    m_pToolBtnAngle->setPopupMode(QToolButton::MenuButtonPopup);
    m_pToolBtnAngle->addAction(m_pActionAngle3pt);
    m_pToolBtnAngle->addAction(m_pActionAngle4pt);
    m_pToolBtnAngle->setDefaultAction(m_pActionAngle3pt);
    connect(m_pToolBtnAngle, &QToolButton::triggered, this,
            [=](QAction* action){
                m_pToolBtnAngle->setDefaultAction(action);
            });

    QMenu* pLineMenu = m_pMenuMeasure->addMenu(QIcon(":/images/measure/arbline.svg"), g_i18n->value("ids_line"));
    m_pActionArbline = pLineMenu->addAction(QIcon(":/images/measure/arbline.svg"), g_i18n->value("ids_arbline"));
    m_pActionHLine = pLineMenu->addAction(QIcon(":/images/measure/hline.svg"), g_i18n->value("ids_hline"));
    m_pActionVLine = pLineMenu->addAction(QIcon(":/images/measure/vline.svg"), g_i18n->value("ids_vline"));
    m_pToolBtnLine = new QToolButton(this);
    m_pToolBtnLine->setPopupMode(QToolButton::MenuButtonPopup);
    m_pToolBtnLine->addAction(m_pActionArbline);
    m_pToolBtnLine->addAction(m_pActionHLine);
    m_pToolBtnLine->addAction(m_pActionVLine);
    m_pToolBtnLine->setDefaultAction(m_pActionArbline);
    connect(m_pToolBtnLine, &QToolButton::triggered, this,
            [=](QAction* action){
                m_pToolBtnLine->setDefaultAction(action);
            });

    QMenu* pParallelMenu = m_pMenuMeasure->addMenu(QIcon(":/images/measure/parallel.svg"), g_i18n->value("ids_parallel"));
    m_pActionParallel4pt = pParallelMenu->addAction(QIcon(":/images/measure/parallel.svg"), g_i18n->value("ids_parallel1"));
    m_pActionParallel8pt = pParallelMenu->addAction(QIcon(":/images/measure/twoParallel.svg"), g_i18n->value("ids_parallel2"));
    m_pToolBtnParallel = new QToolButton(this);
    m_pToolBtnParallel->setPopupMode(QToolButton::MenuButtonPopup);
    m_pToolBtnParallel->addAction(m_pActionParallel4pt);
    m_pToolBtnParallel->addAction(m_pActionParallel8pt);
    m_pToolBtnParallel->setDefaultAction(m_pActionParallel4pt);
    connect(m_pToolBtnParallel, &QToolButton::triggered, this,
            [=](QAction* action){
                m_pToolBtnParallel->setDefaultAction(action);
            });

    QMenu* pVerticalMenu = m_pMenuMeasure->addMenu(QIcon(":/images/measure/vtline.svg"), g_i18n->value("ids_vertical"));
    m_pActionVertical3pt = pVerticalMenu->addAction(QIcon(":/images/measure/vtline2.svg"), g_i18n->value("ids_vertical3pt"));
    m_pActionVertical4pt = pVerticalMenu->addAction(QIcon(":/images/measure/vtline1.svg"), g_i18n->value("ids_vertical4pt"));
    m_pToolBtnVertical = new QToolButton(this);
    m_pToolBtnVertical->setPopupMode(QToolButton::MenuButtonPopup);
    m_pToolBtnVertical->addAction(m_pActionVertical3pt);
    m_pToolBtnVertical->addAction(m_pActionVertical4pt);
    m_pToolBtnVertical->setDefaultAction(m_pActionVertical3pt);
    connect(m_pToolBtnVertical, &QToolButton::triggered, this,
            [=](QAction* action){
                m_pToolBtnVertical->setDefaultAction(action);
            });

    m_pActionRectangle = m_pMenuMeasure->addAction(QIcon(":/images/measure/rectangle.svg"), g_i18n->value("ids_rectangle"));
    m_pActionEllipse = m_pMenuMeasure->addAction(QIcon(":/images/measure/ellipse.svg"), g_i18n->value("ids_ellipse"));

    QMenu* pCircleMenu = m_pMenuMeasure->addMenu(QIcon(":/images/measure/circle.svg"), g_i18n->value("ids_circle"));
    m_pActionCircleCR = pCircleMenu->addAction(QIcon(":/images/measure/circle1.svg"), g_i18n->value("ids_circleCR"));
    m_pActionCircle2pt = pCircleMenu->addAction(QIcon(":/images/measure/circle2.svg"), g_i18n->value("ids_circle2pt"));
    m_pActionCircle3pt = pCircleMenu->addAction(QIcon(":/images/measure/circle3.svg"), g_i18n->value("ids_circle3pt"));
    m_pToolBtnCircle = new QToolButton(this);
    m_pToolBtnCircle->setPopupMode(QToolButton::MenuButtonPopup);
    m_pToolBtnCircle->addAction(m_pActionCircleCR);
    m_pToolBtnCircle->addAction(m_pActionCircle2pt);
    m_pToolBtnCircle->addAction(m_pActionCircle3pt);
    m_pToolBtnCircle->setDefaultAction(m_pActionCircleCR);
    connect(m_pToolBtnCircle, &QToolButton::triggered, this,
            [=](QAction* action){
                m_pToolBtnCircle->setDefaultAction(action);
            });

    QMenu* pTwoCirclesMenu = m_pMenuMeasure->addMenu(QIcon(":/images/measure/twoCircles.svg"), g_i18n->value("ids_twocircles"));
    m_pActionTwoCirclesCR = pTwoCirclesMenu->addAction(QIcon(":/images/measure/twoCircles1.svg"), g_i18n->value("ids_twocirclesCR"));
    m_pActionTwoCircles3pt = pTwoCirclesMenu->addAction(QIcon(":/images/measure/twoCircles2.svg"), g_i18n->value("ids_twocircles3pt"));
    m_pToolBtnTwoCircles = new QToolButton(this);
    m_pToolBtnTwoCircles->setPopupMode(QToolButton::MenuButtonPopup);
    m_pToolBtnTwoCircles->addAction(m_pActionTwoCirclesCR);
    m_pToolBtnTwoCircles->addAction(m_pActionTwoCircles3pt);
    m_pToolBtnTwoCircles->setDefaultAction(m_pActionTwoCirclesCR);
    connect(m_pToolBtnTwoCircles, &QToolButton::triggered, this,
            [=](QAction* action){
                m_pToolBtnTwoCircles->setDefaultAction(action);
            });

    m_pActionAnnulus = m_pMenuMeasure->addAction(QIcon(":/images/measure/annulus.svg"), g_i18n->value("ids_annulus"));
    m_pActionArc = m_pMenuMeasure->addAction(QIcon(":/images/measure/arc.svg"), g_i18n->value("ids_arc"));
    m_pActionCurve = m_pMenuMeasure->addAction(QIcon(":/images/measure/curve.svg"), g_i18n->value("ids_curve"));
    m_pActionPolygon = m_pMenuMeasure->addAction(QIcon(":/images/measure/polygon.svg"), g_i18n->value("ids_polygon"));
    m_pActionText = m_pMenuMeasure->addAction(QIcon(":/images/measure/text.svg"), g_i18n->value("ids_text"));
    m_pActionScaleBar = m_pMenuMeasure->addAction(QIcon(":/images/measure/scalebar.svg"), g_i18n->value("ids_scalebar"));
    m_pMenuMeasure->addSeparator();
    m_pActionDeleteMeasure = m_pMenuMeasure->addAction(QIcon(":/images/measure/delete.svg"), g_i18n->value("ids_delete"));
    m_pActionCalibration = m_pMenuMeasure->addAction(QIcon(":/images/measure/calibration.svg"), g_i18n->value("ids_calibration"));

    m_pMenuTeaching = menuBar->addMenu(g_i18n->value("ids_teaching"));
    m_pActionSameScreen = m_pMenuTeaching->addAction(g_i18n->value("ids_same_screen"));
    m_pActionWhiteboard = m_pMenuTeaching->addAction(g_i18n->value("ids_electronic_whiteboard"));
    m_pActionBlackScreen = m_pMenuTeaching->addAction(g_i18n->value("ids_black_screen_silence"));

    m_pMenuHelp = menuBar->addMenu(g_i18n->value("ids_help"));
    m_pActionAbout = m_pMenuHelp->addAction(g_i18n->value("ids_about"));

    m_pZoomScaleCB = new QComboBox();
    m_pZoomScaleCB->setFixedWidth(g_pFontMetrics->averageCharWidth() * 10);
    m_pZoomScaleCB->installEventFilter(this);
    m_pZoomScaleCB->setEditable(true);
    m_pZoomScaleCB->setToolTip(g_i18n->value("ids_zoom"));

    m_pMagCB = new QComboBox();
    m_pMagCB->setFixedWidth(g_pFontMetrics->averageCharWidth() * 10);
    m_pMagCB->setToolTip(g_i18n->value("ids_beilv"));
    for(Calibration &cali : g_caliList)
        m_pMagCB->addItem(cali.magnification);
    m_pMagCB->setCurrentIndex(g_caliIndex);

    m_pUnitCB = new QComboBox();
    m_pUnitCB->setToolTip(g_i18n->value("ids_unit"));
    m_pUnitCB->addItem(g_i18n->value("ids_pixel"));
    m_pUnitCB->addItem(g_i18n->value("ids_nm") + "(nm)");
    m_pUnitCB->addItem(g_i18n->value("ids_um") + "(" + g_i18n->value("ids_abbrum") + ")");
    m_pUnitCB->addItem(g_i18n->value("ids_mm") + "(mm)");
    m_pUnitCB->addItem(g_i18n->value("ids_cm") + "(cm)");
    m_pUnitCB->addItem(g_i18n->value("ids_m") + "(m)");
    m_pUnitCB->addItem(g_i18n->value("ids_in") + "(in)");
    m_pUnitCB->setCurrentIndex(g_caliList[g_caliIndex].unit);

    m_pToolBar = this->addToolBar("toolBar");
    m_pToolBar->addAction(m_pActionOpen);
    m_pToolBar->addAction(m_pActionSave);
    m_pToolBar->addAction(m_pActionQuickSave);
    m_pToolBar->addWidget(m_pZoomScaleCB);
    m_pToolBar->addSeparator();
    m_pToolBar->addWidget(m_pMagCB);
    m_pToolBar->addWidget(m_pUnitCB);
    m_pToolBar->addAction(m_pActionSelect);
    m_pToolBar->addSeparator();
    m_pToolBar->addAction(m_pActionPoint);
    m_pToolBar->addAction(m_pActionArrow);
    m_pToolBar->addWidget(m_pToolBtnAngle);
    m_pToolBar->addWidget(m_pToolBtnLine);
    m_pToolBar->addWidget(m_pToolBtnParallel);
    m_pToolBar->addWidget(m_pToolBtnVertical);
    m_pToolBar->addAction(m_pActionRectangle);
    m_pToolBar->addAction(m_pActionEllipse);
    m_pToolBar->addWidget(m_pToolBtnCircle);
    m_pToolBar->addWidget(m_pToolBtnTwoCircles);
    m_pToolBar->addAction(m_pActionAnnulus);
    m_pToolBar->addAction(m_pActionArc);
    m_pToolBar->addAction(m_pActionCurve);
    m_pToolBar->addAction(m_pActionPolygon);
    m_pToolBar->addAction(m_pActionText);
    m_pToolBar->addAction(m_pActionScaleBar);
    m_pToolBar->addSeparator();
    m_pToolBar->addAction(m_pActionDeleteMeasure);
    m_pToolBar->addAction(m_pActionCalibration);

    m_pLeftPanel = new LeftPanel(this);
    addDockWidget(Qt::LeftDockWidgetArea, m_pLeftPanel);

    m_pTabWidget = new TpTabWidget(this);
    m_pTabWidget->addTab(new MonitorView(this), "1~9");

    QVBoxLayout* mainLayout = new QVBoxLayout();
    mainLayout->setSpacing(0);
    mainLayout->setContentsMargins(0, 0, 0, 0);
    mainLayout->addWidget(m_pTabWidget);

    QWidget* mainwidget = new QWidget();
    mainwidget->setLayout(mainLayout);
    setCentralWidget(mainwidget);

    adjustSize();

    m_pZoomScaleCB->setEnabled(false);
    InitZoomComboBox();

    m_pActionGpMeasures = new QActionGroup(this);
    m_pActionGpMeasures->addAction(m_pActionSelect);
    m_pActionGpMeasures->addAction(m_pActionPoint);
    m_pActionGpMeasures->addAction(m_pActionAngle3pt);
    m_pActionGpMeasures->addAction(m_pActionAngle4pt);
    m_pActionGpMeasures->addAction(m_pActionArrow);
    m_pActionGpMeasures->addAction(m_pActionArbline);
    m_pActionGpMeasures->addAction(m_pActionHLine);
    m_pActionGpMeasures->addAction(m_pActionVLine);
    m_pActionGpMeasures->addAction(m_pActionParallel4pt);
    m_pActionGpMeasures->addAction(m_pActionParallel8pt);
    m_pActionGpMeasures->addAction(m_pActionVertical3pt);
    m_pActionGpMeasures->addAction(m_pActionVertical4pt);
    m_pActionGpMeasures->addAction(m_pActionArc);
    m_pActionGpMeasures->addAction(m_pActionCurve);
    m_pActionGpMeasures->addAction(m_pActionRectangle);
    m_pActionGpMeasures->addAction(m_pActionEllipse);
    m_pActionGpMeasures->addAction(m_pActionCircleCR);
    m_pActionGpMeasures->addAction(m_pActionCircle2pt);
    m_pActionGpMeasures->addAction(m_pActionCircle3pt);
    m_pActionGpMeasures->addAction(m_pActionTwoCirclesCR);
    m_pActionGpMeasures->addAction(m_pActionTwoCircles3pt);
    m_pActionGpMeasures->addAction(m_pActionAnnulus);
    m_pActionGpMeasures->addAction(m_pActionPolygon);
    m_pActionGpMeasures->addAction(m_pActionText);

    connect(g_pCameraManager, SIGNAL(CamEvent(int)), this, SLOT(OnCamEvent(int)));
    connect(g_pCameraManager, SIGNAL(Notify(int)), this, SLOT(OnNotify(int)));
    connect(m_pTabWidget, SIGNAL(tabCloseRequested(int)), this, SLOT(OnTabCloseRequested(int)));
    connect(m_pZoomScaleCB, SIGNAL(activated(int)), this, SLOT(OnZoomScaleActived(int)));
    connect(m_pMagCB, SIGNAL(activated(int)), this, SLOT(OnMagnificationActived(int)));
    connect(m_pUnitCB, SIGNAL(activated(int)), this, SLOT(OnUnitActived(int)));
    connect(m_pActionGpMeasures, SIGNAL(triggered(QAction*)), this, SLOT(OnMeasureTriggered(QAction*)));
    connect(m_pActionCalibration, SIGNAL(triggered()), this, SLOT(OnCalibrationClicked()));
}

MainWindow::~MainWindow()
{
}

void MainWindow::Notify(int notify)
{
    if(NOTIFY_CAM_OPEN == notify)
    {
        m_pTabWidget->insertTab(0, new PreviewView(this), g_pCameraManager->GetCamInfo().name);
        m_pTabWidget->setCurrentIndex(0);
        m_pZoomScaleCB->setEnabled(true);
        m_pZoomScaleCB->setCurrentIndex(g_zoomIndex);
    }
    else if(NOTIFY_CAM_CLOSE == notify)
    {
        m_pZoomScaleCB->setEnabled(false);
        m_pZoomScaleCB->setCurrentIndex(-1);
    }

    m_pLeftPanel->Notify(notify);
    m_pTabWidget->Notify(notify);
}

void MainWindow::ShowMonitorDialog(const QString &title)
{
    MonitorDialog* pDlg = GetMonitorDlgFromList(title);
    if(pDlg)
    {
        if(pDlg->isHidden())
            pDlg->show();
    }
    else
    {
        MonitorDialog *dlg = new MonitorDialog(this);
        m_monitorDlgList.append(dlg);
        dlg->setWindowTitle(title);
        dlg->show();
    }
}

void MainWindow::SetZoomScale(double scale)
{
    m_pZoomScaleCB->setCurrentText(QString::number(scale * 100, 'f', 1) + "%");
}

double MainWindow::GetZoomScale()
{
    QString str = m_pZoomScaleCB->currentText().trimmed();
    if (str.endsWith("%"))
        str = str.left(str.length() - 1);

    bool bOK = false;
    double zoom = str.toDouble(&bOK);
    if (bOK)
    {
        if (zoom < g_zoomWheel[0])
            zoom = g_zoomWheel[0];
        else if (zoom > g_zoomWheel[g_zoomWheelCnt - 1])
            zoom = g_zoomWheel[g_zoomWheelCnt - 1];
        return zoom / 100;
    }
    return -1;
}

void MainWindow::OnCamEvent(int event)
{
    if(CAM_EVENT_DISCONNECT == event)
    {
        g_pCameraManager->Close();
        QMessageBox::critical(nullptr, g_i18n->value("ids_message"), g_i18n->value("ids_cam_not_connect"));
    }
    else if(CAM_EVENT_ERROR == event)
    {
        g_pCameraManager->Close();
        QMessageBox::critical(nullptr, g_i18n->value("ids_message"), g_i18n->value("ids_unknow_error"));
    }
}

void MainWindow::OnNotify(int notify)
{
    Notify(notify);
}

void MainWindow::OnTabCloseRequested(int index)
{
    Log(Log_Info, "MainWindow::%s, index = %d, name = %s", __func__, index, m_pTabWidget->tabText(index).toUtf8().data());

    if(m_pTabWidget->tabText(index) == g_pCameraManager->GetCamInfo().name)
    {
        g_pCameraManager->Close();
        m_pTabWidget->removeTab(index);
    }
}

void MainWindow::OnZoomScaleActived(int index)
{
    g_zoomIndex = index;
    Notify(NOTIFY_ZOOM_CHANGED);
}

void MainWindow::OnMeasureTriggered(QAction *action)
{
    g_measureActType = m_pActionGpMeasures->actions().indexOf(action);
    Notify(NOTIFY_MEASURE_CHANGED);
}

void MainWindow::OnMagnificationActived(int index)
{
    g_caliIndex = index;
    m_pUnitCB->setCurrentIndex(g_caliList[g_caliIndex].unit);
    UpdateMeasure(g_caliList[g_caliIndex]);

    g_saveLoader.SaveCurCaliIndex(g_caliIndex);
}

void MainWindow::OnUnitActived(int index)
{
    if(m_pMagCB->currentText() == "NA")
        m_pUnitCB->setCurrentIndex(0);
    else
    {
        Calibration cali = g_caliList[g_caliIndex];
        cali.unit = index;
        UpdateMeasure(cali);
    }
}

void MainWindow::OnCalibrationClicked()
{
    if(!(0 == strcmp(m_pTabWidget->currentWidget()->metaObject()->className(), "PreviewView")
          || 0 == strcmp(m_pTabWidget->currentWidget()->metaObject()->className(), "ImageView")))
        return;

    int res = 0, width = 0, height = 0;
    g_pCameraManager->GetPara(CAM_PARA_RES, &res);
    g_pCameraManager->GetPara(CAM_PARA_WIDTH | res, &width);
    g_pCameraManager->GetPara(CAM_PARA_HEIGHT | res, &height);
    if(width != g_maxResWidth && height != g_maxResHeight)
    {
        QString str = QString(g_i18n->value("ids_videocalibratemax1")).arg(width).arg(height)
            .arg(g_maxResWidth).arg(g_maxResHeight);
        str = str.replace("\\r\\n", "\n");
        QMessageBox::critical(this, g_i18n->value("ids_message"), str);
        return;
    }

    if(fabs(g_zoomScale - 1.0) > DF_EP)
    {
        int ret = QMessageBox::question(this, g_i18n->value("ids_message"), QString(g_i18n->value("ids_videozoomfloat"))
                                            .arg(100 * g_zoomScale, 0, 'f', 1));
        if(QMessageBox::Yes != ret)
            return;
    }

    m_pCaliDlg = new TpCalibrationDlg(false, this);
    connect(m_pCaliDlg, &TpCalibrationDlg::calibrationComplete, this, &MainWindow::OnCalibrationComplete);
    connect(m_pCaliDlg, &TpCalibrationDlg::calibrationApply, this, &MainWindow::OnCalibrationApply);
    m_pCaliDlg->move(QCursor::pos());
    m_pCaliDlg->show();
    EnableCalibration(true);
}

void MainWindow::OnCalibrationComplete(const Calibration& calibration)
{
    if(!(0 == strcmp(m_pTabWidget->currentWidget()->metaObject()->className(), "PreviewView")
          || 0 == strcmp(m_pTabWidget->currentWidget()->metaObject()->className(), "ImageView")))
        return;

    EnableCalibration(false);
    g_caliList.append(calibration);
    m_pMagCB->addItem(calibration.magnification);
    m_pMagCB->setCurrentIndex(m_pMagCB->count() - 1);
    m_pUnitCB->setCurrentIndex(calibration.unit);
    UpdateMeasure(calibration);

    g_caliIndex = g_caliList.count() - 1;
    g_saveLoader.SaveCalibration(g_caliList);
    g_saveLoader.SaveCurCaliIndex(g_caliIndex);
}

void MainWindow::OnCalibrationApply(const double& resolution)
{
    Q_UNUSED(resolution);
}

void MainWindow::OnCalibrationLength(double length)
{
    m_pCaliDlg->onLengthChanged(length);
}

void MainWindow::OnTextEditDlg()
{
    if(0 == strcmp(m_pTabWidget->currentWidget()->metaObject()->className(), "PreviewView")
        || 0 == strcmp(m_pTabWidget->currentWidget()->metaObject()->className(), "ImageView"))
    {
        TpTextEditDialog dlg("", this);
        if(TpTextEditDialog::Accepted == dlg.exec())
            reinterpret_cast<DisplayView*>(m_pTabWidget->currentWidget())->GetMeasureManager()->SetTextDlgResult(dlg.getTextString(), true);
        else
            reinterpret_cast<DisplayView*>(m_pTabWidget->currentWidget())->GetMeasureManager()->SetTextDlgResult(dlg.getTextString(), false);
    }
}

void MainWindow::OnCapture(const QImage* pImage)
{
    m_pTabWidget->addTab(new ImageView(pImage, this), QString("%1*").arg(ImageView::s_cnt + 1));
}

bool MainWindow::nativeEvent(const QByteArray&, void* message, long*)
{
#ifdef _WIN32
    MSG* msg = reinterpret_cast<MSG*>(message);
    if (WM_DEVICECHANGE == msg->message && DBT_DEVNODES_CHANGED == msg->wParam)
        Notify(NOTIFY_DEV_CHANGED);
#endif
    return false;
}

void MainWindow::closeEvent(QCloseEvent *event)
{
    g_pCameraManager->Close();
    QMainWindow::closeEvent(event);
}

bool MainWindow::eventFilter(QObject* obj, QEvent* event)
{
    if (obj == m_pZoomScaleCB)
    {
        if (event->type() == QEvent::KeyPress)
        {
            QKeyEvent* ke = static_cast<QKeyEvent*>(event);
            if (ke->key() == Qt::Key_Enter || ke->key() == Qt::Key_Return)
            {
                const double zoom = GetZoomScale();
                if (zoom > 0)
                {
                    g_zoomScale = zoom;
                    g_zoomIndex = -1;
                    Notify(NOTIFY_ZOOM_CHANGED);
                    QString str = m_pZoomScaleCB->currentText().trimmed();
                    if(!str.endsWith("%"))
                        m_pZoomScaleCB->setCurrentText(str + "%");
                }
                return true;
            }
        }
        return false;
    }
    else
        return MainWindow::eventFilter(obj, event);
}

MonitorDialog *MainWindow::GetMonitorDlgFromList(const QString &title)
{
    for(int i = 0; i < m_monitorDlgList.count(); ++i)
    {
        if(m_monitorDlgList[i]->windowTitle() == title)
            return m_monitorDlgList[i];
    }
    return nullptr;
}

void MainWindow::InitZoomComboBox()
{
    m_pZoomScaleCB->clear();
    for (int i = 0; i < g_zoomcnt + 3; ++i)
    {
        if (i < g_zoomcnt)
            m_pZoomScaleCB->addItem(QString("%1%").arg(g_zoom[i]));
        else
        {
            switch (i - g_zoomcnt)
            {
            case FIT_TO_WIDTH: m_pZoomScaleCB->addItem(g_i18n->value("ids_fit_to_width")); break;
            case FIT_TO_HEIGHT: m_pZoomScaleCB->addItem(g_i18n->value("ids_fit_to_height")); break;
            case FIT_TO_WINDOW: m_pZoomScaleCB->addItem(g_i18n->value("ids_fit_to_window")); break;
            }
        }
    }
    m_pZoomScaleCB->setCurrentIndex(-1);
}

void MainWindow::EnableCalibration(bool bEnabled)
{
    if(0 == strcmp(m_pTabWidget->currentWidget()->metaObject()->className(), "PreviewView"))
        reinterpret_cast<PreviewView*>(m_pTabWidget->currentWidget())->EnableCalibration(bEnabled);
}

void MainWindow::UpdateMeasure(const Calibration& calibration)
{
    if(0 == strcmp(m_pTabWidget->currentWidget()->metaObject()->className(), "PreviewView"))
    {
        PreviewView* pView = reinterpret_cast<PreviewView*>(m_pTabWidget->currentWidget());
        pView->GetMeasureManager()->setResolution(calibration.resolution);
        pView->GetMeasureManager()->setUnit(static_cast<eUnit>(calibration.unit));
        pView->GetMeasureManager()->updateAll();
    }
}
