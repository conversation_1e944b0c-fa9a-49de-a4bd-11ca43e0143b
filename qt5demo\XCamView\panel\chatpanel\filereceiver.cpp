#include "filereceiver.h"
#include <QDataStream>
#include <QDir>
#include <QTimer>
#include <QFileInfo>
#include <QDebug>

FileReceiver::FileReceiver(QObject *parent) : QObject(parent) {}

FileReceiver::~FileReceiver()
{
    stopReceiving();
}

void FileReceiver::startReceiving(const QHostAddress &senderAddress,
                                 const QString &saveDirectory,
                                 quint16 port)
{
    // 清理之前的资源
    stopReceiving();

    m_savePath = saveDirectory;
    QDir saveDir(m_savePath);
    if (!saveDir.exists()) {
        if (!saveDir.mkpath(".")) {
            emit errorOccurred(tr("无法创建保存目录: %1").arg(m_savePath));
            return;
        }
    }

    m_socket = new QTcpSocket(this);
    connect(m_socket, &QTcpSocket::readyRead, this, &FileReceiver::readIncomingData);
    connect(m_socket, QOverload<QAbstractSocket::SocketError>::of(&QTcpSocket::error),
            this, &FileReceiver::handleSocketError);
    emit statusChanged(tr("正在连接到 %1:%2...").arg(senderAddress.toString()).arg(port));
    m_socket->connectToHost(senderAddress, port);

    m_timer.start();
}

void FileReceiver::stopReceiving()
{
    if (m_socket) {
        m_socket->disconnect();
        m_socket->abort();
        m_socket->deleteLater();
        m_socket = nullptr;
    }

    if (m_file) {
        if (m_file->isOpen()) m_file->close();
        m_file->deleteLater();
        m_file = nullptr;
    }

    m_inBlock.clear();
    m_bytesReceived = 0;
    m_totalBytes = 0;
    m_headerProcessed = false;
}

void FileReceiver::readIncomingData()
{
    // 首次读取，处理文件头
    if (!m_headerProcessed) {
        processFileHeader();

        // 如果仍然未处理完头部，等待更多数据
        if (!m_headerProcessed) return;
    }

    // 计算传输速度（移到外部以便在完成时使用）
    double elapsed = m_timer.elapsed() / 1000.0; // 转换为秒
    double speedMBs = elapsed > 0 ? (m_bytesReceived / (1024.0 * 1024.0)) / elapsed : 0;

    // 处理文件数据
    while (m_socket->bytesAvailable() > 0 && m_bytesReceived < m_totalBytes) {
        QByteArray chunk = m_socket->read(qMin(
            m_socket->bytesAvailable(),
            static_cast<qint64>(m_totalBytes - m_bytesReceived)
        ));

        int bytesRead = chunk.size();
        saveToFile(chunk);
        m_bytesReceived += bytesRead;

        // 重新计算速度
        elapsed = m_timer.elapsed() / 1000.0;
        speedMBs = elapsed > 0 ? (m_bytesReceived / (1024.0 * 1024.0)) / elapsed : 0;

        emit progressUpdated(m_bytesReceived, m_totalBytes, speedMBs);
    }

    // 检查是否完成 - 使用更严格的完成条件
    if (m_bytesReceived >= m_totalBytes) {
        // 确保文件完全写入磁盘
        m_file->flush();
        m_file->close();

        // 强制发送100%进度
        emit progressUpdated(m_totalBytes, m_totalBytes, speedMBs);

        // 延迟一点确保UI更新
        QTimer::singleShot(100, this, [this]() {
            emit statusChanged(tr("文件接收完成"));
            emit transferFinished(m_file->fileName());
        });

        qDebug() << "FileReceiver: 文件接收完成，总字节:" << m_totalBytes << "已接收:" << m_bytesReceived;
        stopReceiving();
        return; // 重要：完成后立即返回
    }

    // 如果接近完成，输出调试信息
    if (m_bytesReceived >= m_totalBytes * 0.99) {
        qDebug() << "FileReceiver: 接近完成，已接收:" << m_bytesReceived << "总大小:" << m_totalBytes << "进度:" << (m_bytesReceived * 100.0 / m_totalBytes) << "%";
    }
}

void FileReceiver::processFileHeader()
{
    QDataStream in(m_socket);
    in.setVersion(QDataStream::Qt_5_14);

    // 等待足够的数据处理头部
    if (m_socket->bytesAvailable() < static_cast<qint64>(sizeof(qint64) * 2)) {
        return;
    }

    // 读取总大小和文件名大小
    in >> m_totalBytes >> m_fileNameSize;

    // 等待文件名数据
    if (m_socket->bytesAvailable() < m_fileNameSize) {
        return;
    }

    // 读取文件名
    in >> m_fileName;

    // 准备文件保存
    QString fullPath = QDir(m_savePath).filePath(m_fileName);
    m_file = new QFile(fullPath);

    // 检查文件是否已存在（支持断点续传）
    bool resumeTransfer = false;
    if (m_file->exists()) {
        qint64 existingSize = m_file->size();
        if (existingSize > 0 && existingSize < m_totalBytes) {
            resumeTransfer = true;
            m_bytesReceived = existingSize;
            m_file->open(QIODevice::WriteOnly | QIODevice::Append);
            emit statusChanged(tr("继续接收文件: %1 (已存在 %2 MB)")
                               .arg(m_fileName)
                               .arg(existingSize / (1024.0 * 1024.0), 0, 'f', 2));
        }
    }

    if (!resumeTransfer) {
        if (!m_file->open(QIODevice::WriteOnly)) {
            emit errorOccurred(tr("无法创建文件: %1").arg(m_file->errorString()));
            stopReceiving();
            return;
        }
        emit statusChanged(tr("开始接收文件: %1").arg(m_fileName));
    }

    m_headerProcessed = true;
}

void FileReceiver::saveToFile(const QByteArray &data)
{
    if (!m_file->isOpen() || !m_file->isWritable()) {
        emit errorOccurred(tr("文件无法写入"));
        stopReceiving();
        return;
    }

    if (m_file->write(data) != data.size()) {
        emit errorOccurred(tr("写入文件失败: %1").arg(m_file->errorString()));
        stopReceiving();
    }
}

void FileReceiver::handleSocketError(QAbstractSocket::SocketError socketError)
{
    Q_UNUSED(socketError);
    emit errorOccurred(tr("网络错误: %1").arg(m_socket->errorString()));
    stopReceiving();
}
