@echo off
echo Setting up XCamView configuration files...
echo.

set BUILD_DIR=D:\work\XCamView\build-XCamView-Desktop_Qt_5_14_2_MSVC2017_64bit-Debug\debug
set SOURCE_CONFIG=D:\work\XCamView\XCamView\config\database.ini

echo Source config file: %SOURCE_CONFIG%
echo Target directory: %BUILD_DIR%
echo.

if not exist "%SOURCE_CONFIG%" (
    echo ERROR: Source config file not found!
    echo Please check: %SOURCE_CONFIG%
    pause
    exit /b 1
)

echo Creating config directory in build folder...
if not exist "%BUILD_DIR%\config" mkdir "%BUILD_DIR%\config"

echo Copying database.ini to build directory...
copy "%SOURCE_CONFIG%" "%BUILD_DIR%\config\database.ini"
copy "%SOURCE_CONFIG%" "%BUILD_DIR%\database.ini"

echo.
echo Configuration files copied successfully!
echo.
echo Contents of database.ini:
type "%SOURCE_CONFIG%"
echo.
echo.
echo You can now run XCamView.exe
pause
