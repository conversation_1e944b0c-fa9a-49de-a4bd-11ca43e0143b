{"artifacts": [{"path": "XCamView.exe"}, {"path": "XCamView.pdb"}], "backtrace": 4, "backtraceGraph": {"commands": ["add_executable", "_qt_internal_create_executable", "qt6_add_executable", "qt_add_executable", "install", "target_link_libraries", "set_target_properties", "include", "find_package", "find_dependency", "_qt_internal_find_qt_dependencies", "target_include_directories", "target_precompile_headers"], "files": ["E:/Qt/6.10.0/msvc2022_64/lib/cmake/Qt6Core/Qt6CoreMacros.cmake", "CMakeLists.txt", "E:/Qt/6.10.0/msvc2022_64/lib/cmake/Qt6Widgets/Qt6WidgetsTargets.cmake", "E:/Qt/6.10.0/msvc2022_64/lib/cmake/Qt6Widgets/Qt6WidgetsConfig.cmake", "build/Desktop_Qt_6_10_0_MSVC2022_64bit-Debug/.qtc/package-manager/maintenance_tool_provider.cmake", "E:/Qt/6.10.0/msvc2022_64/lib/cmake/Qt6Gui/Qt6GuiTargets.cmake", "E:/Qt/6.10.0/msvc2022_64/lib/cmake/Qt6Gui/Qt6GuiConfig.cmake", "E:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeFindDependencyMacro.cmake", "E:/Qt/6.10.0/msvc2022_64/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake", "E:/Qt/6.10.0/msvc2022_64/lib/cmake/Qt6Widgets/Qt6WidgetsDependencies.cmake", "E:/Qt/6.10.0/msvc2022_64/lib/cmake/Qt6Core/Qt6CoreTargets.cmake", "E:/Qt/6.10.0/msvc2022_64/lib/cmake/Qt6Core/Qt6CoreConfig.cmake", "E:/Qt/6.10.0/msvc2022_64/lib/cmake/Qt6EntryPointPrivate/Qt6EntryPointPrivateTargets.cmake", "E:/Qt/6.10.0/msvc2022_64/lib/cmake/Qt6EntryPointPrivate/Qt6EntryPointPrivateConfig.cmake", "E:/Qt/6.10.0/msvc2022_64/lib/cmake/Qt6Core/Qt6CoreDependencies.cmake"], "nodes": [{"file": 1}, {"command": 3, "file": 1, "line": 23, "parent": 0}, {"command": 2, "file": 0, "line": 940, "parent": 1}, {"command": 1, "file": 0, "line": 638, "parent": 2}, {"command": 0, "file": 0, "line": 689, "parent": 3}, {"command": 4, "file": 1, "line": 72, "parent": 0}, {"command": 5, "file": 1, "line": 47, "parent": 0}, {"command": 5, "file": 1, "line": 50, "parent": 0}, {"command": 8, "file": 1, "line": 13, "parent": 0}, {"command": 8, "file": 4, "line": 297, "parent": 8}, {"file": 3, "parent": 9}, {"command": 7, "file": 3, "line": 52, "parent": 10}, {"file": 2, "parent": 11}, {"command": 6, "file": 2, "line": 61, "parent": 12}, {"command": 7, "file": 3, "line": 40, "parent": 10}, {"file": 9, "parent": 14}, {"command": 10, "file": 9, "line": 45, "parent": 15}, {"command": 9, "file": 8, "line": 142, "parent": 16}, {"command": 8, "file": 7, "line": 76, "parent": 17}, {"command": 8, "file": 4, "line": 315, "parent": 18}, {"file": 6, "parent": 19}, {"command": 7, "file": 6, "line": 52, "parent": 20}, {"file": 5, "parent": 21}, {"command": 6, "file": 5, "line": 61, "parent": 22}, {"command": 5, "file": 0, "line": 639, "parent": 2}, {"command": 9, "file": 8, "line": 142, "parent": 16}, {"command": 8, "file": 7, "line": 76, "parent": 25}, {"command": 8, "file": 4, "line": 315, "parent": 26}, {"file": 11, "parent": 27}, {"command": 7, "file": 11, "line": 54, "parent": 28}, {"file": 10, "parent": 29}, {"command": 6, "file": 10, "line": 61, "parent": 30}, {"command": 7, "file": 11, "line": 42, "parent": 28}, {"file": 14, "parent": 32}, {"command": 10, "file": 14, "line": 46, "parent": 33}, {"command": 9, "file": 8, "line": 142, "parent": 34}, {"command": 8, "file": 7, "line": 76, "parent": 35}, {"command": 8, "file": 4, "line": 315, "parent": 36}, {"file": 13, "parent": 37}, {"command": 7, "file": 13, "line": 52, "parent": 38}, {"file": 12, "parent": 39}, {"command": 6, "file": 12, "line": 61, "parent": 40}, {"command": 6, "file": 12, "line": 83, "parent": 40}, {"command": 11, "file": 1, "line": 49, "parent": 0}, {"command": 12, "file": 1, "line": 55, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-DQT_QML_DEBUG /DWIN32 /D_WINDOWS /GR /EHsc /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd"}, {"backtrace": 24, "fragment": "-Zc:__cplusplus"}, {"backtrace": 24, "fragment": "-permissive-"}, {"backtrace": 24, "fragment": "-utf-8"}, {"fragment": "/YcD:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Debug/CMakeFiles/XCamView.dir/cmake_pch.hxx /FpD:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Debug/CMakeFiles/XCamView.dir/./cmake_pch.cxx.pch /FID:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Debug/CMakeFiles/XCamView.dir/cmake_pch.hxx"}], "defines": [{"backtrace": 24, "define": "QT_CORE_LIB"}, {"backtrace": 6, "define": "QT_GUI_LIB"}, {"backtrace": 6, "define": "QT_SQL_LIB"}, {"backtrace": 6, "define": "QT_WIDGETS_LIB"}, {"backtrace": 6, "define": "QT_XML_LIB"}, {"backtrace": 24, "define": "UNICODE"}, {"backtrace": 24, "define": "WIN32"}, {"backtrace": 24, "define": "WIN64"}, {"backtrace": 24, "define": "_ENABLE_EXTENDED_ALIGNED_STORAGE"}, {"backtrace": 24, "define": "_UNICODE"}, {"backtrace": 24, "define": "_WIN64"}], "includes": [{"backtrace": 0, "path": "D:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Debug/XCamView_autogen/include"}, {"backtrace": 43, "path": "D:/work/XCamView/XCamView/lib/include"}, {"backtrace": 24, "isSystem": true, "path": "E:/Qt/6.10.0/msvc2022_64/include/QtCore"}, {"backtrace": 24, "isSystem": true, "path": "E:/Qt/6.10.0/msvc2022_64/include"}, {"backtrace": 24, "isSystem": true, "path": "E:/Qt/6.10.0/msvc2022_64/mkspecs/win32-msvc"}, {"backtrace": 6, "isSystem": true, "path": "E:/Qt/6.10.0/msvc2022_64/include/QtWidgets"}, {"backtrace": 6, "isSystem": true, "path": "E:/Qt/6.10.0/msvc2022_64/include/QtGui"}, {"backtrace": 6, "isSystem": true, "path": "E:/Qt/6.10.0/msvc2022_64/include/QtXml"}, {"backtrace": 6, "isSystem": true, "path": "E:/Qt/6.10.0/msvc2022_64/include/QtSql"}], "language": "CXX", "languageStandard": {"backtraces": [24, 24], "standard": "17"}, "precompileHeaders": [{"backtrace": 44, "header": "D:/work/XCamView/XCamView/predef.h"}], "sourceIndexes": [0]}, {"compileCommandFragments": [{"fragment": "-DQT_QML_DEBUG /DWIN32 /D_WINDOWS /GR /EHsc /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd"}, {"backtrace": 24, "fragment": "-Zc:__cplusplus"}, {"backtrace": 24, "fragment": "-permissive-"}, {"backtrace": 24, "fragment": "-utf-8"}, {"fragment": "/YuD:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Debug/CMakeFiles/XCamView.dir/cmake_pch.hxx /FpD:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Debug/CMakeFiles/XCamView.dir/./cmake_pch.cxx.pch /FID:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Debug/CMakeFiles/XCamView.dir/cmake_pch.hxx"}], "defines": [{"backtrace": 24, "define": "QT_CORE_LIB"}, {"backtrace": 6, "define": "QT_GUI_LIB"}, {"backtrace": 6, "define": "QT_SQL_LIB"}, {"backtrace": 6, "define": "QT_WIDGETS_LIB"}, {"backtrace": 6, "define": "QT_XML_LIB"}, {"backtrace": 24, "define": "UNICODE"}, {"backtrace": 24, "define": "WIN32"}, {"backtrace": 24, "define": "WIN64"}, {"backtrace": 24, "define": "_ENABLE_EXTENDED_ALIGNED_STORAGE"}, {"backtrace": 24, "define": "_UNICODE"}, {"backtrace": 24, "define": "_WIN64"}], "includes": [{"backtrace": 0, "path": "D:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Debug/XCamView_autogen/include"}, {"backtrace": 43, "path": "D:/work/XCamView/XCamView/lib/include"}, {"backtrace": 24, "isSystem": true, "path": "E:/Qt/6.10.0/msvc2022_64/include/QtCore"}, {"backtrace": 24, "isSystem": true, "path": "E:/Qt/6.10.0/msvc2022_64/include"}, {"backtrace": 24, "isSystem": true, "path": "E:/Qt/6.10.0/msvc2022_64/mkspecs/win32-msvc"}, {"backtrace": 6, "isSystem": true, "path": "E:/Qt/6.10.0/msvc2022_64/include/QtWidgets"}, {"backtrace": 6, "isSystem": true, "path": "E:/Qt/6.10.0/msvc2022_64/include/QtGui"}, {"backtrace": 6, "isSystem": true, "path": "E:/Qt/6.10.0/msvc2022_64/include/QtXml"}, {"backtrace": 6, "isSystem": true, "path": "E:/Qt/6.10.0/msvc2022_64/include/QtSql"}], "language": "CXX", "languageStandard": {"backtraces": [24, 24], "standard": "17"}, "precompileHeaders": [{"backtrace": 44, "header": "D:/work/XCamView/XCamView/predef.h"}], "sourceIndexes": [1, 2, 3, 6, 8, 10, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65, 67, 72]}], "dependencies": [{"id": "XCamView_autogen_timestamp_deps::@6890427a1f51a3e7e1df"}, {"backtrace": 0, "id": "XCamView_autogen::@6890427a1f51a3e7e1df"}], "id": "XCamView::@6890427a1f51a3e7e1df", "install": {"destinations": [{"backtrace": 5, "path": "bin"}], "prefix": {"path": "C:/Program Files (x86)/XCamView"}}, "link": {"commandFragments": [{"fragment": "-DQT_QML_DEBUG /DWIN32 /D_WINDOWS /GR /EHsc /Zi /Ob0 /Od /RTC1 -MDd", "role": "flags"}, {"fragment": "/machine:x64 /debug /INCREMENTAL /subsystem:windows", "role": "flags"}, {"backtrace": 6, "fragment": "E:\\Qt\\6.10.0\\msvc2022_64\\lib\\Qt6Widgetsd.lib", "role": "libraries"}, {"backtrace": 6, "fragment": "E:\\Qt\\6.10.0\\msvc2022_64\\lib\\Qt6Xmld.lib", "role": "libraries"}, {"backtrace": 6, "fragment": "E:\\Qt\\6.10.0\\msvc2022_64\\lib\\Qt6Sqld.lib", "role": "libraries"}, {"backtrace": 7, "fragment": "D:\\work\\XCamView\\XCamView\\lib\\x64\\TpLogd.lib", "role": "libraries"}, {"backtrace": 7, "fragment": "D:\\work\\XCamView\\XCamView\\lib\\x64\\TpMeasureManagerd.lib", "role": "libraries"}, {"backtrace": 13, "fragment": "E:\\Qt\\6.10.0\\msvc2022_64\\lib\\Qt6Guid.lib", "role": "libraries"}, {"backtrace": 23, "fragment": "d3d11.lib", "role": "libraries"}, {"backtrace": 23, "fragment": "dxgi.lib", "role": "libraries"}, {"backtrace": 23, "fragment": "dxguid.lib", "role": "libraries"}, {"backtrace": 23, "fragment": "d3d12.lib", "role": "libraries"}, {"backtrace": 24, "fragment": "E:\\Qt\\6.10.0\\msvc2022_64\\lib\\Qt6Cored.lib", "role": "libraries"}, {"backtrace": 31, "fragment": "mpr.lib", "role": "libraries"}, {"backtrace": 31, "fragment": "userenv.lib", "role": "libraries"}, {"backtrace": 41, "fragment": "E:\\Qt\\6.10.0\\msvc2022_64\\lib\\Qt6EntryPointd.lib", "role": "libraries"}, {"backtrace": 42, "fragment": "shell32.lib", "role": "libraries"}, {"fragment": "kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib", "role": "libraries"}], "language": "CXX"}, "name": "XCamView", "nameOnDisk": "XCamView.exe", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2, 3, 6, 8, 10, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65, 67, 72]}, {"name": "<PERSON><PERSON>", "sourceIndexes": [4, 5, 7, 9, 11, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48, 50, 52, 54, 56, 58, 60, 62, 64, 66, 68, 69]}, {"name": "Resources", "sourceIndexes": [70]}, {"name": "", "sourceIndexes": [71]}, {"name": "Precompile Header File", "sourceIndexes": [73]}, {"name": "CMake Rules", "sourceIndexes": [74, 75]}], "sources": [{"backtrace": 0, "compileGroupIndex": 0, "path": "build/Desktop_Qt_6_10_0_MSVC2022_64bit-Debug/CMakeFiles/XCamView.dir/cmake_pch.cxx", "sourceGroupIndex": 0}, {"backtrace": 0, "compileGroupIndex": 1, "isGenerated": true, "path": "build/Desktop_Qt_6_10_0_MSVC2022_64bit-Debug/XCamView_autogen/mocs_compilation.cpp", "sourceGroupIndex": 0}, {"backtrace": 4, "compileGroupIndex": 1, "path": "main.cpp", "sourceGroupIndex": 0}, {"backtrace": 4, "compileGroupIndex": 1, "path": "mainwindow.cpp", "sourceGroupIndex": 0}, {"backtrace": 4, "path": "mainwindow.h", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "module/camera/uvchamcam/uvcham.h", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 1, "path": "module/camera/uvchamcam/uvchamcam.cpp", "sourceGroupIndex": 0}, {"backtrace": 4, "path": "module/camera/uvchamcam/uvchamcam.h", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 1, "path": "module/camera/uvchamcam/uvchamloader.cpp", "sourceGroupIndex": 0}, {"backtrace": 4, "path": "module/camera/uvchamcam/uvchamloader.h", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 1, "path": "module/camera/camera.cpp", "sourceGroupIndex": 0}, {"backtrace": 4, "path": "module/camera/camera.h", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "module/camera/cameradef.h", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 1, "path": "module/camera/cameramanager.cpp", "sourceGroupIndex": 0}, {"backtrace": 4, "path": "module/camera/cameramanager.h", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 1, "path": "module/auxrect/auxrect.cpp", "sourceGroupIndex": 0}, {"backtrace": 4, "path": "module/auxrect/auxrect.h", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 1, "path": "module/i18n/i18n.cpp", "sourceGroupIndex": 0}, {"backtrace": 4, "path": "module/i18n/i18n.h", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 1, "path": "module/TpSaveLoader/tpsaveloader.cpp", "sourceGroupIndex": 0}, {"backtrace": 4, "path": "module/TpSaveLoader/tpsaveloader.h", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 1, "path": "module/TpSignalConnect/TpSignalConnect.cpp", "sourceGroupIndex": 0}, {"backtrace": 4, "path": "module/TpSignalConnect/TpSignalConnect.h", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 1, "path": "module/tutils/tutils.cpp", "sourceGroupIndex": 0}, {"backtrace": 4, "path": "module/tutils/tutils.h", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 1, "path": "panel/camerapanel/cameralistwidget.cpp", "sourceGroupIndex": 0}, {"backtrace": 4, "path": "panel/camerapanel/cameralistwidget.h", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 1, "path": "panel/camerapanel/camerapanel.cpp", "sourceGroupIndex": 0}, {"backtrace": 4, "path": "panel/camerapanel/camerapanel.h", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 1, "path": "panel/camerapanel/coloradjustwidget.cpp", "sourceGroupIndex": 0}, {"backtrace": 4, "path": "panel/camerapanel/coloradjustwidget.h", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 1, "path": "panel/camerapanel/exposuregainwidget.cpp", "sourceGroupIndex": 0}, {"backtrace": 4, "path": "panel/camerapanel/exposuregainwidget.h", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 1, "path": "panel/camerapanel/powerfrequencywidget.cpp", "sourceGroupIndex": 0}, {"backtrace": 4, "path": "panel/camerapanel/powerfrequencywidget.h", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 1, "path": "panel/camerapanel/resolutionwidget.cpp", "sourceGroupIndex": 0}, {"backtrace": 4, "path": "panel/camerapanel/resolutionwidget.h", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 1, "path": "panel/camerapanel/whitebalancewidget.cpp", "sourceGroupIndex": 0}, {"backtrace": 4, "path": "panel/camerapanel/whitebalancewidget.h", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 1, "path": "panel/centralpanel/displayview/displayview.cpp", "sourceGroupIndex": 0}, {"backtrace": 4, "path": "panel/centralpanel/displayview/displayview.h", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 1, "path": "panel/centralpanel/displayview/imageview.cpp", "sourceGroupIndex": 0}, {"backtrace": 4, "path": "panel/centralpanel/displayview/imageview.h", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 1, "path": "panel/centralpanel/displayview/previewview.cpp", "sourceGroupIndex": 0}, {"backtrace": 4, "path": "panel/centralpanel/displayview/previewview.h", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 1, "path": "panel/centralpanel/monitorview/monitorview.cpp", "sourceGroupIndex": 0}, {"backtrace": 4, "path": "panel/centralpanel/monitorview/monitorview.h", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 1, "path": "panel/centralpanel/tptabwidget.cpp", "sourceGroupIndex": 0}, {"backtrace": 4, "path": "panel/centralpanel/tptabwidget.h", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 1, "path": "panel/centralpanel/view.cpp", "sourceGroupIndex": 0}, {"backtrace": 4, "path": "panel/centralpanel/view.h", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 1, "path": "panel/chatpanel/chatwidget.cpp", "sourceGroupIndex": 0}, {"backtrace": 4, "path": "panel/chatpanel/chatwidget.h", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 1, "path": "panel/login/logindialog.cpp", "sourceGroupIndex": 0}, {"backtrace": 4, "path": "panel/login/logindialog.h", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 1, "path": "panel/measure/tpcalibrationdlg.cpp", "sourceGroupIndex": 0}, {"backtrace": 4, "path": "panel/measure/tpcalibrationdlg.h", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 1, "path": "panel/measure/tptexteditdlg.cpp", "sourceGroupIndex": 0}, {"backtrace": 4, "path": "panel/measure/tptexteditdlg.h", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 1, "path": "panel/monitorpanel/monitordialog.cpp", "sourceGroupIndex": 0}, {"backtrace": 4, "path": "panel/monitorpanel/monitordialog.h", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 1, "path": "panel/monitorpanel/monitorwidget.cpp", "sourceGroupIndex": 0}, {"backtrace": 4, "path": "panel/monitorpanel/monitorwidget.h", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 1, "path": "panel/collapsewidget.cpp", "sourceGroupIndex": 0}, {"backtrace": 4, "path": "panel/collapsewidget.h", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 1, "path": "panel/leftpanel.cpp", "sourceGroupIndex": 0}, {"backtrace": 4, "path": "panel/leftpanel.h", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 1, "path": "global.cpp", "sourceGroupIndex": 0}, {"backtrace": 4, "path": "global.h", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "predef.h", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "res/XCamView.qrc", "sourceGroupIndex": 2}, {"backtrace": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_10_0_MSVC2022_64bit-Debug/XCamView_autogen/timestamp", "sourceGroupIndex": 3}, {"backtrace": 0, "compileGroupIndex": 1, "isGenerated": true, "path": "build/Desktop_Qt_6_10_0_MSVC2022_64bit-Debug/XCamView_autogen/PNK5WDWK6L/qrc_XCamView.cpp", "sourceGroupIndex": 0}, {"backtrace": 0, "path": "build/Desktop_Qt_6_10_0_MSVC2022_64bit-Debug/CMakeFiles/XCamView.dir/cmake_pch.hxx", "sourceGroupIndex": 4}, {"backtrace": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_10_0_MSVC2022_64bit-Debug/XCamView_autogen/timestamp.rule", "sourceGroupIndex": 5}, {"backtrace": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_10_0_MSVC2022_64bit-Debug/XCamView_autogen/PNK5WDWK6L/qrc_XCamView.cpp.rule", "sourceGroupIndex": 5}], "type": "EXECUTABLE"}