﻿#ifndef COLLAPSEWIDGET_H
#define COLLAPSEWIDGET_H

#include <QWidget>
#include <QFrame>
#include <QPushButton>
#include <QLabel>
#include <QApplication>

class TitleBar : public QPushButton
{
public:
    TitleBar(const QIcon& icon, const QString& text, QWidget* parent = nullptr);

protected:
    void paintEvent(QPaintEvent*) override;
    void enterEvent(QEvent*) override;

private:
    QIcon expandIcon_;
    QIcon collapseIcon_;
};

class CollapseWidget : public QFrame
{
    Q_OBJECT
public:
    explicit CollapseWidget(const QIcon& icon, const QString& title, QWidget* parent = nullptr);
    void setLayout(QLayout* lyt);
    void SetTitleHidden(bool bHidden);
    bool IsCollapsed();
    TitleBar* GetTitlebat() {return titleBar_;}
    QWidget* GetClientWidget() {return clientWidget_;}

private slots:
    void OnPushButtonClicked(bool bChecked);

signals:
    void isHide(bool bChecked);

private:
    TitleBar* titleBar_;
    QWidget* clientWidget_;
};

#endif
