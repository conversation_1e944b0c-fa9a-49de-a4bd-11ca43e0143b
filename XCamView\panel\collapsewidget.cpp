﻿#include <QPainter>
#include <QIcon>
#include <QVBoxLayout>
#include "collapsewidget.h"

TitleBar::TitleBar(const QIcon& icon, const QString& text, QWidget* parent)
    : QPushButton(icon, text, parent)
    , expandIcon_(":/images/expand.png")
    , collapseIcon_(":/images/collapse.png")
{
    setStyleSheet("QPushButton{color: rgb(0, 0, 0);}");
}

void TitleBar::paintEvent(QPaintEvent*)
{
    QPainter painter(this);
    QRect rc = this->rect();
    const int rcWidth = rc.width(), rcHeight = rc.height();
    painter.fillRect(rc, QColor(180, 180, 180));
    this->icon().paint(&painter, rcHeight / 10, rcHeight / 10, rcHeight * 4 / 5, rcHeight * 4 / 5);
    painter.drawText(QRect(rcHeight, 0, rcWidth - rcHeight, rcHeight), Qt::AlignLeft | Qt::AlignVCenter, this->text());
    if (isChecked())
        collapseIcon_.paint(&painter, rcWidth - rcHeight * 5 / 6, rcHeight / 6, rcHeight * 2 / 3, rcHeight * 2 / 3);
    else
        expandIcon_.paint(&painter, rcWidth - rcHeight * 5 / 6, rcHeight / 6, rcHeight * 2 / 3, rcHeight * 2 / 3);
}

void TitleBar::enterEvent(QEnterEvent*)
{
    setCursor(Qt::PointingHandCursor);
}

CollapseWidget::CollapseWidget(const QIcon& icon, const QString& title, QWidget* parent)
    : QFrame(parent)
{
    titleBar_ = new TitleBar(icon, title, parent);
    titleBar_->setCheckable(true);
    titleBar_->setChecked(true);
    connect(titleBar_, SIGNAL(clicked(bool)), this, SLOT(OnPushButtonClicked(bool)));
    clientWidget_ = new QWidget(parent);
    QVBoxLayout* mainLyt = new QVBoxLayout();
    mainLyt->setContentsMargins(0, 0, 0, 0);
    mainLyt->setSpacing(0);
    mainLyt->addWidget(titleBar_);
    mainLyt->addWidget(clientWidget_);
    QWidget::setLayout(mainLyt);
}

void CollapseWidget::setLayout(QLayout* lyt)
{
    clientWidget_->setLayout(lyt);
}

void CollapseWidget::SetTitleHidden(bool bHidden)
{
    if (titleBar_)
        titleBar_->setHidden(bHidden);
}

bool CollapseWidget::IsCollapsed()
{
    return clientWidget_->isHidden();
}

void CollapseWidget::OnPushButtonClicked(bool bChecked)
{
    clientWidget_->setVisible(bChecked);
    emit isHide(!bChecked);
}
