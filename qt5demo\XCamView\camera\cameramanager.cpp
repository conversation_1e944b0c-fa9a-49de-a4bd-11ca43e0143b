﻿#include "cameramanager.h"
#include "camera.h"
#include "uvchamcam/uvchamcam.h"

CameraManager* g_pCameraManager = nullptr;
QList<CameraSt> g_cameras;

CameraManager::CameraManager(QObject *parent)
    : QObject(parent), m_pCamera(nullptr)
{
    m_pCamera = new UvchamCam(this);
    connect(m_pCamera, SIGNAL(CamEvent(int)), this, SIGNAL(CamEvent(int)));
    connect(m_pCamera, SIGNAL(Notify(int)), this, SIGNAL(Notify(int)));

    g_pCameraManager = this;
}

bool CameraManager::InitLoader()
{
    return m_pCamera->InitLoader();
}

QList<CameraSt> CameraManager::Enum()
{
    return m_pCamera->Enum();
}

bool CameraManager::Open(const CameraSt &info)
{
    if(m_pCamera->Open(info))
    {
        emit Notify(NOTIFY_CAM_OPEN);
        return true;
    }
    return false;
}

const CameraSt &CameraManager::GetCamInfo() const
{
    return m_pCamera->GetCamInfo();
}

void CameraManager::Close()
{
    m_pCamera->Close();
    emit Notify(NOTIFY_CAM_CLOSE);
}

void CameraManager::Stop()
{
    m_pCamera->Stop();
}

bool CameraManager::Start()
{
    return SUCCEEDED(m_pCamera->Start());
}

bool CameraManager::IsOpened()
{
    return m_pCamera->IsOpened();
}

bool CameraManager::IsRunning()
{
    return m_pCamera->IsRunning();
}

HRESULT CameraManager::PutPara(unsigned nId, int val)
{
    return m_pCamera->PutPara(nId, val);
}

HRESULT CameraManager::GetPara(unsigned nId, int *pVal)
{
    return m_pCamera->GetPara(nId, pVal);
}

HRESULT CameraManager::GetParaRange(unsigned nId, int *pMin, int *pMax, int *pDef)
{
    return m_pCamera->GetParaRange(nId, pMin, pMax, pDef);
}

int CameraManager::GetImageWidth()
{
    return m_pCamera->GetImageWidth();
}

int CameraManager::GetImageHeight()
{
    return m_pCamera->GetImageHeight();
}

void CameraManager::GetImage(void *pFrameBuffer)
{
    m_pCamera->GetImage(pFrameBuffer);
}
