import xlrd
import xml.dom.minidom as minidom
import os

def write_xml(file, table):
	dom = minidom.getDOMImplementation().createDocument(None,'i18n',None)
	root = dom.documentElement
	for rowNum in range(1, table.nrows):
		rowVale = table.row_values(rowNum)
		element = dom.createElement('str')
		element.setAttribute('id', rowVale[1])
		if 'en.xml' == file:
			element.setAttribute('val', rowVale[2])
		elif 'hans.xml' == file:
			element.setAttribute('val', rowVale[3])
		root.appendChild(element)
	with open(file, 'w', encoding='utf-8') as f:
		dom.writexml(f, addindent='\t', newl='\n',encoding='utf-8')
		
data = xlrd.open_workbook('语言对照表.xlsx')
table = data.sheet_by_name('文字')
write_xml('en.xml', table)
write_xml('hans.xml', table)
