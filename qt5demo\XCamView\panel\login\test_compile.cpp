// 简单的编译测试
#include "tpdatabasehandler.h"
#include <QApplication>

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);
    
    TpdatabaseHandler* dbHandler = new TpdatabaseHandler();
    bool connected = dbHandler->connectToDatabase();
    
    if (connected) {
        qDebug() << "Database connection test passed!";
    } else {
        qDebug() << "Database connection test failed!";
    }
    
    delete dbHandler;
    return 0;
}
