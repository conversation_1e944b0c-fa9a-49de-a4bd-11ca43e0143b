#include <QPainter>
#include "auxrect.h"

#define MIN_W 25
#define MIN_H 25

AuxRect::AuxRect(const QString& label, const QColor& color, eAuxRectType type, QWidget* parent)
    : m_type(type), m_strLabel(label), m_iPosType(POS_NONE), m_bHide(false), m_rect(QRectF(100, 100, 1000, 1000))
    , m_pen(QPen(color, 0, Qt::SolidLine, Qt::FlatCap)), m_pParent(parent), m_fScale(1.0), m_iMaxW(0), m_iMaxH(0)
{

}

void AuxRect::paint(QPainter& painter, const QPointF& pos)
{
    if (!m_bHide)
    {
        painter.save();
        QRectF tmpRect = m_rect.translated(-pos);
        painter.setPen(m_pen);
        painter.drawRect(tmpRect);
        QFont font = painter.font();
        const double ifsize = 15.0 / m_fScale;
        font.setPointSizeF(ifsize);
        painter.setFont(font);
        painter.drawText(tmpRect.left(), tmpRect.top() - 5 / m_fScale, m_strLabel);
        drawCornerRect(painter, tmpRect);
        painter.restore();
    }
}

QColor AuxRect::reverseColor(QRgb color)
{
    QRgb tmp = 0xFFFFFFFF;
    int lt = QColor(color).lightness();
    return QColor(tmp - QColor(lt, lt, lt).rgba());
}

void AuxRect::drawCornerRect(QPainter& painter, const QRectF& rect)
{
    const double rect_w = 5 / m_fScale, rect_h = 5 / m_fScale;
    //    DisplayWidget* widget = qobject_cast<DisplayWidget*>(m_pParent);
    //    if (widget)
    //    {
    //        QRgb value = widget->GetImageValue(rect.left(), rect.top());
    //        painter.setPen(QPen(reverseColor(value), 0, Qt::SolidLine, Qt::FlatCap));
    //        painter.drawRect(QRectF(rect.left() - rect_w, rect.top() - rect_h, 2 * rect_w, 2 * rect_h));//left top

    //        value = widget->GetImageValue(rect.left(), rect.bottom());
    //        painter.setPen(QPen(reverseColor(value), 0, Qt::SolidLine, Qt::FlatCap));
    //        painter.drawRect(QRectF(rect.left() - rect_w, rect.bottom() - rect_h, 2 * rect_w, 2 * rect_h));//left bottom

    //        value = widget->GetImageValue(rect.right(), rect.top());
    //        painter.setPen(QPen(reverseColor(value), 0, Qt::SolidLine, Qt::FlatCap));
    //        painter.drawRect(QRectF(rect.right() - rect_w, rect.top() - rect_h, 2 * rect_w, 2 * rect_h));//right top

    //        value = widget->GetImageValue(rect.right(), rect.bottom());
    //        painter.setPen(QPen(reverseColor(value), 0, Qt::SolidLine, Qt::FlatCap));
    //        painter.drawRect(QRectF(rect.right() - rect_w, rect.bottom() - rect_h, 2 * rect_w, 2 * rect_h));//right bottom

    //        value = widget->GetImageValue(rect.left(), rect.bottom() - rect.height() / 2 );
    //        painter.setPen(QPen(reverseColor(value), 0, Qt::SolidLine, Qt::FlatCap));
    //        painter.drawRect(QRectF(rect.left() - rect_w, rect.bottom() - rect.height() / 2 - rect_h, 2 * rect_w, 2 * rect_h));//left center

    //        value = widget->GetImageValue(rect.right() - rect.width() / 2, rect.top());
    //        painter.setPen(QPen(reverseColor(value), 0, Qt::SolidLine, Qt::FlatCap));
    //        painter.drawRect(QRectF(rect.right() - rect.width() / 2 - rect_w, rect.top() - rect_h, 2 * rect_w, 2 * rect_h));//top center

    //        value = widget->GetImageValue(rect.right(), rect.bottom() - rect.height() / 2);
    //        painter.setPen(QPen(reverseColor(value), 0, Qt::SolidLine, Qt::FlatCap));
    //        painter.drawRect(QRectF(rect.right() - rect_w, rect.bottom() - rect.height() / 2 - rect_h, 2 * rect_w, 2 * rect_h));//right center

    //        value = widget->GetImageValue(rect.right() - rect.width() / 2, rect.bottom());
    //        painter.setPen(QPen(reverseColor(value), 0, Qt::SolidLine, Qt::FlatCap));
    //        painter.drawRect(QRectF(rect.right() - rect.width() / 2 - rect_w, rect.bottom() - rect_h, 2 * rect_w, 2 * rect_h));//bottom center
    //    }
    //    else
    {
        painter.setPen(QPen(Qt::red, 0, Qt::SolidLine, Qt::FlatCap));
        painter.drawRect(QRectF(rect.left() - rect_w, rect.top() - rect_h, 2 * rect_w, 2 * rect_h));//left top
        painter.drawRect(QRectF(rect.left() - rect_w, rect.bottom() - rect_h, 2 * rect_w, 2 * rect_h));//left bottom
        painter.drawRect(QRectF(rect.right() - rect_w, rect.top() - rect_h, 2 * rect_w, 2 * rect_h));//right top
        painter.drawRect(QRectF(rect.right() - rect_w, rect.bottom() - rect_h, 2 * rect_w, 2 * rect_h));//right bottom
        painter.drawRect(QRectF(rect.left() - rect_w, rect.bottom() - rect.height() / 2 - rect_h, 2 * rect_w, 2 * rect_h));//left center
        painter.drawRect(QRectF(rect.right() - rect.width() / 2 - rect_w, rect.top() - rect_h, 2 * rect_w, 2 * rect_h));//top center
        painter.drawRect(QRectF(rect.right() - rect_w, rect.bottom() - rect.height() / 2 - rect_h, 2 * rect_w, 2 * rect_h));//right center
        painter.drawRect(QRectF(rect.right() - rect.width() / 2 - rect_w, rect.bottom() - rect_h, 2 * rect_w, 2 * rect_h));//bottom center
    }
}

ePOS AuxRect::getPosType(QPointF point, const QRectF& rect) const
{
    const double rect_w = 5 / m_fScale, rect_h = 5 / m_fScale;
    if (QRectF(rect.left() - rect_w, rect.top() - rect_h, 2 * rect_w, 2 * rect_h).contains(point))
        return POS_LEFT_TOP;
    if (QRectF(rect.left() - rect_w, rect.bottom() - rect_h, 2 * rect_w, 2 * rect_h).contains(point))
        return POS_LEFT_BOTTOM;
    if (QRectF(rect.right() - rect_w, rect.top() - rect_h, 2 * rect_w, 2 * rect_h).contains(point))
        return POS_RIGHT_TOP;
    if (QRectF(rect.right() - rect_w, rect.bottom() - rect_h, 2 * rect_w, 2 * rect_h).contains(point))
        return POS_RIGHT_BOTTOM;
    if (QRectF(rect.left() - rect_w, rect.bottom() - rect.height() / 2 - rect_h, 2 * rect_w, 2 * rect_h).contains(point))
        return POS_LEFT_CENTER;
    if (QRectF(rect.right() - rect.width() / 2 - rect_w, rect.top() - rect_h, 2 * rect_w, 2 * rect_h).contains(point))
        return POS_TOP_CENTER;
    if (QRectF(rect.right() - rect_w, rect.bottom() - rect.height() / 2 - rect_h, 2 * rect_w, 2 * rect_h).contains(point))
        return POS_RIGHT_CENTER;
    if (QRectF(rect.right() - rect.width() / 2 - rect_w, rect.bottom() - rect_h, 2 * rect_w, 2 * rect_h).contains(point))
        return POS_BOTTOM_CENTER;
    if (rect.contains(point))
        return POS_INNER;
    return POS_NONE;
}

QRectF AuxRect::getAuxRect() const
{
    return m_rect;
}

void AuxRect::updateRect(const QPointF& pos)
{
    const QRectF oldRect = getAuxRect();
    const QPointF dis = pos - m_ptS;
    double left = oldRect.left();
    double top = oldRect.top();
    double right = oldRect.right();
    double bottom = oldRect.bottom();
    switch (m_iPosType)
    {
    case POS_LEFT_TOP:
        left = pos.x();
        top = pos.y();
        break;
    case POS_TOP_CENTER:
        top = pos.y();
        break;
    case POS_RIGHT_TOP:
        top = pos.y();
        right = pos.x();
        break;
    case POS_LEFT_CENTER:
        left = pos.x();
        break;
    case POS_RIGHT_CENTER:
        right = pos.x();
        break;
    case POS_LEFT_BOTTOM:
        left = pos.x();
        bottom = pos.y();
        break;
    case POS_BOTTOM_CENTER:
        bottom = pos.y();
        break;
    case POS_RIGHT_BOTTOM:
        right = pos.x();
        bottom = pos.y();
        break;
    case POS_INNER:
        left += dis.x();
        right += dis.x();
        top += dis.y();
        bottom += dis.y();
        break;
    case POS_NONE:
        return;
    }
    m_ptS = pos;

    int width = right - left;
    int height = bottom - top;

    if (height == oldRect.height() && width == oldRect.width())
    {
        if (left < 0)
            left = 0, right = left + oldRect.width();
        else if (right > m_iMaxW)
            right = m_iMaxW, left = right - oldRect.width();
        if (top < 0)
            top = 0, bottom = top + oldRect.height();
        else if (bottom > m_iMaxH)
            bottom = m_iMaxH, top = bottom - oldRect.height();
    }
    else
    {
        if (left < 0)
            left = 0, right = oldRect.right();
        else if (right > m_iMaxW)
            right = m_iMaxW, left = oldRect.left();
        if (top < 0)
            top = 0, bottom = oldRect.bottom();
        else if (bottom > m_iMaxH)
            bottom = m_iMaxH, top = oldRect.top();

        if (left == oldRect.left() && right < left)
            right = oldRect.left();
        if (right == oldRect.right() && left > right)
            left = oldRect.right();
        if (top == oldRect.top() && bottom < top)
            bottom = oldRect.top();
        if (bottom == oldRect.bottom() && top > bottom)
            top = oldRect.bottom();
    }
    setAuxRect(QRectF(QPointF(left, top), QPointF(right, bottom)));
    emit sizeChanged(m_rect, m_type);
}

void AuxRect::mousePress(const QPointF& pos)
{
    m_ptS = pos;
}

void AuxRect::mouseMove(const QPointF& pos)
{
    updateRect(pos);
}

void AuxRect::mouseRelease()
{
    emit sizeChangedFini(m_rect, m_type);
    m_iPosType = POS_NONE;
}

ePOS AuxRect::mouseHover(const QPointF& pos)
{
    ePOS iret = POS_NONE;
    if (!m_bHide)
        iret = getPosType(pos, getAuxRect());
    return iret;
}

bool AuxRect::hasFocus(const QPointF& pos)
{
    m_iPosType = getPosType(pos, getAuxRect());
    return !m_bHide && POS_NONE != m_iPosType;
}
