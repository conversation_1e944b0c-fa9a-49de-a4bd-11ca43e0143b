# TpDatabase 配置文件示例
# 复制此文件为 database.ini 并修改相应配置

[Database]
# 数据库服务器地址
host=localhost

# 数据库端口
port=3306

# 数据库名称
database=xcamview

# 用户名
username=root

# 密码
password=

# 数据库驱动（通常不需要修改）
driver=QMYSQL

# 连接选项（可选）
# options=CLIENT_IGNORE_SPACE=1;MYSQL_OPT_RECONNECT=1

[ConnectionPool]
# 最小连接数
minConnections=2

# 最大连接数
maxConnections=10

# 连接超时时间（毫秒）
connectionTimeout=30000

# 空闲超时时间（毫秒，5分钟）
idleTimeout=300000

# 健康检查间隔（毫秒，1分钟）
healthCheckInterval=60000

# 是否启用健康检查
enableHealthCheck=true

[Performance]
# 是否启用查询缓存
enableQueryCache=true

# 查询缓存大小
queryCacheSize=100

# 查询缓存超时（毫秒，5分钟）
queryCacheTimeout=300000

# 是否启用慢查询日志
enableSlowQueryLog=true

# 慢查询阈值（毫秒）
slowQueryThreshold=1000

# 是否启用批量操作优化
enableBatchOptimization=true

# 批量操作大小
batchSize=1000

[Security]
# 是否启用SQL注入防护
enableSqlInjectionProtection=true

# 是否启用输入验证
enableInputValidation=true

# 是否启用敏感数据加密
enableSensitiveDataEncryption=false

# 加密密钥（如果启用加密）
encryptionKey=

# 是否启用访问控制
enableAccessControl=false

# 配置说明：
# 1. 基础配置部分包含数据库连接的基本信息
# 2. 连接池配置用于优化数据库连接管理
# 3. 性能配置用于提升查询和操作效率
# 4. 安全配置用于增强数据安全性
#
# 环境变量支持：
# 可以使用以下环境变量覆盖配置文件设置：
# TP_DB_HOST - 数据库主机
# TP_DB_PORT - 数据库端口
# TP_DB_DATABASE - 数据库名称
# TP_DB_USER - 用户名
# TP_DB_PASSWORD - 密码
# TP_DB_DRIVER - 数据库驱动
# TP_DB_MIN_CONNECTIONS - 最小连接数
# TP_DB_MAX_CONNECTIONS - 最大连接数
#
# 配置文件搜索顺序：
# 1. 应用程序目录/config/database.ini
# 2. 应用程序目录/database.ini
# 3. 当前工作目录/config/database.ini
# 4. 当前工作目录/database.ini
# 5. 系统配置目录/XCamView/database.ini
