﻿#include "chatwidget.h"
#include <QVBoxLayout>

ChatWidget::ChatWidget(QWidget* parent)
    : QWidget(parent)
{
    m_pMsgTEdit = new QTextEdit();
    m_pSendBtn = new QPushButton(g_i18n->value("ids_send"));

    QVBoxLayout* mainLyt = new QVBoxLayout();
    mainLyt->addWidget(m_pMsgTEdit, 1);
    mainLyt->addWidget(m_pSendBtn, 0, Qt::AlignRight);
    setLayout(mainLyt);
}

void ChatWidget::Notify(int notify)
{

}

