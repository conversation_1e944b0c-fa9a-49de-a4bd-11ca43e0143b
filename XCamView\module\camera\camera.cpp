﻿#include "camera.h"

Camera::Camera(QObject *parent)
    : QObject(parent)
{

}

bool Camera::Open(const CameraSt &info)
{
    Q_UNUSED(info);
    return false;
}

const CameraSt &Camera::GetCamInfo() const
{
    return m_camInfo;
}

void Camera::Close()
{

}

void Camera::Stop()
{

}

HRESULT Camera::Start()
{
    return E_NOTIMPL;
}

bool Camera::IsOpened()
{
    return false;
}

bool Camera::IsRunning()
{
    return false;
}

HRESULT Camera::PutPara(unsigned nId, int val)
{
    Q_UNUSED(nId);
    Q_UNUSED(val);
    return E_NOTIMPL;
}

HRESULT Camera::GetPara(unsigned nId, int *pVal)
{
    Q_UNUSED(nId);
    Q_UNUSED(pVal);
    return E_NOTIMPL;
}

HRESULT Camera::GetParaRange(unsigned nId, int *pMin, int *pMax, int *pDef)
{
    Q_UNUSED(nId);
    Q_UNUSED(pMin);
    Q_UNUSED(pMax);
    Q_UNUSED(pDef);
    return E_NOTIMPL;
}

void Camera::ChangeResolution(int res)
{
    Q_UNUSED(res);
}

int Camera::GetImageWidth()
{
    return 0;
}

int Camera::GetImageHeight()
{
    return 0;
}

void Camera::GetImage(void* pFrameBuffer)
{
    Q_UNUSED(pFrameBuffer);
}
