@echo off
echo ========================================
echo XCamView MySQL Connection Fix Tool
echo ========================================
echo.
echo This tool will fix the MySQL connection issue:
echo "Access denied for user 'root'@'DESKTOP-KDN5QHV'"
echo.
echo Current computer name: %COMPUTERNAME%
echo.

set /p MYSQL_PASSWORD="Enter MySQL root password: "

echo.
echo Executing MySQL fix commands...
echo.

echo Creating universal root user...
mysql -u root -p%MYSQL_PASSWORD% -e "CREATE USER IF NOT EXISTS 'root'@'%%' IDENTIFIED BY '123456@mysql';"
mysql -u root -p%MYSQL_PASSWORD% -e "GRANT ALL PRIVILEGES ON *.* TO 'root'@'%%' WITH GRANT OPTION;"

echo Creating hostname-specific user...
mysql -u root -p%MYSQL_PASSWORD% -e "CREATE USER IF NOT EXISTS 'root'@'%COMPUTERNAME%' IDENTIFIED BY '123456@mysql';"
mysql -u root -p%MYSQL_PASSWORD% -e "GRANT ALL PRIVILEGES ON *.* TO 'root'@'%COMPUTERNAME%' WITH GRANT OPTION;"

echo Creating IP range user...
mysql -u root -p%MYSQL_PASSWORD% -e "CREATE USER IF NOT EXISTS 'root'@'192.168.6.%%' IDENTIFIED BY '123456@mysql';"
mysql -u root -p%MYSQL_PASSWORD% -e "GRANT ALL PRIVILEGES ON *.* TO 'root'@'192.168.6.%%' WITH GRANT OPTION;"

echo Flushing privileges...
mysql -u root -p%MYSQL_PASSWORD% -e "FLUSH PRIVILEGES;"

echo.
echo Verifying users created:
mysql -u root -p%MYSQL_PASSWORD% -e "SELECT user, host FROM mysql.user WHERE user = 'root' ORDER BY host;"

echo.
echo ========================================
echo Fix completed successfully!
echo ========================================
echo.
echo You can now run XCamView.exe
echo The program should connect successfully.
echo.
pause
