{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "E:/Qt/Tools/CMake_64/bin/cmake.exe", "cpack": "E:/Qt/Tools/CMake_64/bin/cpack.exe", "ctest": "E:/Qt/Tools/CMake_64/bin/ctest.exe", "root": "E:/Qt/Tools/CMake_64/share/cmake-3.30"}, "version": {"isDirty": false, "major": 3, "minor": 30, "patch": 5, "string": "3.30.5", "suffix": ""}}, "objects": [{"jsonFile": "codemodel-v2-495b1a53ec44aa2088ad.json", "kind": "codemodel", "version": {"major": 2, "minor": 7}}, {"jsonFile": "cache-v2-98d5b1787379965a3c85.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-1aac09e343a99a5a4222.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 1}}], "reply": {"cache-v2": {"jsonFile": "cache-v2-98d5b1787379965a3c85.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-1aac09e343a99a5a4222.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 1}}, "codemodel-v2": {"jsonFile": "codemodel-v2-495b1a53ec44aa2088ad.json", "kind": "codemodel", "version": {"major": 2, "minor": 7}}}}