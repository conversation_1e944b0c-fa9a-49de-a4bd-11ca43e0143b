#include "tpcalibrationdlg.h"
#include "mainwindow.h"
#include "measure/tpmeasuredef.h"
#include "../../util/tutils/tutils.h"
#include <QLabel>
#include <QMessageBox>
#include <QGridLayout>
#include <QHBoxLayout>
#include <QVBoxLayout>

TpCalibrationDlg::TpCalibrationDlg(const bool bApply, QWidget* parent)
    : QDialog(parent)
    , m_editFocusIndex(0)
    , m_resolution(0)
    , m_dScreenLen(0)
{
    setWindowTitle(g_i18n->value("ids_calibrate"));
    setWindowFlags(windowFlags() & ~Qt::WindowContextHelpButtonHint & ~Qt::WindowMaximizeButtonHint | Qt::WindowStaysOnTopHint);
    setAttribute(Qt::WA_DeleteOnClose);
    initPanel(bApply);
    updateEdit(0);
    setFixedSize(sizeHint());
    connect(this, &TpCalibrationDlg::finished, this, &TpCalibrationDlg::onFinished);
}

void TpCalibrationDlg::initPanel(const bool bApply)
{
    QLabel* lbl_magnification = new QLabel(g_i18n->value("ids_beilv"), this);
    QLabel* lbl_actualLength = new QLabel(g_i18n->value("ids_calibration_ac"), this);
    QLabel* lbl_pixel = new QLabel(g_i18n->value("ids_calibration_pi"), this);
    QLabel* lbl_resolution = new QLabel(g_i18n->value("ids_resolution"), this);
    QLabel* lbl_resUnit = new QLabel(g_i18n->value("ids_calibration_pm"), this);
    m_cb_magnification = new QComboBox(this);
    m_cb_magnification->setEditable(true);
    m_cb_magnification->addItem("4X");
    m_cb_magnification->addItem("10X");
    m_cb_magnification->addItem("20X");
    m_cb_magnification->addItem("40X");
    m_cb_magnification->addItem("100X");
    m_cb_magnification->setCurrentIndex(-1);
    m_magnification = m_cb_magnification->currentText();
    connect(m_cb_magnification, SIGNAL(editTextChanged(QString)), this, SLOT(onMagnificationEditChanged(QString)));
    m_cb_actualLengthUnit = new QComboBox(this);
    m_cb_actualLengthUnit->addItem(g_i18n->value("ids_nm") + " (nm)");
    m_cb_actualLengthUnit->addItem(g_i18n->value("ids_um") + " (" + g_i18n->value("ids_abbrum") + ")");
    m_cb_actualLengthUnit->addItem(g_i18n->value("ids_mm") + " (mm)");
    m_cb_actualLengthUnit->addItem(g_i18n->value("ids_cm") + " (cm)");
    m_cb_actualLengthUnit->addItem(g_i18n->value("ids_m") + " (m)");
    m_cb_actualLengthUnit->addItem(g_i18n->value("ids_in") + " (in)");
    m_cb_actualLengthUnit->setCurrentIndex(0);
    m_cb_actualLengthUnit->setEditable(true);
    m_cb_actualLengthUnit->lineEdit()->setReadOnly(true);
    connect(m_cb_actualLengthUnit, SIGNAL(currentIndexChanged(int)), this, SLOT(onCurUnitIndexChanged(int)));
    m_edit_actualLength = new QLineEdit(this);
    connect(m_edit_actualLength, SIGNAL(textChanged(QString)), this, SLOT(onActualLengthEditTextChanged(QString)));
    m_edit_pixel = new QLineEdit(this);
    m_edit_pixel->setEnabled(false);
    m_edit_res = new QLineEdit(this);
    m_edit_res->setEnabled(false);
    m_btn_cancel = new QPushButton(g_i18n->value("ids_cancel"), this);
    m_btn_ok = new QPushButton(g_i18n->value("ids_ok"), this);
    m_btn_ok->setAutoDefault(true);
    connect(m_btn_cancel, &QPushButton::clicked, this, &TpCalibrationDlg::reject);
    connect(m_btn_ok, &QPushButton::clicked, this, &TpCalibrationDlg::onOk);
    if (bApply)
    {
        m_btn_apply = new QPushButton(g_i18n->value("ids_apply"), this);
        connect(m_btn_apply, &QPushButton::clicked, this, &TpCalibrationDlg::onApply);
    }
    QGridLayout* upLayout = new QGridLayout();
    upLayout->setMargin(1);
    int i = -1;
    upLayout->addWidget(lbl_magnification, ++i, 0);
    upLayout->addWidget(m_cb_magnification, i, 1);
    upLayout->addWidget(lbl_actualLength, ++i, 0);
    upLayout->addWidget(m_edit_actualLength, i, 1);
    upLayout->addWidget(m_cb_actualLengthUnit, i, 2);
    upLayout->addWidget(lbl_pixel, ++i, 0);
    upLayout->addWidget(m_edit_pixel, i, 1);
    upLayout->addWidget(lbl_resolution, ++i, 0);
    upLayout->addWidget(m_edit_res, i, 1);
    upLayout->addWidget(lbl_resUnit, i, 2);
    QHBoxLayout* btnLayout = new QHBoxLayout;
    btnLayout->addStretch(1);
    btnLayout->addWidget(m_btn_cancel);
    btnLayout->addWidget(m_btn_ok);
    if (bApply)
        btnLayout->addWidget(m_btn_apply);
    QVBoxLayout* mainLayout = new QVBoxLayout(this);
    mainLayout->setMargin(10);
    mainLayout->addLayout(upLayout);
    mainLayout->addLayout(btnLayout);
    setLayout(mainLayout);
}

void TpCalibrationDlg::delForwardZeros(QString* pStr)
{
    if (!pStr->isEmpty() && fabs(pStr->toDouble()) < MINDVALUE)
    {
        if (*pStr != "." && *pStr != "0.")
            *pStr = "0";
        return;
    }
    if (pStr->count() > 1 && pStr->at(1) != '.')
    {
        while (pStr->startsWith("0"))
            pStr->remove(0, 1);
    }
}

void TpCalibrationDlg::setEditFocus(int index)
{
    if (index > 1)
        index = 1;
    if (index)
    {
        if (!m_magnification.endsWith("X") && !m_magnification.isEmpty())
            m_magnification.append("X");
        m_cb_magnification->lineEdit()->setText(m_magnification);
    }
    m_editFocusIndex = index;
}

void TpCalibrationDlg::updateEdit(int index/* = -1*/)
{
    if (index > 1)
        index = 1;
    switch (index)
    {
    case 0:
        if (m_magnification.contains("X"))
        {
            int beg = m_magnification.indexOf("X");
            ++beg;
            int end = m_magnification.count();
            m_magnification.remove(beg, end - beg);
        }
        m_cb_magnification->lineEdit()->setText(m_magnification);
        m_cb_magnification->setFocus();
        setEditFocus(index);
        break;
    case 1:
        delForwardZeros(&m_actualLength);
        m_edit_actualLength->setText(m_actualLength);
        m_edit_actualLength->setFocus();
        setEditFocus(index);
        break;
    default:
        break;
    }
}

void TpCalibrationDlg::updateAllEdit()
{
    m_cb_magnification->lineEdit()->setText(m_magnification);
    m_edit_actualLength->setText(m_actualLength);
}

void TpCalibrationDlg::onOk(bool /*check*/)
{
    for (int i = 0; i < g_caliList.count(); ++i)
    {
        if (g_caliList.at(i).magnification == m_magnification)
        {
            QMessageBox::information(this, g_i18n->value("ids_message"), QString(g_i18n->value("ids_calibratedlgmagnificationexists"))
                                                                             .arg(g_caliList.at(i).magnification));
            return;
        }
    }
    accept();
}

void TpCalibrationDlg::onFinished(int result)
{
    if (result == QDialog::Accepted)
    {
        Calibration calibration;
        calibration.magnification = m_magnification;
        calibration.resolution = m_resolution;
        calibration.unit = m_cb_actualLengthUnit->currentIndex() + 1;
        emit calibrationComplete(calibration);
    }
    else
        emit calibrationComplete();
}

void TpCalibrationDlg::onApply()
{
    emit calibrationApply(m_resolution);
}

void TpCalibrationDlg::onLengthChanged(double length)
{
    if (fabs(m_dScreenLen - length) > MINDVALUE)
    {
        m_dScreenLen = length;
        m_edit_pixel->setText(QString::number(length, 'f', 2));
        updateRes(singleByteCharSet(m_actualLength).toDouble());
    }
}

void TpCalibrationDlg::onActualLengthEditTextChanged(const QString& text)
{
    m_actualLength = text;
    if (!m_actualLength.isEmpty())
        updateRes(singleByteCharSet(m_actualLength).toDouble());
    else
        m_edit_res->setText("");
    m_btn_ok->setEnabled(!(m_actualLength.isEmpty() || m_cb_magnification->lineEdit()->text().isEmpty() || m_edit_res->text() == "inf"));
}

void TpCalibrationDlg::onCurUnitIndexChanged(int /*index*/){
    if (!m_actualLength.isEmpty())
        updateRes(singleByteCharSet(m_actualLength).toDouble());
}

void TpCalibrationDlg::updateRes(double len)
{
    double pixel = singleByteCharSet(m_edit_pixel->text()).toDouble();
    switch (m_cb_actualLengthUnit->currentIndex() + 1)
    {
    case UNIT_NM:
        m_resolution = pixel / len * 1000000000;
        break;
    case UNIT_UM:
        m_resolution = pixel / len * 1000000;
        break;
    case UNIT_MM:
        m_resolution = pixel / len * 1000;
        break;
    case UNIT_CM:
        m_resolution = pixel / len * 100;
        break;
    case UNIT_M:
        m_resolution = pixel / len;
        break;
    case UNIT_IN:
        m_resolution = pixel / len / 0.0254;
        break;
    default:
        m_resolution = pixel / len;
        break;
    }
    m_edit_res->setText(QString::number(m_resolution, 'f', 2));
}

void TpCalibrationDlg::onMagnificationEditChanged(const QString& text)
{
    m_btn_ok->setEnabled(!(m_actualLength.isEmpty() || text.isEmpty() || m_edit_res->text() == "inf"));
    m_magnification = m_cb_magnification->currentText();
}
