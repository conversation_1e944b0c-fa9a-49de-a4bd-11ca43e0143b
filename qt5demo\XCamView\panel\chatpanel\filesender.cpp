#include "filesender.h"
#include <QDataStream>
#include <QFileInfo>
#include <QDebug>

FileSender::FileSender(QObject *parent) : QObject(parent) {}

FileSender::~FileSender()
{
    stopSending();
}

void FileSender::startSending(const QString &filePath, quint16 port)
{
    // 清理之前的资源
    stopSending();

    m_port = port;
    m_file = new QFile(filePath);

    if (!m_file->open(QIODevice::ReadOnly)) {
        emit errorOccurred(tr("无法打开文件: %1").arg(m_file->errorString()));
        delete m_file;
        m_file = nullptr;
        return;
    }

    m_fileName = QFileInfo(filePath).fileName();
    m_totalBytes = m_file->size();

    m_server = new QTcpServer(this);
    if (!m_server->listen(QHostAddress::Any, m_port)) {
        emit errorOccurred(tr("启动服务器失败: %1").arg(m_server->errorString()));
        delete m_server;
        m_server = nullptr;
        return;
    }

    connect(m_server, &QTcpServer::newConnection, this, &FileSender::handleNewConnection);
    emit statusChanged(tr("等待客户端连接..."));
}

void FileSender::stopSending()
{
    if (m_clientSocket) {
        m_clientSocket->disconnect();
        m_clientSocket->abort();
        m_clientSocket->deleteLater();
        m_clientSocket = nullptr;
    }

    if (m_server) {
        m_server->close();
        m_server->deleteLater();
        m_server = nullptr;
    }

    if (m_file) {
        if (m_file->isOpen()) m_file->close();
        m_file->deleteLater();
        m_file = nullptr;
    }

    m_outBlock.clear();
    m_bytesWritten = 0;
    m_bytesToWrite = 0;
}

void FileSender::handleNewConnection()
{
    m_clientSocket = m_server->nextPendingConnection();
    connect(m_clientSocket, &QTcpSocket::bytesWritten,
            this, &FileSender::updateTransferProgress);
    connect(m_clientSocket, QOverload<QAbstractSocket::SocketError>::of(&QTcpSocket::error),
            this, &FileSender::handleSocketError);

    m_server->close(); // 只接受一个客户端连接
    emit statusChanged(tr("connect,ready to transfer"));

    m_timer.start();
    sendFileHeader();
}

void FileSender::sendFileHeader()
{
    QDataStream sendStream(&m_outBlock, QIODevice::WriteOnly);
    sendStream.setVersion(QDataStream::Qt_5_14);

    // 预留空间写入总大小和文件名大小
    sendStream << qint64(0) << qint64(0) << m_fileName;

    // 计算实际大小
    m_totalBytes += m_outBlock.size();

    // 回写实际大小
    sendStream.device()->seek(0);
    sendStream << m_totalBytes << qint64(m_outBlock.size() - sizeof(qint64)*2);

    // 发送文件头
    m_bytesToWrite = m_totalBytes - m_clientSocket->write(m_outBlock);
    m_outBlock.resize(0); // 清空缓存

    emit statusChanged(tr("开始发送文件: %1").arg(m_fileName));
}

void FileSender::updateTransferProgress(qint64 bytesSent)
{
    Q_UNUSED(bytesSent);

    // 检查是否还有数据要发送
    if (m_bytesToWrite > 0 && !m_file->atEnd()) {
        // 读取下一块数据
        QByteArray chunk = m_file->read(qMin(m_bytesToWrite, m_payloadSize));
        if (!chunk.isEmpty()) {
            qint64 written = m_clientSocket->write(chunk);
            if (written > 0) {
                m_bytesToWrite -= written;
                m_bytesWritten += written;
            }
        }
    }

    // 更新进度
    double elapsed = m_timer.elapsed() / 1000.0; // 转换为秒
    double speedMBs = elapsed > 0 ? (m_bytesWritten / (1024.0 * 1024.0)) / elapsed : 0;

    emit progressUpdated(m_bytesWritten, m_totalBytes, speedMBs);

    // 检查是否完成 - 使用更严格的完成条件
    if (m_bytesToWrite <= 0 && m_file->atEnd()) {
        // 确保所有数据都已写入网络
        m_clientSocket->waitForBytesWritten(3000);

        // 最终确认：发送100%进度
        emit progressUpdated(m_totalBytes, m_totalBytes, speedMBs);

        m_file->close();
        emit statusChanged(tr("file send complete"));
        emit transferFinished();

        qDebug() << "FileSender: 文件发送完成，总字节:" << m_totalBytes << "已发送:" << m_bytesWritten;
        return; // 重要：完成后立即返回，避免继续处理
    }

    // 如果接近完成但还没完全完成，继续处理
    if (m_bytesWritten >= m_totalBytes * 0.99 && m_bytesToWrite > 0) {
        qDebug() << "FileSender: 接近完成，剩余字节:" << m_bytesToWrite << "文件位置:" << m_file->pos() << "文件大小:" << m_file->size();
    }
}

void FileSender::handleSocketError(QAbstractSocket::SocketError socketError)
{
    Q_UNUSED(socketError);
    emit errorOccurred(tr("网络错误: %1").arg(m_clientSocket->errorString()));
    stopSending();
}
