// 编译测试文件 - 验证修复是否成功
#include "panel/chatpanel/filereceiver.h"
#include "panel/chatpanel/filesender.h"
#include "panel/chatpanel/chatwidget.h"
#include "panel/monitorpanel/monitorwidget.h"
#include "global.h"

#include <QApplication>
#include <QDebug>

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);
    
    qDebug() << "Testing compilation of fixed components...";
    
    // 测试新的消息类型
    MessageType testType1 = UserListRequest;
    MessageType testType2 = UserListResponse;
    qDebug() << "New message types compiled successfully:" << testType1 << testType2;
    
    // 测试 FileReceiver 编译
    FileReceiver* receiver = new FileReceiver();
    qDebug() << "FileReceiver compiled successfully";
    
    // 测试 FileSender 编译
    FileSender* sender = new FileSender();
    qDebug() << "FileSender compiled successfully";
    
    // 测试 ChatWidget 编译
    ChatWidget* chatWidget = new ChatWidget();
    qDebug() << "ChatWidget compiled successfully";
    
    // 测试 MonitorWidget 编译
    MonitorWidget* monitorWidget = new MonitorWidget();
    qDebug() << "MonitorWidget compiled successfully";
    
    // 清理
    delete receiver;
    delete sender;
    delete chatWidget;
    delete monitorWidget;
    
    qDebug() << "All components compiled and instantiated successfully!";
    qDebug() << "Critical fixes are working correctly.";
    
    return 0;
}
