#ifndef TPDATABASECONFIG_H
#define TPDATABASECONFIG_H

#include <QString>
#include <QSettings>

#if defined(TPDATABASE_LIBRARY)
#define TPDATABASE_EXPORT Q_DECL_EXPORT
#else
#define TPDATABASE_EXPORT Q_DECL_IMPORT
#endif

/**
 * @brief 数据库连接池配置结构
 */
struct TPDATABASE_EXPORT TpDatabasePoolConfig
{
    int minConnections = 2;         // 最小连接数
    int maxConnections = 10;        // 最大连接数
    int connectionTimeout = 30000;  // 连接超时时间（毫秒）
    int idleTimeout = 300000;       // 空闲超时时间（毫秒，5分钟）
    int healthCheckInterval = 60000; // 健康检查间隔（毫秒，1分钟）
    bool enableHealthCheck = true;   // 是否启用健康检查
    
    /**
     * @brief 验证配置有效性
     * @return true表示配置有效
     */
    bool isValid() const;
};

/**
 * @brief 数据库性能配置结构
 */
struct TPDATABASE_EXPORT TpDatabasePerformanceConfig
{
    bool enableQueryCache = true;       // 是否启用查询缓存
    int queryCacheSize = 100;          // 查询缓存大小
    int queryCacheTimeout = 300000;    // 查询缓存超时（毫秒，5分钟）
    
    bool enableSlowQueryLog = true;     // 是否启用慢查询日志
    int slowQueryThreshold = 1000;     // 慢查询阈值（毫秒）
    
    bool enableBatchOptimization = true; // 是否启用批量操作优化
    int batchSize = 1000;              // 批量操作大小
    
    /**
     * @brief 验证配置有效性
     * @return true表示配置有效
     */
    bool isValid() const;
};

/**
 * @brief 数据库安全配置结构
 */
struct TPDATABASE_EXPORT TpDatabaseSecurityConfig
{
    bool enableSqlInjectionProtection = true;  // SQL注入防护
    bool enableInputValidation = true;         // 输入验证
    bool enableSensitiveDataEncryption = false; // 敏感数据加密
    QString encryptionKey;                     // 加密密钥
    
    bool enableAccessControl = false;          // 访问控制
    QStringList allowedTables;                // 允许访问的表
    QStringList restrictedOperations;         // 受限操作
    
    /**
     * @brief 验证配置有效性
     * @return true表示配置有效
     */
    bool isValid() const;
};

/**
 * @brief 数据库配置管理类
 * 
 * 负责数据库连接配置的加载、验证和管理
 * 支持多种配置源：INI文件、环境变量、代码配置
 */
class TPDATABASE_EXPORT TpDatabaseConfig
{
public:
    /**
     * @brief 构造函数
     */
    TpDatabaseConfig();
    
    /**
     * @brief 拷贝构造函数
     */
    TpDatabaseConfig(const TpDatabaseConfig& other);
    
    /**
     * @brief 赋值操作符
     */
    TpDatabaseConfig& operator=(const TpDatabaseConfig& other);
    
    /**
     * @brief 析构函数
     */
    ~TpDatabaseConfig();

    // 基础连接配置
    QString hostName() const { return m_hostName; }
    void setHostName(const QString& hostName) { m_hostName = hostName; }
    
    int port() const { return m_port; }
    void setPort(int port) { m_port = port; }
    
    QString databaseName() const { return m_databaseName; }
    void setDatabaseName(const QString& databaseName) { m_databaseName = databaseName; }
    
    QString userName() const { return m_userName; }
    void setUserName(const QString& userName) { m_userName = userName; }
    
    QString password() const { return m_password; }
    void setPassword(const QString& password) { m_password = password; }
    
    QString driverName() const { return m_driverName; }
    void setDriverName(const QString& driverName) { m_driverName = driverName; }
    
    QString connectOptions() const { return m_connectOptions; }
    void setConnectOptions(const QString& options) { m_connectOptions = options; }

    // 扩展配置
    const TpDatabasePoolConfig& poolConfig() const { return m_poolConfig; }
    void setPoolConfig(const TpDatabasePoolConfig& config) { m_poolConfig = config; }
    
    const TpDatabasePerformanceConfig& performanceConfig() const { return m_performanceConfig; }
    void setPerformanceConfig(const TpDatabasePerformanceConfig& config) { m_performanceConfig = config; }
    
    const TpDatabaseSecurityConfig& securityConfig() const { return m_securityConfig; }
    void setSecurityConfig(const TpDatabaseSecurityConfig& config) { m_securityConfig = config; }

    // 配置管理方法
    
    /**
     * @brief 验证配置有效性
     * @return true表示配置有效
     */
    bool isValid() const;
    
    /**
     * @brief 从INI文件加载配置
     * @param filePath 配置文件路径，为空则自动搜索
     * @return true表示加载成功
     */
    bool loadFromFile(const QString& filePath = "");
    
    /**
     * @brief 从环境变量加载配置
     * @return true表示加载成功
     */
    bool loadFromEnvironment();
    
    /**
     * @brief 保存配置到INI文件
     * @param filePath 配置文件路径
     * @return true表示保存成功
     */
    bool saveToFile(const QString& filePath) const;
    
    /**
     * @brief 获取配置搜索路径列表
     * @return 配置文件搜索路径列表
     */
    QStringList getConfigSearchPaths() const;
    
    /**
     * @brief 格式化连接字符串（用于日志显示，不包含密码）
     * @return 格式化的连接字符串
     */
    QString formatConnectionString() const;

    // 静态工厂方法
    
    /**
     * @brief 创建默认配置
     * @return 默认配置对象
     */
    static TpDatabaseConfig createDefault();
    
    /**
     * @brief 从Qt5Demo的配置迁移
     * @param legacyConfig 旧配置对象
     * @return 新配置对象
     */
    static TpDatabaseConfig fromLegacyConfig(const struct DatabaseConfig& legacyConfig);

private:
    // 基础连接配置
    QString m_hostName;
    int m_port;
    QString m_databaseName;
    QString m_userName;
    QString m_password;
    QString m_driverName;
    QString m_connectOptions;
    
    // 扩展配置
    TpDatabasePoolConfig m_poolConfig;
    TpDatabasePerformanceConfig m_performanceConfig;
    TpDatabaseSecurityConfig m_securityConfig;
    
    // 私有方法
    void loadBasicConfigFromSettings(QSettings& settings);
    void loadPoolConfigFromSettings(QSettings& settings);
    void loadPerformanceConfigFromSettings(QSettings& settings);
    void loadSecurityConfigFromSettings(QSettings& settings);
    
    void saveBasicConfigToSettings(QSettings& settings) const;
    void savePoolConfigToSettings(QSettings& settings) const;
    void savePerformanceConfigToSettings(QSettings& settings) const;
    void saveSecurityConfigToSettings(QSettings& settings) const;
};

#endif // TPDATABASECONFIG_H
