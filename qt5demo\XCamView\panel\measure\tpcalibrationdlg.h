#ifndef TPCALIBRATIONDLG_H
#define TPCALIBRATIONDLG_H

#include <QComboBox>
#include <QLineEdit>
#include <QString>
#include <QPushButton>
#include <QDialog>

class TpCalibrationDlg : public QDialog
{
    Q_OBJECT
    QComboBox*      m_cb_magnification;
    QComboBox*      m_cb_actualLengthUnit;
    QLineEdit*      m_edit_actualLength;
    QLineEdit*      m_edit_pixel;
    QLineEdit*      m_edit_res;
    QString         m_magnification;
    QString         m_actualLength;
    int             m_editFocusIndex;
    double          m_resolution;
    double          m_dScreenLen;
    QPushButton*    m_btn_ok;
	QPushButton*    m_btn_cancel;
    QPushButton*    m_btn_apply;
public:
    explicit TpCalibrationDlg(const bool bApply = false, QWidget* parent = nullptr);
signals:
    void unitChanged(int mUnit);
    void calibrationComplete(Calibration calibration = Calibration{"", 1, 0});
    void calibrationApply(const double& resolution);
public slots:
    void onLengthChanged(double length);
private slots:
    void onOk(bool check);
    void onFinished(int result);
    void onApply();
    void onActualLengthEditTextChanged(const QString& text);
    void onCurUnitIndexChanged(int index);
    void onMagnificationEditChanged(const QString& text);
private:
    void initPanel(const bool bApply);
    void connectSignals();
    void setEditFocus(int index);
    void updateEdit(int index = -1);
    void updateAllEdit();
    void delForwardZeros(QString* pStr);
    void updateRes(double len);
};

#endif
