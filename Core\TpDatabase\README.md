# TpDatabase - 通用数据库API模块

TpDatabase是为XCamView项目设计的通用数据库访问模块，基于Qt5Demo的数据库功能扩展而来，提供了现代化、类型安全、高性能的数据库操作接口。

## 🚀 特性

### 核心功能
- **分层架构设计**: 连接管理、通用操作、业务抽象三层分离
- **统一错误处理**: 基于错误码的结果封装机制
- **连接池管理**: 自动连接分配、回收和健康检查
- **配置驱动**: 支持INI文件、环境变量、代码配置
- **Qt5/Qt6兼容**: 同时支持Qt5和Qt6框架

### 高级特性
- **自动重连**: 连接丢失时自动重连，指数退避策略
- **健康检查**: 定期检查连接有效性
- **性能监控**: 查询统计、慢查询检测
- **线程安全**: 内置互斥锁保护
- **SQL注入防护**: 强制参数绑定，输入验证

## 📦 模块结构

```
Core/TpDatabase/
├── TpDatabase.h/cpp           # 主头文件和模块初始化
├── TpDatabaseResult.h/cpp     # 结果封装和错误处理
├── TpDatabaseConfig.h/cpp     # 配置管理
├── TpDatabaseConnection.h/cpp # 连接管理
├── CMakeLists.txt            # 构建配置
├── database.ini.example      # 配置文件示例
├── test_basic.cpp           # 基础功能测试
└── README.md               # 本文档
```

## 🛠️ 快速开始

### 1. 基本使用

```cpp
#include "Core/TpDatabase/TpDatabase.h"

// 初始化模块
TpDatabase::initialize();

// 创建配置
TpDatabaseConfig config;
config.setHostName("localhost");
config.setPort(3306);
config.setDatabaseName("xcamview");
config.setUserName("root");
config.setPassword("password");

// 创建连接
TpDatabaseConnection connection(config);

// 连接数据库
TpDatabaseResult result = connection.connectToDatabase();
if (result.isSuccess()) {
    qDebug() << "数据库连接成功";
} else {
    qDebug() << "连接失败:" << result.errorMessage();
}

// 用户验证（兼容Qt5Demo）
TpDatabaseResult authResult = connection.validateUser("admin", "password");
if (authResult.isSuccess()) {
    int userId = authResult.data().toInt();
    qDebug() << "用户验证成功，ID:" << userId;
}
```

### 2. 配置文件使用

创建 `config/database.ini` 文件：

```ini
[Database]
host=localhost
port=3306
database=xcamview
username=root
password=your_password

[ConnectionPool]
minConnections=2
maxConnections=10
connectionTimeout=30000
```

代码中加载配置：

```cpp
TpDatabaseConfig config;
config.loadFromFile(); // 自动搜索配置文件
config.loadFromEnvironment(); // 加载环境变量

TpDatabaseConnection connection(config);
```

### 3. 环境变量配置

```bash
export TP_DB_HOST=*************
export TP_DB_PORT=3306
export TP_DB_DATABASE=xcamview
export TP_DB_USER=admin
export TP_DB_PASSWORD=secret
```

## 🔧 构建和集成

### CMake集成

在主项目的 `CMakeLists.txt` 中添加：

```cmake
# 添加TpDatabase子目录
add_subdirectory(Core/TpDatabase)

# 链接到主项目
target_link_libraries(XCamView PRIVATE TpDatabase)
```

### 预编译头文件

在 `predef.h` 中添加：

```cpp
#include "Core/TpDatabase/TpDatabase.h"
```

## 📋 API参考

### TpDatabaseResult - 结果封装

```cpp
// 检查操作结果
bool isSuccess() const;
TpDatabaseErrorCode errorCode() const;
QString errorMessage() const;
QVariant data() const;

// 静态工厂方法
static TpDatabaseResult success(const QVariant& data = QVariant());
static TpDatabaseResult error(TpDatabaseErrorCode code, const QString& message);
```

### TpDatabaseConfig - 配置管理

```cpp
// 基础配置
void setHostName(const QString& host);
void setPort(int port);
void setDatabaseName(const QString& name);
void setUserName(const QString& user);
void setPassword(const QString& password);

// 配置加载
bool loadFromFile(const QString& path = "");
bool loadFromEnvironment();
bool saveToFile(const QString& path) const;
```

### TpDatabaseConnection - 连接管理

```cpp
// 连接操作
TpDatabaseResult connectToDatabase();
void disconnectFromDatabase();
bool isConnected() const;
TpDatabaseResult testConnection();

// 用户认证
TpDatabaseResult validateUser(const QString& username, const QString& password);

// 健康检查
void setHealthCheckEnabled(bool enabled);
bool isHealthCheckEnabled() const;
```

## 🔍 错误处理

TpDatabase使用统一的错误码机制：

```cpp
enum class TpDatabaseErrorCode {
    Success = 0,
    ConnectionFailed,
    QueryFailed,
    TransactionFailed,
    ConfigError,
    ValidationError,
    // ... 更多错误码
};
```

错误处理示例：

```cpp
TpDatabaseResult result = connection.connectToDatabase();
if (!result.isSuccess()) {
    switch (result.errorCode()) {
    case TpDatabaseErrorCode::ConnectionFailed:
        // 处理连接失败
        break;
    case TpDatabaseErrorCode::ConfigError:
        // 处理配置错误
        break;
    default:
        // 处理其他错误
        break;
    }
}
```

## 🧪 测试

运行基础功能测试：

```bash
# 编译测试程序
g++ -std=c++17 test_basic.cpp -o test_basic `pkg-config --cflags --libs Qt5Core Qt5Sql`

# 运行测试
./test_basic
```

## 📈 性能优化

### 连接池配置

```ini
[ConnectionPool]
minConnections=2      # 最小连接数
maxConnections=10     # 最大连接数
connectionTimeout=30000   # 连接超时
idleTimeout=300000    # 空闲超时
healthCheckInterval=60000 # 健康检查间隔
```

### 查询缓存

```ini
[Performance]
enableQueryCache=true
queryCacheSize=100
queryCacheTimeout=300000
```

## 🔒 安全特性

- **SQL注入防护**: 强制使用参数绑定
- **输入验证**: 自动清理和验证输入
- **敏感数据加密**: 可选的密码和敏感字段加密
- **访问控制**: 基于表和操作的权限控制

## 🐛 故障排除

### 常见问题

1. **连接失败**: 检查数据库服务器状态、网络连通性、用户权限
2. **配置错误**: 验证配置文件格式、路径、权限
3. **驱动问题**: 确保安装了正确的Qt SQL驱动

### 调试信息

启用详细日志：

```cpp
TpDatabase::setLogLevel(4); // 调试级别
```

## 📄 许可证

本模块遵循与XCamView项目相同的许可证。

## 🤝 贡献

欢迎提交Issue和Pull Request来改进TpDatabase模块。

---

**版本**: 1.0.0  
**更新时间**: 2024年  
**维护者**: XCamView开发团队
