﻿#ifndef UVCHAMLOADER_H
#define UVCHAMLOADER_H

#include "uvcham.h"
#include <QLibrary>
#include <memory>

typedef const char*	(__stdcall* PDllPrefix)();

typedef unsigned (__stdcall* PUvcham_enum)(UvchamDevice arr[UVCHAM_MAX]);
typedef HUvcham  (__stdcall* PUvcham_open)(const wchar_t* camId);
typedef HRESULT  (__stdcall* PUvcham_start)(HUvcham h, void* pFrameBuffer, PUVCHAM_CALLBACK pCallbackFun, void* pCallbackCtx);
typedef HRESULT  (__stdcall* PUvcham_stop)(HUvcham h);
typedef void     (__stdcall* PUvcham_close)(HUvcham h);
typedef HRESULT  (__stdcall* PUvcham_put)(HUvcham h, unsigned nId, int val);
typedef HRESULT  (__stdcall* PUvcham_get)(HUvcham h, unsigned nId, int* pVal);
typedef HRESULT  (__stdcall* PUvcham_range)(HUvcham h, unsigned nId, int* pMin, int* pMax, int* pDef);
typedef HRESULT  (__stdcall* PUvcham_pull)(HUvcham h, void* pFrameBuffer);
typedef HRESULT  (__stdcall* PUvcham_record)(HUvcham h, const char* filePath);

class UvchamLoader
{
public:
    UvchamLoader();

    PUvcham_enum       pUvcham_enum  ;
    PUvcham_open       pUvcham_open  ;
    PUvcham_start      pUvcham_start ;
    PUvcham_stop       pUvcham_stop  ;
    PUvcham_close      pUvcham_close ;
    PUvcham_put        pUvcham_put   ;
    PUvcham_get        pUvcham_get   ;
    PUvcham_range      pUvcham_range ;
    PUvcham_pull       pUvcham_pull  ;
    PUvcham_record     pUvcham_record;

    bool initLoader();
    bool isLoad() const { return m_bLoad; }

private:
    bool InitUvcham(const std::shared_ptr<QLibrary>& libptr, const char* prefix);

    std::shared_ptr<QLibrary> m_pLibUvcham;
    bool m_bLoad;
};

extern UvchamLoader g_uvchamLoader;

#endif // UVCHAMLOADER_H
