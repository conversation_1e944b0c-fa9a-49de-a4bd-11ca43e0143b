#include "i18n.h"
#include <QDir>
#include <QDomDocument>
#include <QApplication>

I18n* g_i18n = nullptr;

static QString getDefLang()
{
    switch (QLocale::system().language())
    {
    case QLocale::Chinese:
    {
        QLocale::Country lc = QLocale::system().country();
        if (QLocale::Taiwan != lc && QLocale::Macau != lc && QLocale::HongKong != lc)
            return "hans";
    }
    default:
        return "en";
    }
}

I18n::I18n()
{
    g_i18n = this;
}

const QString& I18n::value(const char* id)
{
    static QString nullstr;
    QMap<QString, QString>::const_iterator it = map_.find(id);
    if (it != map_.end())
        return it.value();
    return nullstr;
}

void I18n::init()
{
    QDir xmlpath(QApplication::applicationDirPath());
#ifdef _DEBUG
    xmlpath.cd("../../res/i18n");
#else
    xmlpath.cd("i18n");
#endif
	QString strLang;
//    if (g_mw->strLang() != "def")
//        strLang = g_mw->strLang();
//    else
    strLang = getDefLang();
    loadXML(xmlpath.filePath(strLang + ".xml"), false);
    if (strLang != "en")
        loadXML(xmlpath.filePath("en.xml"), true);
    lang_ = strLang;
}

void I18n::insert(const QString& id, const QString& val, bool patch)
{
    if (patch)
    {
        if (!map_.contains(id))
            map_.insert(id, val);
    }
    else
    {
        map_.insert(id, val);
    }
}

void I18n::loadXML(const QString& path, bool patch)
{
    QDomDocument doc;
    QFile file(path);
    if (!file.open(QIODevice::ReadOnly))
        return;
    if (!doc.setContent(&file))
    {
        file.close();
        return;
    }
    file.close();

    QDomNodeList nodelist = doc.elementsByTagName("i18n");
    if (nodelist.count() > 0)
    {
        QDomNode node = nodelist.at(0).firstChild();
        while (!node.isNull())
        {
            if (node.toElement().tagName() == "str")
            {
                const QString id = node.toElement().attribute("id");
                const QString val = node.toElement().attribute("val");
                insert(id, val, patch);
            }
            node = node.nextSibling();
        }
    }
}
