#ifndef LOGINDIALOG_H
#define LOGINDIALOG_H

#include <QDialog>
#include <QLabel>
#include <QLineEdit>
#include <QPushButton>

class LoginDialog : public QDialog
{
    Q_OBJECT
public:
    explicit LoginDialog(QWidget *parent = nullptr);

private:
    QLabel* m_pNameLabel;
    QLineEdit* m_pNameLineEdit;
    QLabel* m_pPasswordLabel;
    QLineEdit* m_pPasswordLineEdit;
    QPushButton* m_pOKBtn;
    QPushButton* m_pCancelBtn;
};

#endif // LOGINDIALOG_H
