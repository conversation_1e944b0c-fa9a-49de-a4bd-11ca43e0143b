cmake_minimum_required(VERSION 3.16)

project(XCamView VERSION 0.1 LANGUAGES CXX)

set(CMAKE_AUTOUIC ON)
set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTORCC ON)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

find_package(QT NAMES Qt6 Qt5 REQUIRED COMPONENTS Widgets Xml Sql)
find_package(Qt${QT_VERSION_MAJOR} REQUIRED COMPONENTS Widgets Xml Sql)

# 添加TpDatabase模块
add_subdirectory(../Core/TpDatabase TpDatabase)

set(PROJECT_SOURCES
        main.cpp
        mainwindow.cpp
        mainwindow.h
)

if(${QT_VERSION_MAJOR} GREATER_EQUAL 6)
  # qt6_add_resources(RESOURCES res/XCamView.qrc)
  qt_add_executable(XCamView
    MANUAL_FINALIZATION
    ${PROJECT_SOURCES}
    module/camera/uvchamcam/uvcham.h module/camera/uvchamcam/uvchamcam.cpp module/camera/uvchamcam/uvchamcam.h module/camera/uvchamcam/uvchamloader.cpp module/camera/uvchamcam/uvchamloader.h module/camera/camera.cpp module/camera/camera.h module/camera/cameradef.h module/camera/cameramanager.cpp module/camera/cameramanager.h module/auxrect/auxrect.cpp module/auxrect/auxrect.h module/i18n/i18n.cpp module/i18n/i18n.h module/TpSaveLoader/tpsaveloader.cpp module/TpSaveLoader/tpsaveloader.h module/TpSignalConnect/TpSignalConnect.cpp module/TpSignalConnect/TpSignalConnect.h module/tutils/tutils.cpp module/tutils/tutils.h panel/camerapanel/cameralistwidget.cpp panel/camerapanel/cameralistwidget.h panel/camerapanel/camerapanel.cpp panel/camerapanel/camerapanel.h panel/camerapanel/coloradjustwidget.cpp panel/camerapanel/coloradjustwidget.h panel/camerapanel/exposuregainwidget.cpp panel/camerapanel/exposuregainwidget.h panel/camerapanel/powerfrequencywidget.cpp panel/camerapanel/powerfrequencywidget.h panel/camerapanel/resolutionwidget.cpp panel/camerapanel/resolutionwidget.h panel/camerapanel/whitebalancewidget.cpp panel/camerapanel/whitebalancewidget.h panel/centralpanel/displayview/displayview.cpp panel/centralpanel/displayview/displayview.h panel/centralpanel/displayview/imageview.cpp panel/centralpanel/displayview/imageview.h panel/centralpanel/displayview/previewview.cpp panel/centralpanel/displayview/previewview.h panel/centralpanel/monitorview/monitorview.cpp panel/centralpanel/monitorview/monitorview.h panel/centralpanel/tptabwidget.cpp panel/centralpanel/tptabwidget.h panel/centralpanel/view.cpp panel/centralpanel/view.h panel/chatpanel/chatwidget.cpp panel/chatpanel/chatwidget.h panel/login/logindialog.cpp panel/login/logindialog.h panel/measure/tpcalibrationdlg.cpp panel/measure/tpcalibrationdlg.h panel/measure/tptexteditdlg.cpp panel/measure/tptexteditdlg.h panel/monitorpanel/monitordialog.cpp panel/monitorpanel/monitordialog.h panel/monitorpanel/monitorwidget.cpp panel/monitorpanel/monitorwidget.h panel/collapsewidget.cpp panel/collapsewidget.h panel/leftpanel.cpp panel/leftpanel.h global.cpp global.h predef.h
    res/XCamView.qrc
  )
# Define target properties for Android with Qt 6 as:
#    set_property(TARGET XCamView APPEND PROPERTY QT_ANDROID_PACKAGE_SOURCE_DIR
#                 ${CMAKE_CURRENT_SOURCE_DIR}/android)
# For more information, see https://doc.qt.io/qt-6/qt-add-executable.html#target-creation
else()
    if(ANDROID)
        add_library(XCamView SHARED
            ${PROJECT_SOURCES}
        )
# Define properties for Android with Qt 5 after find_package() calls as:
#    set(ANDROID_PACKAGE_SOURCE_DIR "${CMAKE_CURRENT_SOURCE_DIR}/android")
    else()
        add_executable(XCamView
            ${PROJECT_SOURCES}
        )
    endif()
endif()

target_link_libraries(XCamView PRIVATE Qt${QT_VERSION_MAJOR}::Widgets Qt${QT_VERSION_MAJOR}::Xml Qt${QT_VERSION_MAJOR}::Sql TpDatabase)

target_include_directories(XCamView PRIVATE lib/include)
target_link_libraries(XCamView PRIVATE
  $<$<CONFIG:Debug>: ${CMAKE_CURRENT_SOURCE_DIR}/lib/x64/TpLogd.lib ${CMAKE_CURRENT_SOURCE_DIR}/lib/x64/TpMeasureManagerd.lib>
  $<$<CONFIG:Release>: ${CMAKE_CURRENT_SOURCE_DIR}/lib/x64/TpLog.lib ${CMAKE_CURRENT_SOURCE_DIR}/lib/x64/TpMeasureManager.lib>
)

target_precompile_headers(XCamView PRIVATE predef.h)

# Qt for iOS sets MACOSX_BUNDLE_GUI_IDENTIFIER automatically since Qt 6.1.
# If you are developing for iOS or macOS you should consider setting an
# explicit, fixed bundle identifier manually though.
if(${QT_VERSION} VERSION_LESS 6.1.0)
  set(BUNDLE_ID_OPTION MACOSX_BUNDLE_GUI_IDENTIFIER com.example.XCamView)
endif()
set_target_properties(XCamView PROPERTIES
    ${BUNDLE_ID_OPTION}
    MACOSX_BUNDLE_BUNDLE_VERSION ${PROJECT_VERSION}
    MACOSX_BUNDLE_SHORT_VERSION_STRING ${PROJECT_VERSION_MAJOR}.${PROJECT_VERSION_MINOR}
    MACOSX_BUNDLE TRUE
    WIN32_EXECUTABLE TRUE
)

include(GNUInstallDirs)
install(TARGETS XCamView
    BUNDLE DESTINATION .
    LIBRARY DESTINATION ${CMAKE_INSTALL_LIBDIR}
    RUNTIME DESTINATION ${CMAKE_INSTALL_BINDIR}
)

if(QT_VERSION_MAJOR EQUAL 6)
    qt_finalize_executable(XCamView)
endif()
