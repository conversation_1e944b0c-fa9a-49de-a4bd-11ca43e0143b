{"BUILD_DIR": "D:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Debug/XCamView_autogen", "CMAKE_BINARY_DIR": "D:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Debug", "CMAKE_CURRENT_BINARY_DIR": "D:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Debug", "CMAKE_CURRENT_SOURCE_DIR": "D:/work/XCamView/XCamView", "CMAKE_SOURCE_DIR": "D:/work/XCamView/XCamView", "CROSS_CONFIG": false, "GENERATOR": "Ninja", "INCLUDE_DIR": "D:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Debug/XCamView_autogen/include", "INPUTS": ["D:/work/XCamView/XCamView/res/images/flip.png", "D:/work/XCamView/XCamView/res/images/camera.png", "D:/work/XCamView/XCamView/res/images/misc.png", "D:/work/XCamView/XCamView/res/images/collapse.png", "D:/work/XCamView/XCamView/res/images/save.png", "D:/work/XCamView/XCamView/res/images/open.png", "D:/work/XCamView/XCamView/res/images/exposure.png", "D:/work/XCamView/XCamView/res/images/cooling.png", "D:/work/XCamView/XCamView/res/images/res.png", "D:/work/XCamView/XCamView/res/images/capture.png", "D:/work/XCamView/XCamView/res/images/blacklevel.png", "D:/work/XCamView/XCamView/res/images/record_stop.png", "D:/work/XCamView/XCamView/res/images/bitdepth.png", "D:/work/XCamView/XCamView/res/images/expand.png", "D:/work/XCamView/XCamView/res/images/dragMoveCursor.png", "D:/work/XCamView/XCamView/res/images/qsave.png", "D:/work/XCamView/XCamView/res/images/light.png", "D:/work/XCamView/XCamView/res/images/sample.png", "D:/work/XCamView/XCamView/res/images/histogram.png", "D:/work/XCamView/XCamView/res/images/color.png", "D:/work/XCamView/XCamView/res/images/record_start.png", "D:/work/XCamView/XCamView/res/images/dfc.png", "D:/work/XCamView/XCamView/res/images/speed.png", "D:/work/XCamView/XCamView/res/images/labelMoveCursor.png", "D:/work/XCamView/XCamView/res/images/cg.png", "D:/work/XCamView/XCamView/res/images/measure/arbline.svg", "D:/work/XCamView/XCamView/res/images/measure/polygon.svg", "D:/work/XCamView/XCamView/res/images/measure/select.svg", "D:/work/XCamView/XCamView/res/images/measure/scalebar.svg", "D:/work/XCamView/XCamView/res/images/measure/angle.svg", "D:/work/XCamView/XCamView/res/images/measure/curve.svg", "D:/work/XCamView/XCamView/res/images/measure/export.svg", "D:/work/XCamView/XCamView/res/images/measure/vtline1.svg", "D:/work/XCamView/XCamView/res/images/measure/layerburn.svg", "D:/work/XCamView/XCamView/res/images/measure/twoCircles1.svg", "D:/work/XCamView/XCamView/res/images/measure/delete.svg", "D:/work/XCamView/XCamView/res/images/measure/vtline2.svg", "D:/work/XCamView/XCamView/res/images/measure/circle3.svg", "D:/work/XCamView/XCamView/res/images/measure/parallel.svg", "D:/work/XCamView/XCamView/res/images/measure/arrow.svg", "D:/work/XCamView/XCamView/res/images/measure/twoCircles2.svg", "D:/work/XCamView/XCamView/res/images/measure/point.svg", "D:/work/XCamView/XCamView/res/images/measure/annulus.svg", "D:/work/XCamView/XCamView/res/images/measure/ellipse.svg", "D:/work/XCamView/XCamView/res/images/measure/angle2.svg", "D:/work/XCamView/XCamView/res/images/measure/twoParallel.svg", "D:/work/XCamView/XCamView/res/images/measure/layernorm.svg", "D:/work/XCamView/XCamView/res/images/measure/hline.svg", "D:/work/XCamView/XCamView/res/images/measure/vline.svg", "D:/work/XCamView/XCamView/res/images/measure/calibration.svg", "D:/work/XCamView/XCamView/res/images/measure/circle.svg", "D:/work/XCamView/XCamView/res/images/measure/twoCircles.svg", "D:/work/XCamView/XCamView/res/images/measure/circle2.svg", "D:/work/XCamView/XCamView/res/images/measure/circle1.svg", "D:/work/XCamView/XCamView/res/images/measure/text.svg", "D:/work/XCamView/XCamView/res/images/measure/vtline.svg", "D:/work/XCamView/XCamView/res/images/measure/rectangle.svg", "D:/work/XCamView/XCamView/res/images/measure/arc.svg", "D:/work/XCamView/XCamView/res/images/measure/angle1.svg"], "LOCK_FILE": "D:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Debug/CMakeFiles/XCamView_autogen.dir/AutoRcc_XCamView_PNK5WDWK6L_Lock.lock", "MULTI_CONFIG": false, "OPTIONS": ["--no-zstd", "-name", "XCamView"], "OUTPUT_CHECKSUM": "PNK5WDWK6L", "OUTPUT_NAME": "qrc_XCamView.cpp", "RCC_EXECUTABLE": "E:/Qt/6.10.0/msvc2022_64/bin/rcc.exe", "RCC_LIST_OPTIONS": ["--list"], "SETTINGS_FILE": "D:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Debug/CMakeFiles/XCamView_autogen.dir/AutoRcc_XCamView_PNK5WDWK6L_Used.txt", "SOURCE": "D:/work/XCamView/XCamView/res/XCamView.qrc", "USE_BETTER_GRAPH": true, "VERBOSITY": 0}