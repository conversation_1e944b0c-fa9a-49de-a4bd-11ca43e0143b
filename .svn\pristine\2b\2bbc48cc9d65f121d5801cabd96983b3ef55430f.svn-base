﻿#ifndef PREDEF_H
#define PREDEF_H

#include <QDebug>
#include "global.h"
#include "tplog.h"
#include "module/camera/cameramanager.h"
#include "util/i18n/i18n.h"
#include "util/TpSignalConnect/TpSignalConnect.h"

#if defined(_WIN32)
#ifndef _INC_WINDOWS
#include <windows.h>
#endif
#else /* Linux or macOS */
#if (!defined(HRESULT)) && (!defined(__COREFOUNDATION_CFPLUGINCOM__)) /* CFPlugInCOM.h */
#define HRESULT int
#endif
#ifndef SUCCEEDED
#define SUCCEEDED(hr)   (((HRESULT)(hr)) >= 0)
#endif
#ifndef FAILED
#define FAILED(hr)      (((HRESULT)(hr)) < 0)
#endif
#endif

#ifndef TDIBWIDTHBYTES
#define TDIBWIDTHBYTES(bits)  ((unsigned)(((bits) + 31) & (~31)) / 8)
#endif

/********************************************************************************/
/* HRESULT: error code                                                          */
/* Please note that the return value >= 0 means success                         */
/* (especially S_FALSE is also successful, indicating that the internal value and the value set by the user is equivalent, which means "no operation"). */
/* Therefore, the SUCCEEDED and FAILED macros should generally be used to determine whether the return value is successful or failed. */
/* (Unless there are special needs, do not use "==S_OK" or "==0" to judge the return value) */
/*                                                                              */
/* #define SUCCEEDED(hr)   (((HRESULT)(hr)) >= 0)                               */
/* #define FAILED(hr)      (((HRESULT)(hr)) < 0)                                */
/*                                                                              */
/********************************************************************************/
#if defined(TOUPCAM_HRESULT_ERRORCODE_NEEDED)
#define S_OK                (HRESULT)(0x00000000) /* Success */
#define S_FALSE             (HRESULT)(0x00000001) /* Yet another success */ /* Remark: Different from S_OK, such as internal values and user-set values have coincided, equivalent to noop */
#define E_UNEXPECTED        (HRESULT)(0x8000ffff) /* Catastrophic failure */ /* Remark: Generally indicates that the conditions are not met, such as calling put_Option setting some options that do not support modification when the camera is running, and so on */
#define E_NOTIMPL           (HRESULT)(0x80004001) /* Not supported or not implemented */ /* Remark: This feature is not supported on this model of camera */
#define E_NOINTERFACE       (HRESULT)(0x80004002)
#define E_ACCESSDENIED      (HRESULT)(0x80070005) /* Permission denied */ /* Remark: The program on Linux does not have permission to open the USB device, please enable udev rules file or run as root */
#define E_OUTOFMEMORY       (HRESULT)(0x8007000e) /* Out of memory */
#define E_INVALIDARG        (HRESULT)(0x80070057) /* One or more arguments are not valid */
#define E_POINTER           (HRESULT)(0x80004003) /* Pointer that is not valid */ /* Remark: Pointer is NULL */
#define E_FAIL              (HRESULT)(0x80004005) /* Generic failure */
#define E_WRONG_THREAD      (HRESULT)(0x8001010e) /* Call function in the wrong thread */
#define E_GEN_FAILURE       (HRESULT)(0x8007001f) /* Device not functioning */ /* Remark: It is generally caused by hardware errors, such as cable problems, USB port problems, poor contact, camera hardware damage, etc */
#define E_BUSY              (HRESULT)(0x800700aa) /* The requested resource is in use */ /* Remark: The camera is already in use, such as duplicated opening/starting the camera, or being used by other application, etc */
#define E_PENDING           (HRESULT)(0x8000000a) /* The data necessary to complete this operation is not yet available */ /* Remark: No data is available at this time */
#define E_TIMEOUT           (HRESULT)(0x8001011f) /* This operation returned because the timeout period expired */
#define E_UNREACH           (HRESULT)(0x80072743) /* Network is unreachable */
#endif

#define DF_EP 1e-9

#define NOTIFY_CAM_OPEN          0x00000001
#define NOTIFY_CAM_CLOSE         0x00000002
#define NOTIFY_CAM_RES           0x00000003
#define NOTIFY_WBROI_VISIBLE     0x00000100
#define NOTIFY_DEV_CHANGED       0x00010000
#define NOTIFY_ZOOM_CHANGED      0x00020000
#define NOTIFY_MEASURE_CHANGED   0x00030000

enum eAuxRectType{
    RECT_NONE = 0,
    RECT_WB,
    RECT_BB,
    RECT_EXP,
    RECT_AF
};

enum ePOS{
    POS_NONE = -1,
    POS_INNER = 0,
    POS_LEFT_TOP,
    POS_TOP_CENTER,
    POS_RIGHT_TOP,
    POS_LEFT_CENTER,
    POS_RIGHT_CENTER,
    POS_LEFT_BOTTOM,
    POS_BOTTOM_CENTER,
    POS_RIGHT_BOTTOM
};

enum eZoomFit{
    FIT_TO_WIDTH = 0,
    FIT_TO_HEIGHT,
    FIT_TO_WINDOW
};

enum eActType
{
    ACT_NONE = -1,
    ACT_SELECT,
    ACT_POINT,
    ACT_ANGLE3P,
    ACT_ANGLE4P,
    ACT_ARROW,
    ACT_ARBLINE,
    ACT_HLINE,
    ACT_VLINE,
    ACT_PARALLEL4P,
    ACT_PARALLEL8P,
    ACT_VERTICAL3P,
    ACT_VERTICAL4P,
    ACT_ARC,
    ACT_CURVE,
    ACT_RECT,
    ACT_ELLIPSE,
    ACT_CIRCLECR,
    ACT_CIRCLE2P,
    ACT_CIRCLE3P,
    ACT_TWOCIRCLECR,
    ACT_TWOCIRCLE3P,
    ACT_ANNULUS,
    ACT_POLYGON,
    ACT_TEXT,
    ACT_SCALEBAR,
    ACT_CALIBRATION,
    ACT_WBRECT,
    ACT_EXPRECT,
    ACT_LEFT,
    ACT_RIGHT,
    ACT_UP,
    ACT_DOWN,
    ACT_DELETE
};

#endif // PREDEF_H
