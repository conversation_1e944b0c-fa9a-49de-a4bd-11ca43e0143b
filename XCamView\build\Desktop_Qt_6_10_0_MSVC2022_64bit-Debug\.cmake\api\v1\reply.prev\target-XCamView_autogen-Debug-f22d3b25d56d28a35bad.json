{"backtrace": 0, "backtraceGraph": {"commands": [], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}]}, "dependencies": [{"id": "XCamView_autogen_timestamp_deps::@6890427a1f51a3e7e1df"}], "id": "XCamView_autogen::@6890427a1f51a3e7e1df", "isGeneratorProvided": true, "name": "XCamView_autogen", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "", "sourceIndexes": [0]}, {"name": "CMake Rules", "sourceIndexes": [1, 2]}], "sources": [{"backtrace": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_10_0_MSVC2022_64bit-Debug/CMakeFiles/XCamView_autogen", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_10_0_MSVC2022_64bit-Debug/CMakeFiles/XCamView_autogen.rule", "sourceGroupIndex": 1}, {"backtrace": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_10_0_MSVC2022_64bit-Debug/XCamView_autogen/timestamp.rule", "sourceGroupIndex": 1}], "type": "UTILITY"}