/**
 * @file test_basic.cpp
 * @brief TpDatabase基础功能测试
 * 
 * 这个文件包含了TpDatabase模块的基础功能测试，
 * 可以用来验证模块的基本功能是否正常工作。
 * 
 * 编译方法：
 * g++ -std=c++17 test_basic.cpp -o test_basic `pkg-config --cflags --libs Qt5Core Qt5Sql`
 * 
 * 或者在CMake项目中：
 * add_executable(test_basic test_basic.cpp)
 * target_link_libraries(test_basic TpDatabase Qt5::Core Qt5::Sql)
 */

#include "TpDatabase.h"
#include <QCoreApplication>
#include <QDebug>
#include <iostream>

class TpDatabaseTester
{
public:
    TpDatabaseTester() = default;
    
    void runAllTests()
    {
        std::cout << "=== TpDatabase 基础功能测试 ===" << std::endl;
        
        testModuleInitialization();
        testConfigurationManagement();
        testResultHandling();
        testConnectionBasics();
        
        std::cout << "=== 测试完成 ===" << std::endl;
    }

private:
    void testModuleInitialization()
    {
        std::cout << "\n--- 测试模块初始化 ---" << std::endl;
        
        // 测试版本信息
        std::cout << "TpDatabase版本: " << TpDatabase::version() << std::endl;
        std::cout << "版本号: " << TpDatabase::versionNumber() << std::endl;
        
        // 测试版本检查
        bool versionOk = TpDatabase::versionAtLeast(1, 0, 0);
        std::cout << "版本检查 (>=1.0.0): " << (versionOk ? "✅ 通过" : "❌ 失败") << std::endl;
        
        // 测试初始化
        bool initResult = TpDatabase::initialize();
        std::cout << "模块初始化: " << (initResult ? "✅ 成功" : "❌ 失败") << std::endl;
        
        // 测试日志级别
        TpDatabase::setLogLevel(3);
        int logLevel = TpDatabase::logLevel();
        std::cout << "日志级别设置: " << (logLevel == 3 ? "✅ 正确" : "❌ 错误") << std::endl;
    }
    
    void testConfigurationManagement()
    {
        std::cout << "\n--- 测试配置管理 ---" << std::endl;
        
        // 测试默认配置
        TpDatabaseConfig config = TpDatabaseConfig::createDefault();
        std::cout << "默认配置创建: " << (config.isValid() ? "✅ 有效" : "❌ 无效") << std::endl;
        std::cout << "连接字符串: " << config.formatConnectionString().toStdString() << std::endl;
        
        // 测试配置修改
        config.setHostName("*************");
        config.setPort(3306);
        config.setDatabaseName("test_db");
        config.setUserName("test_user");
        config.setPassword("test_pass");
        
        std::cout << "配置修改后: " << config.formatConnectionString().toStdString() << std::endl;
        std::cout << "配置有效性: " << (config.isValid() ? "✅ 有效" : "❌ 无效") << std::endl;
        
        // 测试连接池配置
        TpDatabasePoolConfig poolConfig;
        poolConfig.minConnections = 1;
        poolConfig.maxConnections = 5;
        poolConfig.connectionTimeout = 10000;
        
        config.setPoolConfig(poolConfig);
        std::cout << "连接池配置: " << (config.poolConfig().isValid() ? "✅ 有效" : "❌ 无效") << std::endl;
        
        // 测试配置搜索路径
        QStringList searchPaths = config.getConfigSearchPaths();
        std::cout << "配置搜索路径数量: " << searchPaths.size() << std::endl;
        for (const QString& path : searchPaths) {
            std::cout << "  - " << path.toStdString() << std::endl;
        }
    }
    
    void testResultHandling()
    {
        std::cout << "\n--- 测试结果处理 ---" << std::endl;
        
        // 测试成功结果
        TpDatabaseResult successResult = TpDatabaseResult::success(42);
        std::cout << "成功结果: " << (successResult.isSuccess() ? "✅ 正确" : "❌ 错误") << std::endl;
        std::cout << "返回数据: " << successResult.data().toInt() << std::endl;
        
        // 测试错误结果
        TpDatabaseResult errorResult = TpDatabaseResult::error(
            TpDatabaseErrorCode::ConnectionFailed, 
            "测试错误信息"
        );
        std::cout << "错误结果: " << (!errorResult.isSuccess() ? "✅ 正确" : "❌ 错误") << std::endl;
        std::cout << "错误信息: " << errorResult.errorMessage().toStdString() << std::endl;
        std::cout << "错误码: " << TpDatabaseResult::errorCodeToString(errorResult.errorCode()).toStdString() << std::endl;
        
        // 测试拷贝构造
        TpDatabaseResult copyResult = successResult;
        std::cout << "拷贝构造: " << (copyResult.isSuccess() && copyResult.data().toInt() == 42 ? "✅ 正确" : "❌ 错误") << std::endl;
        
        // 测试赋值操作
        TpDatabaseResult assignResult;
        assignResult = errorResult;
        std::cout << "赋值操作: " << (!assignResult.isSuccess() ? "✅ 正确" : "❌ 错误") << std::endl;
    }
    
    void testConnectionBasics()
    {
        std::cout << "\n--- 测试连接基础功能 ---" << std::endl;
        
        // 创建配置
        TpDatabaseConfig config;
        config.setHostName("localhost");
        config.setPort(3306);
        config.setDatabaseName("test");
        config.setUserName("root");
        config.setPassword("");
        config.setDriverName("QMYSQL");
        
        // 创建连接对象
        TpDatabaseConnection connection(config);
        std::cout << "连接对象创建: ✅ 成功" << std::endl;
        std::cout << "连接名称: " << connection.connectionName().toStdString() << std::endl;
        
        // 测试初始状态
        bool initiallyConnected = connection.isConnected();
        std::cout << "初始连接状态: " << (!initiallyConnected ? "✅ 未连接" : "❌ 异常") << std::endl;
        
        TpDatabaseConnectionState state = connection.connectionState();
        std::cout << "初始状态码: " << static_cast<int>(state) << std::endl;
        
        // 测试配置获取
        TpDatabaseConfig retrievedConfig = connection.config();
        std::cout << "配置获取: " << (retrievedConfig.hostName() == "localhost" ? "✅ 正确" : "❌ 错误") << std::endl;
        
        // 测试健康检查设置
        connection.setHealthCheckEnabled(false);
        bool healthCheckDisabled = !connection.isHealthCheckEnabled();
        std::cout << "健康检查禁用: " << (healthCheckDisabled ? "✅ 成功" : "❌ 失败") << std::endl;
        
        connection.setHealthCheckEnabled(true);
        bool healthCheckEnabled = connection.isHealthCheckEnabled();
        std::cout << "健康检查启用: " << (healthCheckEnabled ? "✅ 成功" : "❌ 失败") << std::endl;
        
        // 测试统计信息
        QVariantMap stats = connection.getConnectionStats();
        std::cout << "统计信息获取: " << (!stats.isEmpty() ? "✅ 成功" : "❌ 失败") << std::endl;
        std::cout << "统计项目数量: " << stats.size() << std::endl;
        
        // 注意：实际的数据库连接测试需要真实的数据库环境
        std::cout << "注意: 实际数据库连接测试需要配置真实的数据库环境" << std::endl;
    }
};

int main(int argc, char *argv[])
{
    QCoreApplication app(argc, argv);
    
    // 设置应用程序信息
    app.setApplicationName("TpDatabase Test");
    app.setApplicationVersion("1.0.0");
    
    try {
        TpDatabaseTester tester;
        tester.runAllTests();
        
        // 清理模块
        TpDatabase::cleanup();
        
        std::cout << "\n所有测试执行完成！" << std::endl;
        return 0;
        
    } catch (const std::exception& e) {
        std::cerr << "测试过程中发生异常: " << e.what() << std::endl;
        return 1;
    } catch (...) {
        std::cerr << "测试过程中发生未知异常" << std::endl;
        return 1;
    }
}
