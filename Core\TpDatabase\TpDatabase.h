#ifndef TPDATABASE_H
#define TPDATABASE_H

/**
 * @file TpDatabase.h
 * @brief TpDatabase模块主头文件
 * 
 * 这个文件包含了TpDatabase模块的所有公共接口，
 * 外部项目只需要包含这一个头文件即可使用所有功能。
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @date 2024
 */

// 核心类
#include "TpDatabaseResult.h"
#include "TpDatabaseConfig.h"
#include "TpDatabaseConnection.h"

// 版本信息
#define TPDATABASE_VERSION_MAJOR 1
#define TPDATABASE_VERSION_MINOR 0
#define TPDATABASE_VERSION_PATCH 0
#define TPDATABASE_VERSION_STRING "1.0.0"

// 便利宏
#define TPDATABASE_VERSION_CHECK(major, minor, patch) \
    ((major << 16) | (minor << 8) | patch)

#define TPDATABASE_VERSION \
    TPDATABASE_VERSION_CHECK(TPDATABASE_VERSION_MAJOR, \
                            TPDATABASE_VERSION_MINOR, \
                            TPDATABASE_VERSION_PATCH)

/**
 * @namespace TpDatabase
 * @brief TpDatabase模块命名空间
 */
namespace TpDatabase {

/**
 * @brief 获取TpDatabase版本字符串
 * @return 版本字符串，格式为"major.minor.patch"
 */
inline const char* version() {
    return TPDATABASE_VERSION_STRING;
}

/**
 * @brief 获取TpDatabase版本号
 * @return 版本号，可用于版本比较
 */
inline int versionNumber() {
    return TPDATABASE_VERSION;
}

/**
 * @brief 检查TpDatabase版本是否满足要求
 * @param major 主版本号
 * @param minor 次版本号
 * @param patch 补丁版本号
 * @return true表示当前版本满足要求
 */
inline bool versionAtLeast(int major, int minor, int patch) {
    return TPDATABASE_VERSION >= TPDATABASE_VERSION_CHECK(major, minor, patch);
}

/**
 * @brief 初始化TpDatabase模块
 * 
 * 这个函数会执行必要的初始化操作，建议在使用TpDatabase之前调用。
 * 多次调用是安全的。
 * 
 * @return true表示初始化成功
 */
bool initialize();

/**
 * @brief 清理TpDatabase模块
 * 
 * 这个函数会清理模块使用的资源，建议在程序退出前调用。
 * 多次调用是安全的。
 */
void cleanup();

/**
 * @brief 设置全局日志级别
 * @param level 日志级别（0=关闭，1=错误，2=警告，3=信息，4=调试）
 */
void setLogLevel(int level);

/**
 * @brief 获取当前日志级别
 * @return 当前日志级别
 */
int logLevel();

} // namespace TpDatabase

// 类型别名，方便使用
using TpDbResult = TpDatabaseResult;
using TpDbConfig = TpDatabaseConfig;
using TpDbConnection = TpDatabaseConnection;
using TpDbErrorCode = TpDatabaseErrorCode;
using TpDbConnectionState = TpDatabaseConnectionState;

#endif // TPDATABASE_H
