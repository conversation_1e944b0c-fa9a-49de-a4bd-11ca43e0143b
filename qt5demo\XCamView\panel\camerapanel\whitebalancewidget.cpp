#include "whitebalancewidget.h"
#include "../../mainwindow.h"
#include <QGridLayout>

WhiteBalanceWidget::WhiteBalanceWidget(QWidget* parent)
    : CollapseWidget(QIcon(":/images/exposure.png"), g_i18n->value("ids_white_balance"), parent)
{
    m_pManualRb = new QRadioButton(g_i18n->value("ids_manual"));
    m_pAutoRb = new QRadioButton(g_i18n->value("ids_global_auto"));
    m_pRoiRb = new QRadioButton("ROI");
    m_pWBBGp = new QButtonGroup(this);
    m_pWBBGp->addButton(m_pManualRb, WB_MANUAL);
    m_pWBBGp->addButton(m_pAutoRb, WB_AUTO);
    m_pWBBGp->addButton(m_pRoiRb, WB_ROI);
    m_pWBRedLbl = new QLabel(g_i18n->value("ids_red"));
    m_pWBRedValueLbl = new QLabel("0");
    m_pWBRedValueLbl->setAlignment(Qt::AlignRight);
    m_pWBRedSlider = new QSlider(Qt::Horizontal);
    m_pWBGreenLbl = new QLabel(g_i18n->value("ids_green"));
    m_pWBGreenValueLbl = new QLabel("0");
    m_pWBGreenValueLbl->setAlignment(Qt::AlignRight);
    m_pWBGreenSlider = new QSlider(Qt::Horizontal);
    m_pWBBlueLbl = new QLabel(g_i18n->value("ids_blue"));
    m_pWBBlueValueLbl = new QLabel("0");
    m_pWBBlueValueLbl->setAlignment(Qt::AlignRight);
    m_pWBBlueSlider = new QSlider(Qt::Horizontal);
    m_pDefBtn = new QPushButton(g_i18n->value("ids_defaults"));

    QGridLayout *mainLyt = new QGridLayout();
    mainLyt->addWidget(m_pAutoRb, 0, 0);
    mainLyt->addWidget(m_pManualRb, 0, 1);
    mainLyt->addWidget(m_pRoiRb, 0, 2);
    mainLyt->addWidget(m_pWBRedLbl, 1, 0);
    mainLyt->addWidget(m_pWBRedValueLbl, 1, 2);
    mainLyt->addWidget(m_pWBRedSlider, 2, 0, 1, 3);
    mainLyt->addWidget(m_pWBGreenLbl, 3, 0);
    mainLyt->addWidget(m_pWBGreenValueLbl, 3, 2);
    mainLyt->addWidget(m_pWBGreenSlider, 4, 0, 1, 3);
    mainLyt->addWidget(m_pWBBlueLbl, 5, 0);
    mainLyt->addWidget(m_pWBBlueValueLbl, 5, 2);
    mainLyt->addWidget(m_pWBBlueSlider, 6, 0, 1, 3);
    mainLyt->addWidget(m_pDefBtn, 7, 0, 1, 3, Qt::AlignCenter);
    setLayout(mainLyt);

    connect(m_pWBBGp, SIGNAL(buttonClicked(int)), this, SLOT(OnWBBGpClicked(int)));
    connect(m_pWBRedSlider, SIGNAL(valueChanged(int)), this, SLOT(OnWBRedChanged(int)));
    connect(m_pWBGreenSlider, SIGNAL(valueChanged(int)), this, SLOT(OnWBGreenChanged(int)));
    connect(m_pWBBlueSlider, SIGNAL(valueChanged(int)), this, SLOT(OnWBBlueChanged(int)));
    connect(m_pDefBtn, SIGNAL(clicked()), this, SLOT(OnDefBtnClicked()));

    connect(this, SIGNAL(isHide(bool)), this, SLOT(OnIsHide(bool)));

    connect(g_pCameraManager, SIGNAL(CamEvent(int)), this, SLOT(OnCamEvent(int)));

    EnableBtns(false);
}

void WhiteBalanceWidget::Notify(int notify)
{
    if(NOTIFY_CAM_OPEN == notify)
    {
        EnableBtns(true);
        m_pWBBGp->blockSignals(true);
        m_pWBRedSlider->blockSignals(true);
        m_pWBGreenSlider->blockSignals(true);
        m_pWBBlueSlider->blockSignals(true);
        int min = 0, max = 0, val = 0;
        g_pCameraManager->GetParaRange(CAM_PARA_WBRED, &min, &max, nullptr);
        m_pWBRedSlider->setRange(min, max);
        g_pCameraManager->GetParaRange(CAM_PARA_WBGREEN, &min, &max, nullptr);
        m_pWBGreenSlider->setRange(min, max);
        g_pCameraManager->GetParaRange(CAM_PARA_WBBLUE, &min, &max, nullptr);
        m_pWBBlueSlider->setRange(min, max);
        g_pCameraManager->GetPara(CAM_PARA_WBMODE, &val);
        if(m_pWBBGp->button(val))
            m_pWBBGp->button(val)->setChecked(true);
        if(WB_AUTO != val)
        {
            g_pCameraManager->GetPara(CAM_PARA_WBRED, &val);
            m_pWBRedSlider->setValue(val);
            g_pCameraManager->GetPara(CAM_PARA_WBGREEN, &val);
            m_pWBGreenSlider->setValue(val);
            g_pCameraManager->GetPara(CAM_PARA_WBBLUE, &val);
            m_pWBBlueSlider->setValue(val);
        }
        m_pWBBGp->blockSignals(false);
        m_pWBRedSlider->blockSignals(false);
        m_pWBGreenSlider->blockSignals(false);
        m_pWBBlueSlider->blockSignals(false);
        UpdateBtnsEnabled();
        g_bWBDlgHidden = IsCollapsed();
        g_WBMode = m_pWBBGp->checkedId();
        g_pMainWindow->Notify(NOTIFY_WBROI_VISIBLE);
    }
    else if(NOTIFY_CAM_CLOSE == notify)
    {
        EnableBtns(false);
    }
}

void WhiteBalanceWidget::OnWBBGpClicked(int id)
{
    int cur = 0;
    g_pCameraManager->GetPara(CAM_PARA_WBMODE, &cur);
    if(cur != id)
    {
        g_pCameraManager->PutPara(CAM_PARA_WBMODE, id);
        UpdateBtnsEnabled();
        g_bWBDlgHidden = IsCollapsed();
        g_WBMode = id;
        g_pMainWindow->Notify(NOTIFY_WBROI_VISIBLE);
    }
}

void WhiteBalanceWidget::OnWBRedChanged(int value)
{
    int cur = 0;
    g_pCameraManager->GetPara(CAM_PARA_WBRED, &cur);
    if(cur != value)
    {
        g_pCameraManager->PutPara(CAM_PARA_WBRED, value);
        SetWBRedLabel(value);
    }
}

void WhiteBalanceWidget::OnWBGreenChanged(int value)
{
    int cur = 0;
    g_pCameraManager->GetPara(CAM_PARA_WBGREEN, &cur);
    if(cur != value)
    {
        g_pCameraManager->PutPara(CAM_PARA_WBGREEN, value);
        SetWBGreenLabel(value);
    }
}

void WhiteBalanceWidget::OnWBBlueChanged(int value)
{
    int cur = 0;
    g_pCameraManager->GetPara(CAM_PARA_WBBLUE, &cur);
    if(cur != value)
    {
        g_pCameraManager->PutPara(CAM_PARA_WBBLUE, value);
        SetWBBlueLabel(value);
    }
}

void WhiteBalanceWidget::OnDefBtnClicked()
{
    int def = 0;
    g_pCameraManager->GetParaRange(CAM_PARA_WBMODE, nullptr, nullptr, &def);
    if(m_pWBBGp->button(def))
        m_pWBBGp->button(def)->setChecked(true);
    if(WB_AUTO != def)
    {
        g_pCameraManager->GetParaRange(CAM_PARA_WBRED, nullptr, nullptr, &def);
        m_pWBRedSlider->setValue(def);
        g_pCameraManager->GetParaRange(CAM_PARA_WBGREEN, nullptr, nullptr, &def);
        m_pWBGreenSlider->setValue(def);
        g_pCameraManager->GetParaRange(CAM_PARA_WBBLUE, nullptr, nullptr, &def);
        m_pWBBlueSlider->setValue(def);
    }
}

void WhiteBalanceWidget::OnCamEvent(int event)
{
    if(CAM_EVENT_WB_CHANGED == event)
    {
        int val = 0;
        g_pCameraManager->GetPara(CAM_PARA_WBRED, &val);
        m_pWBRedSlider->blockSignals(true);
        m_pWBRedSlider->setValue(val);
        SetWBRedLabel(val);
        m_pWBRedSlider->blockSignals(false);

        g_pCameraManager->GetPara(CAM_PARA_WBGREEN, &val);
        m_pWBGreenSlider->blockSignals(true);
        m_pWBGreenSlider->setValue(val);
        SetWBGreenLabel(val);
        m_pWBGreenSlider->blockSignals(false);

        g_pCameraManager->GetPara(CAM_PARA_WBBLUE, &val);
        m_pWBBlueSlider->blockSignals(true);
        m_pWBBlueSlider->setValue(val);
        SetWBBlueLabel(val);
        m_pWBBlueSlider->blockSignals(false);
    }
}

void WhiteBalanceWidget::OnIsHide(bool bHide)
{
    g_bWBDlgHidden = bHide;
    g_WBMode = m_pWBBGp->checkedId();
    g_pMainWindow->Notify(NOTIFY_WBROI_VISIBLE);
}

void WhiteBalanceWidget::EnableBtns(bool bEnabled)
{
    m_pManualRb->setEnabled(bEnabled);
    m_pAutoRb->setEnabled(bEnabled);
    m_pRoiRb->setEnabled(bEnabled);
    m_pWBRedSlider->setEnabled(bEnabled);
    m_pWBGreenSlider->setEnabled(bEnabled);
    m_pWBBlueSlider->setEnabled(bEnabled);
    m_pDefBtn->setEnabled(bEnabled);
}

void WhiteBalanceWidget::UpdateBtnsEnabled()
{
    m_pWBRedSlider->setEnabled(WB_MANUAL == m_pWBBGp->checkedId());
    m_pWBGreenSlider->setEnabled(WB_MANUAL == m_pWBBGp->checkedId());
    m_pWBBlueSlider->setEnabled(WB_MANUAL == m_pWBBGp->checkedId());
}

void WhiteBalanceWidget::SetWBRedLabel(int val)
{
    m_pWBRedValueLbl->setText(QString::number(val));
}

void WhiteBalanceWidget::SetWBGreenLabel(int val)
{
    m_pWBGreenValueLbl->setText(QString::number(val));
}

void WhiteBalanceWidget::SetWBBlueLabel(int val)
{
    m_pWBBlueValueLbl->setText(QString::number(val));
}
