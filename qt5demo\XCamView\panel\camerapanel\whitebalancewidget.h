#ifndef WHITEBALANCEWIDGET_H
#define WHITEBALANCEWIDGET_H

#include <QPushButton>
#include <QButtonGroup>
#include <QRadioButton>
#include <QLabel>
#include <QSlider>
#include "../collapsewidget.h"

class WhiteBalanceWidget : public CollapseWidget
{
    Q_OBJECT
public:
    explicit WhiteBalanceWidget(QWidget* parent = nullptr);

    void Notify(int notify);

protected slots:
    void OnWBBGpClicked(int id);
    void OnWBRedChanged(int value);
    void OnWBGreenChanged(int value);
    void OnWBBlueChanged(int value);
    void OnDefBtnClicked();
    void OnCamEvent(int event);
    void OnIsHide(bool bHide);

private:
    void EnableBtns(bool bEnabled);
    void UpdateBtnsEnabled();
    void SetWBRedLabel(int val);
    void SetWBGreenLabel(int val);
    void SetWBBlueLabel(int val);

    QRadioButton*   m_pManualRb;
    QRadioButton*   m_pAutoRb;
    QRadioButton*   m_pRoiRb;
    QButtonGroup*   m_pWBBGp;
    QLabel*         m_pWBRedLbl;
    QLabel*         m_pWBRedValueLbl;
    QSlider*        m_pWBRedSlider;
    QLabel*         m_pWBGreenLbl;
    QLabel*         m_pWBGreenValueLbl;
    QSlider*        m_pWBGreenSlider;
    QLabel*         m_pWBBlueLbl;
    QLabel*         m_pWBBlueValueLbl;
    QSlider*        m_pWBBlueSlider;
    QPushButton*    m_pDefBtn;
};

#endif
