
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "E:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineSystem.cmake:205 (message)"
      - "CMakeLists.txt:3 (project)"
    message: |
      The system is: Windows - 10.0.19045 - AMD64
  -
    kind: "message-v1"
    backtrace:
      - "E:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "E:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "E:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler: D:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/HostX64/x64/cl.exe 
      Build flags: -DQT_QML_DEBUG
      Id flags:  
      
      The output was:
      0
      用于 x64 的 Microsoft (R) C/C++ 优化编译器 19.44.35211 版
      版权所有(C) Microsoft Corporation。保留所有权利。
      
      CMakeCXXCompilerId.cpp
      Microsoft (R) Incremental Linker Version 14.44.35211.0
      Copyright (C) Microsoft Corporation.  All rights reserved.
      
      /out:CMakeCXXCompilerId.exe 
      CMakeCXXCompilerId.obj 
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CMakeCXXCompilerId.exe"
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CMakeCXXCompilerId.obj"
      
      The CXX compiler identification is MSVC, found in:
        D:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Debug/CMakeFiles/3.30.5/CompilerIdCXX/CMakeCXXCompilerId.exe
      
  -
    kind: "message-v1"
    backtrace:
      - "E:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:1243 (message)"
      - "E:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:250 (CMAKE_DETERMINE_MSVC_SHOWINCLUDES_PREFIX)"
      - "E:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Detecting CXX compiler /showIncludes prefix:
        main.c
        注意: 包含文件:  D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Debug\\CMakeFiles\\ShowIncludes\\foo.h
        
      Found prefix "注意: 包含文件:  "
  -
    kind: "try_compile-v1"
    backtrace:
      - "E:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineCompilerABI.cmake:74 (try_compile)"
      - "E:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "D:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-cxicgw"
      binary: "D:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-cxicgw"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "-DQT_QML_DEBUG /DWIN32 /D_WINDOWS /GR /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_CXX_SCAN_FOR_MODULES: "OFF"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'D:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-cxicgw'
        
        Run Build Command(s): D:/PROGRA~2/MICROS~1/2022/COMMUN~1/Common7/IDE/COMMON~1/MICROS~1/CMake/Ninja/ninja.exe -v cmTC_1ca52
        [1/2] D:\\PROGRA~2\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\HostX64\\x64\\cl.exe  /nologo /TP   -DQT_QML_DEBUG /DWIN32 /D_WINDOWS /GR /EHsc  /Zi /Ob0 /Od /RTC1 -MDd /showIncludes /FoCMakeFiles\\cmTC_1ca52.dir\\CMakeCXXCompilerABI.cpp.obj /FdCMakeFiles\\cmTC_1ca52.dir\\ /FS -c E:\\Qt\\Tools\\CMake_64\\share\\cmake-3.30\\Modules\\CMakeCXXCompilerABI.cpp
        [2/2] C:\\Windows\\system32\\cmd.exe /C "cd . && E:\\Qt\\Tools\\CMake_64\\bin\\cmake.exe -E vs_link_exe --intdir=CMakeFiles\\cmTC_1ca52.dir --rc=D:\\WINDOW~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=D:\\WINDOW~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- D:\\PROGRA~2\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\link.exe /nologo CMakeFiles\\cmTC_1ca52.dir\\CMakeCXXCompilerABI.cpp.obj  /out:cmTC_1ca52.exe /implib:cmTC_1ca52.lib /pdb:cmTC_1ca52.pdb /version:0.0 /machine:x64  /debug /INCREMENTAL /subsystem:console  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "E:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineCompilerABI.cmake:218 (message)"
      - "E:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Parsed CXX implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(\\.[a-z]+)?|link\\.exe|lld-link(\\.exe)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(\\.[a-z]+)?|link\\.exe|lld-link(\\.exe)?))("|,| |$)]
        linker tool for 'CXX': D:/PROGRA~2/MICROS~1/2022/COMMUN~1/VC/Tools/MSVC/1444~1.352/bin/Hostx64/x64/link.exe
        implicit libs: []
        implicit objs: []
        implicit dirs: []
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "E:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/Internal/CMakeDetermineLinkerId.cmake:40 (message)"
      - "E:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineCompilerABI.cmake:255 (cmake_determine_linker_id)"
      - "E:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Running the CXX compiler's linker: "D:/PROGRA~2/MICROS~1/2022/COMMUN~1/VC/Tools/MSVC/1444~1.352/bin/Hostx64/x64/link.exe" "-v"
      Microsoft (R) Incremental Linker Version 14.44.35211.0
      Copyright (C) Microsoft Corporation.  All rights reserved.
  -
    kind: "try_compile-v1"
    backtrace:
      - "E:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/Internal/CheckSourceCompiles.cmake:101 (try_compile)"
      - "E:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CheckCXXSourceCompiles.cmake:52 (cmake_check_source_compiles)"
      - "E:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/FindThreads.cmake:99 (CHECK_CXX_SOURCE_COMPILES)"
      - "E:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/FindThreads.cmake:163 (_threads_check_libc)"
      - "build/Desktop_Qt_6_10_0_MSVC2022_64bit-Debug/.qtc/package-manager/maintenance_tool_provider.cmake:322 (find_package)"
      - "E:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeFindDependencyMacro.cmake:76 (find_package)"
      - "E:/Qt/6.10.0/msvc2022_64/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake:36 (find_dependency)"
      - "E:/Qt/6.10.0/msvc2022_64/lib/cmake/Qt6/Qt6Dependencies.cmake:35 (_qt_internal_find_third_party_dependencies)"
      - "E:/Qt/6.10.0/msvc2022_64/lib/cmake/Qt6/Qt6Config.cmake:172 (include)"
      - "build/Desktop_Qt_6_10_0_MSVC2022_64bit-Debug/.qtc/package-manager/maintenance_tool_provider.cmake:322 (find_package)"
      - "CMakeLists.txt:12 (find_package)"
    checks:
      - "Performing Test CMAKE_HAVE_LIBC_PTHREAD"
    directories:
      source: "D:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-96oukw"
      binary: "D:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-96oukw"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "-DQT_QML_DEBUG /DWIN32 /D_WINDOWS /GR /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "E:/Qt/6.10.0/msvc2022_64/lib/cmake/Qt6;E:/Qt/6.10.0/msvc2022_64/lib/cmake/Qt6/3rdparty/extra-cmake-modules/find-modules;E:/Qt/6.10.0/msvc2022_64/lib/cmake/Qt6/3rdparty/kwin"
    buildResult:
      variable: "CMAKE_HAVE_LIBC_PTHREAD"
      cached: true
      stdout: |
        Change Dir: 'D:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-96oukw'
        
        Run Build Command(s): D:/PROGRA~2/MICROS~1/2022/COMMUN~1/Common7/IDE/COMMON~1/MICROS~1/CMake/Ninja/ninja.exe -v cmTC_44f56
        [1/2] D:\\PROGRA~2\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\HostX64\\x64\\cl.exe  /nologo /TP -DCMAKE_HAVE_LIBC_PTHREAD  -DQT_QML_DEBUG /DWIN32 /D_WINDOWS /GR /EHsc  /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd /showIncludes /FoCMakeFiles\\cmTC_44f56.dir\\src.cxx.obj /FdCMakeFiles\\cmTC_44f56.dir\\ /FS -c D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Debug\\CMakeFiles\\CMakeScratch\\TryCompile-96oukw\\src.cxx
        \x1b[31mFAILED: \x1b[0mCMakeFiles/cmTC_44f56.dir/src.cxx.obj 
        D:\\PROGRA~2\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\HostX64\\x64\\cl.exe  /nologo /TP -DCMAKE_HAVE_LIBC_PTHREAD  -DQT_QML_DEBUG /DWIN32 /D_WINDOWS /GR /EHsc  /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd /showIncludes /FoCMakeFiles\\cmTC_44f56.dir\\src.cxx.obj /FdCMakeFiles\\cmTC_44f56.dir\\ /FS -c D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Debug\\CMakeFiles\\CMakeScratch\\TryCompile-96oukw\\src.cxx
        D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Debug\\CMakeFiles\\CMakeScratch\\TryCompile-96oukw\\src.cxx(1): fatal error C1083: 无法打开包括文件: “pthread.h”: No such file or directory
        ninja: build stopped: subcommand failed.
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "E:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CheckLibraryExists.cmake:69 (try_compile)"
      - "E:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/FindThreads.cmake:112 (CHECK_LIBRARY_EXISTS)"
      - "E:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/FindThreads.cmake:175 (_threads_check_lib)"
      - "build/Desktop_Qt_6_10_0_MSVC2022_64bit-Debug/.qtc/package-manager/maintenance_tool_provider.cmake:322 (find_package)"
      - "E:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeFindDependencyMacro.cmake:76 (find_package)"
      - "E:/Qt/6.10.0/msvc2022_64/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake:36 (find_dependency)"
      - "E:/Qt/6.10.0/msvc2022_64/lib/cmake/Qt6/Qt6Dependencies.cmake:35 (_qt_internal_find_third_party_dependencies)"
      - "E:/Qt/6.10.0/msvc2022_64/lib/cmake/Qt6/Qt6Config.cmake:172 (include)"
      - "build/Desktop_Qt_6_10_0_MSVC2022_64bit-Debug/.qtc/package-manager/maintenance_tool_provider.cmake:322 (find_package)"
      - "CMakeLists.txt:12 (find_package)"
    checks:
      - "Looking for pthread_create in pthreads"
    directories:
      source: "D:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-kfzuh7"
      binary: "D:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-kfzuh7"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "-DQT_QML_DEBUG /DWIN32 /D_WINDOWS /GR /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "E:/Qt/6.10.0/msvc2022_64/lib/cmake/Qt6;E:/Qt/6.10.0/msvc2022_64/lib/cmake/Qt6/3rdparty/extra-cmake-modules/find-modules;E:/Qt/6.10.0/msvc2022_64/lib/cmake/Qt6/3rdparty/kwin"
    buildResult:
      variable: "CMAKE_HAVE_PTHREADS_CREATE"
      cached: true
      stdout: |
        Change Dir: 'D:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-kfzuh7'
        
        Run Build Command(s): D:/PROGRA~2/MICROS~1/2022/COMMUN~1/Common7/IDE/COMMON~1/MICROS~1/CMake/Ninja/ninja.exe -v cmTC_788c3
        [1/2] D:\\PROGRA~2\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\HostX64\\x64\\cl.exe  /nologo /TP   -DQT_QML_DEBUG /DWIN32 /D_WINDOWS /GR /EHsc -DCHECK_FUNCTION_EXISTS=pthread_create /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd /showIncludes /FoCMakeFiles\\cmTC_788c3.dir\\CheckFunctionExists.cxx.obj /FdCMakeFiles\\cmTC_788c3.dir\\ /FS -c D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Debug\\CMakeFiles\\CMakeScratch\\TryCompile-kfzuh7\\CheckFunctionExists.cxx
        [2/2] C:\\Windows\\system32\\cmd.exe /C "cd . && E:\\Qt\\Tools\\CMake_64\\bin\\cmake.exe -E vs_link_exe --intdir=CMakeFiles\\cmTC_788c3.dir --rc=D:\\WINDOW~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=D:\\WINDOW~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- D:\\PROGRA~2\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\link.exe /nologo CMakeFiles\\cmTC_788c3.dir\\CheckFunctionExists.cxx.obj  /out:cmTC_788c3.exe /implib:cmTC_788c3.lib /pdb:cmTC_788c3.pdb /version:0.0 /machine:x64  /debug /INCREMENTAL /subsystem:console  pthreads.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        \x1b[31mFAILED: \x1b[0mcmTC_788c3.exe 
        C:\\Windows\\system32\\cmd.exe /C "cd . && E:\\Qt\\Tools\\CMake_64\\bin\\cmake.exe -E vs_link_exe --intdir=CMakeFiles\\cmTC_788c3.dir --rc=D:\\WINDOW~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=D:\\WINDOW~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- D:\\PROGRA~2\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\link.exe /nologo CMakeFiles\\cmTC_788c3.dir\\CheckFunctionExists.cxx.obj  /out:cmTC_788c3.exe /implib:cmTC_788c3.lib /pdb:cmTC_788c3.pdb /version:0.0 /machine:x64  /debug /INCREMENTAL /subsystem:console  pthreads.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        LINK Pass 1: command "D:\\PROGRA~2\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\link.exe /nologo CMakeFiles\\cmTC_788c3.dir\\CheckFunctionExists.cxx.obj /out:cmTC_788c3.exe /implib:cmTC_788c3.lib /pdb:cmTC_788c3.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console pthreads.lib kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTFILE:CMakeFiles\\cmTC_788c3.dir/intermediate.manifest CMakeFiles\\cmTC_788c3.dir/manifest.res" failed (exit code 1104) with the following output:
        LINK : fatal error LNK1104: 无法打开文件“pthreads.lib”\x0d
        ninja: build stopped: subcommand failed.
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "E:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CheckLibraryExists.cmake:69 (try_compile)"
      - "E:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/FindThreads.cmake:112 (CHECK_LIBRARY_EXISTS)"
      - "E:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/FindThreads.cmake:176 (_threads_check_lib)"
      - "build/Desktop_Qt_6_10_0_MSVC2022_64bit-Debug/.qtc/package-manager/maintenance_tool_provider.cmake:322 (find_package)"
      - "E:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeFindDependencyMacro.cmake:76 (find_package)"
      - "E:/Qt/6.10.0/msvc2022_64/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake:36 (find_dependency)"
      - "E:/Qt/6.10.0/msvc2022_64/lib/cmake/Qt6/Qt6Dependencies.cmake:35 (_qt_internal_find_third_party_dependencies)"
      - "E:/Qt/6.10.0/msvc2022_64/lib/cmake/Qt6/Qt6Config.cmake:172 (include)"
      - "build/Desktop_Qt_6_10_0_MSVC2022_64bit-Debug/.qtc/package-manager/maintenance_tool_provider.cmake:322 (find_package)"
      - "CMakeLists.txt:12 (find_package)"
    checks:
      - "Looking for pthread_create in pthread"
    directories:
      source: "D:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-cipd21"
      binary: "D:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-cipd21"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "-DQT_QML_DEBUG /DWIN32 /D_WINDOWS /GR /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "E:/Qt/6.10.0/msvc2022_64/lib/cmake/Qt6;E:/Qt/6.10.0/msvc2022_64/lib/cmake/Qt6/3rdparty/extra-cmake-modules/find-modules;E:/Qt/6.10.0/msvc2022_64/lib/cmake/Qt6/3rdparty/kwin"
    buildResult:
      variable: "CMAKE_HAVE_PTHREAD_CREATE"
      cached: true
      stdout: |
        Change Dir: 'D:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-cipd21'
        
        Run Build Command(s): D:/PROGRA~2/MICROS~1/2022/COMMUN~1/Common7/IDE/COMMON~1/MICROS~1/CMake/Ninja/ninja.exe -v cmTC_68235
        [1/2] D:\\PROGRA~2\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\HostX64\\x64\\cl.exe  /nologo /TP   -DQT_QML_DEBUG /DWIN32 /D_WINDOWS /GR /EHsc -DCHECK_FUNCTION_EXISTS=pthread_create /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd /showIncludes /FoCMakeFiles\\cmTC_68235.dir\\CheckFunctionExists.cxx.obj /FdCMakeFiles\\cmTC_68235.dir\\ /FS -c D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Debug\\CMakeFiles\\CMakeScratch\\TryCompile-cipd21\\CheckFunctionExists.cxx
        [2/2] C:\\Windows\\system32\\cmd.exe /C "cd . && E:\\Qt\\Tools\\CMake_64\\bin\\cmake.exe -E vs_link_exe --intdir=CMakeFiles\\cmTC_68235.dir --rc=D:\\WINDOW~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=D:\\WINDOW~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- D:\\PROGRA~2\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\link.exe /nologo CMakeFiles\\cmTC_68235.dir\\CheckFunctionExists.cxx.obj  /out:cmTC_68235.exe /implib:cmTC_68235.lib /pdb:cmTC_68235.pdb /version:0.0 /machine:x64  /debug /INCREMENTAL /subsystem:console  pthread.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        \x1b[31mFAILED: \x1b[0mcmTC_68235.exe 
        C:\\Windows\\system32\\cmd.exe /C "cd . && E:\\Qt\\Tools\\CMake_64\\bin\\cmake.exe -E vs_link_exe --intdir=CMakeFiles\\cmTC_68235.dir --rc=D:\\WINDOW~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=D:\\WINDOW~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- D:\\PROGRA~2\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\link.exe /nologo CMakeFiles\\cmTC_68235.dir\\CheckFunctionExists.cxx.obj  /out:cmTC_68235.exe /implib:cmTC_68235.lib /pdb:cmTC_68235.pdb /version:0.0 /machine:x64  /debug /INCREMENTAL /subsystem:console  pthread.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        LINK Pass 1: command "D:\\PROGRA~2\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\link.exe /nologo CMakeFiles\\cmTC_68235.dir\\CheckFunctionExists.cxx.obj /out:cmTC_68235.exe /implib:cmTC_68235.lib /pdb:cmTC_68235.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console pthread.lib kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTFILE:CMakeFiles\\cmTC_68235.dir/intermediate.manifest CMakeFiles\\cmTC_68235.dir/manifest.res" failed (exit code 1104) with the following output:
        LINK : fatal error LNK1104: 无法打开文件“pthread.lib”\x0d
        ninja: build stopped: subcommand failed.
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "E:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/Internal/CheckSourceCompiles.cmake:101 (try_compile)"
      - "E:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CheckCXXSourceCompiles.cmake:52 (cmake_check_source_compiles)"
      - "E:/Qt/6.10.0/msvc2022_64/lib/cmake/Qt6/FindWrapAtomic.cmake:36 (check_cxx_source_compiles)"
      - "build/Desktop_Qt_6_10_0_MSVC2022_64bit-Debug/.qtc/package-manager/maintenance_tool_provider.cmake:322 (find_package)"
      - "E:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeFindDependencyMacro.cmake:76 (find_package)"
      - "E:/Qt/6.10.0/msvc2022_64/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake:36 (find_dependency)"
      - "E:/Qt/6.10.0/msvc2022_64/lib/cmake/Qt6Core/Qt6CoreDependencies.cmake:35 (_qt_internal_find_third_party_dependencies)"
      - "E:/Qt/6.10.0/msvc2022_64/lib/cmake/Qt6Core/Qt6CoreConfig.cmake:42 (include)"
      - "build/Desktop_Qt_6_10_0_MSVC2022_64bit-Debug/.qtc/package-manager/maintenance_tool_provider.cmake:315 (find_package)"
      - "E:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeFindDependencyMacro.cmake:76 (find_package)"
      - "E:/Qt/6.10.0/msvc2022_64/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake:142 (find_dependency)"
      - "E:/Qt/6.10.0/msvc2022_64/lib/cmake/Qt6Widgets/Qt6WidgetsDependencies.cmake:45 (_qt_internal_find_qt_dependencies)"
      - "E:/Qt/6.10.0/msvc2022_64/lib/cmake/Qt6Widgets/Qt6WidgetsConfig.cmake:40 (include)"
      - "build/Desktop_Qt_6_10_0_MSVC2022_64bit-Debug/.qtc/package-manager/maintenance_tool_provider.cmake:297 (find_package)"
      - "CMakeLists.txt:13 (find_package)"
    checks:
      - "Performing Test HAVE_STDATOMIC"
    directories:
      source: "D:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-diytdm"
      binary: "D:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-diytdm"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "-DQT_QML_DEBUG /DWIN32 /D_WINDOWS /GR /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "E:/Qt/6.10.0/msvc2022_64/lib/cmake/Qt6;E:/Qt/6.10.0/msvc2022_64/lib/cmake/Qt6/3rdparty/extra-cmake-modules/find-modules;E:/Qt/6.10.0/msvc2022_64/lib/cmake/Qt6/3rdparty/kwin"
    buildResult:
      variable: "HAVE_STDATOMIC"
      cached: true
      stdout: |
        Change Dir: 'D:/work/XCamView/XCamView/build/Desktop_Qt_6_10_0_MSVC2022_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-diytdm'
        
        Run Build Command(s): D:/PROGRA~2/MICROS~1/2022/COMMUN~1/Common7/IDE/COMMON~1/MICROS~1/CMake/Ninja/ninja.exe -v cmTC_59f0b
        [1/2] D:\\PROGRA~2\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\HostX64\\x64\\cl.exe  /nologo /TP -DHAVE_STDATOMIC  -DQT_QML_DEBUG /DWIN32 /D_WINDOWS /GR /EHsc  /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd /showIncludes /FoCMakeFiles\\cmTC_59f0b.dir\\src.cxx.obj /FdCMakeFiles\\cmTC_59f0b.dir\\ /FS -c D:\\work\\XCamView\\XCamView\\build\\Desktop_Qt_6_10_0_MSVC2022_64bit-Debug\\CMakeFiles\\CMakeScratch\\TryCompile-diytdm\\src.cxx
        [2/2] C:\\Windows\\system32\\cmd.exe /C "cd . && E:\\Qt\\Tools\\CMake_64\\bin\\cmake.exe -E vs_link_exe --intdir=CMakeFiles\\cmTC_59f0b.dir --rc=D:\\WINDOW~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=D:\\WINDOW~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- D:\\PROGRA~2\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\link.exe /nologo CMakeFiles\\cmTC_59f0b.dir\\src.cxx.obj  /out:cmTC_59f0b.exe /implib:cmTC_59f0b.lib /pdb:cmTC_59f0b.pdb /version:0.0 /machine:x64  /debug /INCREMENTAL /subsystem:console  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
...
