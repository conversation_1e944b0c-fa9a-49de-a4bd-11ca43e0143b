cmake_minimum_required(VERSION 3.16)

project(TpDatabase LANGUAGES CXX)

set(CMAKE_AUTOUIC ON)
set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTORCC ON)
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 查找Qt组件
find_package(QT NAMES Qt6 Qt5 REQUIRED COMPONENTS Core Sql)
find_package(Qt${QT_VERSION_MAJOR} REQUIRED COMPONENTS Core Sql)

# 源文件列表
set(TPDATABASE_SOURCES
    TpDatabase.cpp
    TpDatabaseResult.cpp
    TpDatabaseConfig.cpp
    TpDatabaseConnection.cpp
)

# 头文件列表
set(TPDATABASE_HEADERS
    TpDatabase.h
    TpDatabaseResult.h
    TpDatabaseConfig.h
    TpDatabaseConnection.h
)

# 创建共享库
add_library(TpDatabase SHARED
    ${TPDATABASE_SOURCES}
    ${TPDATABASE_HEADERS}
)

# 设置库属性
set_target_properties(TpDatabase PROPERTIES
    DEBUG_POSTFIX "d"
    VERSION 1.0.0
    SOVERSION 1
)

# 链接Qt库
target_link_libraries(TpDatabase PRIVATE 
    Qt${QT_VERSION_MAJOR}::Core
    Qt${QT_VERSION_MAJOR}::Sql
)

# 编译定义
target_compile_definitions(TpDatabase PRIVATE TPDATABASE_LIBRARY)

# 包含目录
target_include_directories(TpDatabase PUBLIC
    $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}>
    $<INSTALL_INTERFACE:include/TpDatabase>
)

# Qt5/Qt6兼容性处理
if(${QT_VERSION_MAJOR} GREATER_EQUAL 6)
    # Qt6特定设置
    target_compile_definitions(TpDatabase PRIVATE QT_VERSION_MAJOR=6)
else()
    # Qt5特定设置
    target_compile_definitions(TpDatabase PRIVATE QT_VERSION_MAJOR=5)
endif()

# 编译器特定设置
if(MSVC)
    # Windows MSVC编译器设置
    target_compile_options(TpDatabase PRIVATE /utf-8)
    target_compile_definitions(TpDatabase PRIVATE 
        _CRT_SECURE_NO_WARNINGS
        NOMINMAX
    )
elseif(CMAKE_CXX_COMPILER_ID STREQUAL "GNU" OR CMAKE_CXX_COMPILER_ID STREQUAL "Clang")
    # GCC/Clang编译器设置
    target_compile_options(TpDatabase PRIVATE 
        -Wall 
        -Wextra 
        -Wpedantic
    )
endif()

# 安装规则（可选）
include(GNUInstallDirs)

install(TARGETS TpDatabase
    EXPORT TpDatabaseTargets
    LIBRARY DESTINATION ${CMAKE_INSTALL_LIBDIR}
    ARCHIVE DESTINATION ${CMAKE_INSTALL_LIBDIR}
    RUNTIME DESTINATION ${CMAKE_INSTALL_BINDIR}
    INCLUDES DESTINATION ${CMAKE_INSTALL_INCLUDEDIR}
)

install(FILES ${TPDATABASE_HEADERS}
    DESTINATION ${CMAKE_INSTALL_INCLUDEDIR}/TpDatabase
)

# 导出配置（可选）
install(EXPORT TpDatabaseTargets
    FILE TpDatabaseTargets.cmake
    NAMESPACE TpDatabase::
    DESTINATION ${CMAKE_INSTALL_LIBDIR}/cmake/TpDatabase
)

# 创建配置文件（可选）
include(CMakePackageConfigHelpers)
write_basic_package_version_file(
    TpDatabaseConfigVersion.cmake
    VERSION 1.0.0
    COMPATIBILITY AnyNewerVersion
)

configure_package_config_file(
    "${CMAKE_CURRENT_SOURCE_DIR}/TpDatabaseConfig.cmake.in"
    "${CMAKE_CURRENT_BINARY_DIR}/TpDatabaseConfig.cmake"
    INSTALL_DESTINATION ${CMAKE_INSTALL_LIBDIR}/cmake/TpDatabase
)

install(FILES
    "${CMAKE_CURRENT_BINARY_DIR}/TpDatabaseConfig.cmake"
    "${CMAKE_CURRENT_BINARY_DIR}/TpDatabaseConfigVersion.cmake"
    DESTINATION ${CMAKE_INSTALL_LIBDIR}/cmake/TpDatabase
)
