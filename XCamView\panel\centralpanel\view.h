#ifndef VIEW_H
#define VIEW_H

#include <QWidget>
#include <QScrollBar>
#include <QImage>
#include <QSize>

class ViewWidget : public QWidget
{
    Q_OBJECT
public:
    explicit ViewWidget(QWidget* parent = nullptr);
    ~ViewWidget();

    virtual void Notify(int notify);

    void SetOffsetX(int x);
    void SetOffsetY(int y);
    QPoint GetOffsetPt() const;
    void SetOffsetPt(const QPoint &pt);

    double GetScale() const;
    void SetScale(double scale);

    QSize GetImageSize();
    QSize GetWinowSize();

    QRgb GetImageValue(int x, int y);

    void SetDefCurShape(Qt::CursorShape defCurShape);

    void FitToWinWidth();
    void FitToWinHeight();
    void FitToWindow();

signals:
    void UpdateScrollBar();
    void ScaleChanged(double scale);

protected:
    void paintEvent(QPaintEvent *event) override;
    void wheelEvent(QWheelEvent *event) override;

    virtual void paint(QPainter &painter);
    QPointF MapToImageCoord(const QPointF& pt);
    void DoZoomChanged();
    virtual void DoZoomChangedExt();

    QImage*           m_pImage;
    QPoint            m_ptCenter;
    double            m_fScale;
    QPoint            m_ptOffSet;
    Qt::CursorShape   m_defCurShape;
};

class View : public QWidget
{
    Q_OBJECT
public:
    explicit View(ViewWidget* pViewWidget, QWidget* parent = nullptr);

    virtual void Notify(int notify);

protected:
    void resizeEvent(QResizeEvent *event) override;
    void mousePressEvent(QMouseEvent *event) override;
    void mouseReleaseEvent(QMouseEvent *event) override;
    void mouseMoveEvent(QMouseEvent *event) override;

protected slots:
    void OnUpdateScrollBar();
    void OnHorScrollBarChanged(int x);
    void OnVerScrollBarChanged(int y);

protected:
    void UpdateCursor();

    QScrollBar  *m_pHorScrollBar;
    QScrollBar  *m_pVerScrollBar;
    ViewWidget  *m_pViewWidget;
    QPoint       m_begPt;
    bool         m_bLeftPressed;
};

#endif
