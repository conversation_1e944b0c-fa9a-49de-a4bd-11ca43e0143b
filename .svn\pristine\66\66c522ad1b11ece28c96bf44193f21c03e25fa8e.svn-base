#ifndef TPLOG_H
#define TPLOG_H

#include <QString>

#if defined(TPLOG_LIBRARY)
#define TPLOG_EXPORT Q_DECL_EXPORT
#else
#define TPLOG_EXPORT Q_DECL_IMPORT
#endif

enum LogLevel {
    Log_Debug,
    Log_Info,
    Log_Warning,
    Log_Error,
    Log_Critical
};

TPLOG_EXPORT void LogInit();
TPLOG_EXPORT void Log(LogLevel level, const char* format, ...);
TPLOG_EXPORT void Log(LogLevel level, const QString& str);

#endif // TPLOG_H
