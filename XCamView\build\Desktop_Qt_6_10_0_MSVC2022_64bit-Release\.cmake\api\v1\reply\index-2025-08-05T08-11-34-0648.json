{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "E:/Qt/Tools/CMake_64/bin/cmake.exe", "cpack": "E:/Qt/Tools/CMake_64/bin/cpack.exe", "ctest": "E:/Qt/Tools/CMake_64/bin/ctest.exe", "root": "E:/Qt/Tools/CMake_64/share/cmake-3.30"}, "version": {"isDirty": false, "major": 3, "minor": 30, "patch": 5, "string": "3.30.5", "suffix": ""}}, "objects": [{"jsonFile": "codemodel-v2-1dbeebe961d16f341d5d.json", "kind": "codemodel", "version": {"major": 2, "minor": 7}}, {"jsonFile": "cache-v2-752151b64de96da60c64.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-f0921422a85dc469b62a.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 1}}], "reply": {"cache-v2": {"jsonFile": "cache-v2-752151b64de96da60c64.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-f0921422a85dc469b62a.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 1}}, "codemodel-v2": {"jsonFile": "codemodel-v2-1dbeebe961d16f341d5d.json", "kind": "codemodel", "version": {"major": 2, "minor": 7}}}}