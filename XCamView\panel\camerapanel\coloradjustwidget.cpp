#include "coloradjustwidget.h"
#include "../../mainwindow.h"
#include <QGridLayout>

ColorAdjustWidget::ColorAdjustWidget(QWidget* parent)
    : CollapseWidget(QIcon(":/images/color.png"), g_i18n->value("ids_coloradjust"), parent)
{
    m_pHueLbl = new QLabel(g_i18n->value("ids_hue"));
    m_pHueValueLbl = new QLabel("0");
    m_pHueValueLbl->setAlignment(Qt::AlignRight);
    m_pHueSlider = new QSlider(Qt::Horizontal);
    m_pSaturationLbl = new QLabel(g_i18n->value("ids_saturation"));
    m_pSaturationValueLbl = new QLabel("0");
    m_pSaturationValueLbl->setAlignment(Qt::AlignRight);
    m_pSaturationSlider = new QSlider(Qt::Horizontal);
    m_pBrightnessLbl = new QLabel(g_i18n->value("ids_brightness"));
    m_pBrightnessValueLbl = new QLabel("0");
    m_pBrightnessValueLbl->setAlignment(Qt::AlignRight);
    m_pBrightnessSlider = new QSlider(Qt::Horizontal);
    m_pContrastLbl = new QLabel(g_i18n->value("ids_contrast"));
    m_pContrastValueLbl = new QLabel("0");
    m_pContrastValueLbl->setAlignment(Qt::AlignRight);
    m_pContrastSlider = new QSlider(Qt::Horizontal);
    m_pGammaLbl = new QLabel(g_i18n->value("ids_gamma"));
    m_pGammaValueLbl = new QLabel("0");
    m_pGammaValueLbl->setAlignment(Qt::AlignRight);
    m_pGammaSlider = new QSlider(Qt::Horizontal);
    m_pDefBtn = new QPushButton(g_i18n->value("ids_defaults"));

    QGridLayout *mainLyt = new QGridLayout();
    mainLyt->addWidget(m_pHueLbl, 1, 0);
    mainLyt->addWidget(m_pHueValueLbl, 1, 1);
    mainLyt->addWidget(m_pHueSlider, 2, 0, 1, 2);
    mainLyt->addWidget(m_pSaturationLbl, 3, 0);
    mainLyt->addWidget(m_pSaturationValueLbl, 3, 1);
    mainLyt->addWidget(m_pSaturationSlider, 4, 0, 1, 2);
    mainLyt->addWidget(m_pBrightnessLbl, 5, 0);
    mainLyt->addWidget(m_pBrightnessValueLbl, 5, 1);
    mainLyt->addWidget(m_pBrightnessSlider, 6, 0, 1, 2);
    mainLyt->addWidget(m_pContrastLbl, 7, 0);
    mainLyt->addWidget(m_pContrastValueLbl, 7, 1);
    mainLyt->addWidget(m_pContrastSlider, 8, 0, 1, 2);
    mainLyt->addWidget(m_pGammaLbl, 9, 0);
    mainLyt->addWidget(m_pGammaValueLbl, 9, 1);
    mainLyt->addWidget(m_pGammaSlider, 10, 0, 1, 2);
    mainLyt->addWidget(m_pDefBtn, 11, 0, 1, 2, Qt::AlignCenter);
    setLayout(mainLyt);

    connect(m_pHueSlider, SIGNAL(valueChanged(int)), this, SLOT(OnHueChanged(int)));
    connect(m_pSaturationSlider, SIGNAL(valueChanged(int)), this, SLOT(OnSaturationChanged(int)));
    connect(m_pBrightnessSlider, SIGNAL(valueChanged(int)), this, SLOT(OnBrightnessChanged(int)));
    connect(m_pContrastSlider, SIGNAL(valueChanged(int)), this, SLOT(OnContrastChanged(int)));
    connect(m_pGammaSlider, SIGNAL(valueChanged(int)), this, SLOT(OnGammaChanged(int)));
    connect(m_pDefBtn, SIGNAL(clicked()), this, SLOT(OnDefBtnClicked()));

    EnableBtns(false);
}

void ColorAdjustWidget::Notify(int notify)
{
    if(NOTIFY_CAM_OPEN == notify)
    {
        EnableBtns(true);
        m_pHueSlider->blockSignals(true);
        m_pSaturationSlider->blockSignals(true);
        m_pBrightnessSlider->blockSignals(true);
        m_pContrastSlider->blockSignals(true);
        m_pGammaSlider->blockSignals(true);

        int min = 0, max = 0, val = 0;
        g_pCameraManager->GetParaRange(CAM_PARA_HUE, &min, &max, nullptr);
        m_pHueSlider->setRange(min, max);
        g_pCameraManager->GetParaRange(CAM_PARA_SATURATION, &min, &max, nullptr);
        m_pSaturationSlider->setRange(min, max);
        g_pCameraManager->GetParaRange(CAM_PARA_BRIGHTNESS, &min, &max, nullptr);
        m_pBrightnessSlider->setRange(min, max);
        g_pCameraManager->GetParaRange(CAM_PARA_CONTRAST, &min, &max, nullptr);
        m_pContrastSlider->setRange(min, max);
        g_pCameraManager->GetParaRange(CAM_PARA_GAMMA, &min, &max, nullptr);
        m_pGammaSlider->setRange(min, max);

        g_pCameraManager->GetPara(CAM_PARA_HUE, &val);
        m_pHueSlider->setValue(val);
        SetHueLabel(val);
        g_pCameraManager->GetPara(CAM_PARA_SATURATION, &val);
        m_pSaturationSlider->setValue(val);
        SetSaturationLabel(val);
        g_pCameraManager->GetPara(CAM_PARA_BRIGHTNESS, &val);
        m_pBrightnessSlider->setValue(val);
        SetBrightnessLabel(val);
        g_pCameraManager->GetPara(CAM_PARA_CONTRAST, &val);
        m_pContrastSlider->setValue(val);
        SetContrastLabel(val);
        g_pCameraManager->GetPara(CAM_PARA_GAMMA, &val);
        m_pGammaSlider->setValue(val);
        SetGammaLabel(val);

        m_pHueSlider->blockSignals(false);
        m_pSaturationSlider->blockSignals(false);
        m_pBrightnessSlider->blockSignals(false);
        m_pContrastSlider->blockSignals(false);
        m_pGammaSlider->blockSignals(false);
    }
    else if(NOTIFY_CAM_CLOSE == notify)
    {
        EnableBtns(false);
    }
}

void ColorAdjustWidget::OnHueChanged(int value)
{
    int cur = 0;
    g_pCameraManager->GetPara(CAM_PARA_HUE, &cur);
    if(cur != value)
    {
        g_pCameraManager->PutPara(CAM_PARA_HUE, value);
        SetHueLabel(value);
    }
}

void ColorAdjustWidget::OnSaturationChanged(int value)
{
    int cur = 0;
    g_pCameraManager->GetPara(CAM_PARA_SATURATION, &cur);
    if(cur != value)
    {
        g_pCameraManager->PutPara(CAM_PARA_SATURATION, value);
        SetSaturationLabel(value);
    }
}

void ColorAdjustWidget::OnBrightnessChanged(int value)
{
    int cur = 0;
    g_pCameraManager->GetPara(CAM_PARA_BRIGHTNESS, &cur);
    if(cur != value)
    {
        g_pCameraManager->PutPara(CAM_PARA_BRIGHTNESS, value);
        SetBrightnessLabel(value);
    }
}

void ColorAdjustWidget::OnContrastChanged(int value)
{
    int cur = 0;
    g_pCameraManager->GetPara(CAM_PARA_CONTRAST, &cur);
    if(cur != value)
    {
        g_pCameraManager->PutPara(CAM_PARA_CONTRAST, value);
        SetContrastLabel(value);
    }
}

void ColorAdjustWidget::OnGammaChanged(int value)
{
    int cur = 0;
    g_pCameraManager->GetPara(CAM_PARA_GAMMA, &cur);
    if(cur != value)
    {
        g_pCameraManager->PutPara(CAM_PARA_GAMMA, value);
        SetGammaLabel(value);
    }
}

void ColorAdjustWidget::OnDefBtnClicked()
{
    int def = 0;
    g_pCameraManager->GetParaRange(CAM_PARA_HUE, nullptr, nullptr, &def);
    m_pHueSlider->setValue(def);
    g_pCameraManager->GetParaRange(CAM_PARA_SATURATION, nullptr, nullptr, &def);
    m_pSaturationSlider->setValue(def);
    g_pCameraManager->GetParaRange(CAM_PARA_BRIGHTNESS, nullptr, nullptr, &def);
    m_pBrightnessSlider->setValue(def);
    g_pCameraManager->GetParaRange(CAM_PARA_CONTRAST, nullptr, nullptr, &def);
    m_pContrastSlider->setValue(def);
    g_pCameraManager->GetParaRange(CAM_PARA_GAMMA, nullptr, nullptr, &def);
    m_pGammaSlider->setValue(def);
}

void ColorAdjustWidget::EnableBtns(bool bEnabled)
{
    m_pHueSlider->setEnabled(bEnabled);
    m_pSaturationSlider->setEnabled(bEnabled);
    m_pBrightnessSlider->setEnabled(bEnabled);
    m_pContrastSlider->setEnabled(bEnabled);
    m_pGammaSlider->setEnabled(bEnabled);
    m_pDefBtn->setEnabled(bEnabled);
}

void ColorAdjustWidget::SetHueLabel(int val)
{
    m_pHueValueLbl->setText(QString::number(val));
}

void ColorAdjustWidget::SetSaturationLabel(int val)
{
    m_pSaturationValueLbl->setText(QString::number(val));
}

void ColorAdjustWidget::SetBrightnessLabel(int val)
{
    m_pBrightnessValueLbl->setText(QString::number(val));
}

void ColorAdjustWidget::SetContrastLabel(int val)
{
    m_pContrastValueLbl->setText(QString::number(val));
}

void ColorAdjustWidget::SetGammaLabel(int val)
{
    m_pGammaValueLbl->setText(QString::number(val));
}
