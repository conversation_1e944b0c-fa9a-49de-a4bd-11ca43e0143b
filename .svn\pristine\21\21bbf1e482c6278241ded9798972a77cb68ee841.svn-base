#ifndef PREVIEWVIEW_H
#define PREVIEWVIEW_H

#include "displayview.h"
#include <QImage>

class AuxRect;

class PreviewWidget : public DisplayWidget
{
    Q_OBJECT
public:
    explicit PreviewWidget(QWidget* parent = nullptr);

    void Notify(int notify) override;

private slots:
    void OnCamEvent(int event);
    void OnCapture();

private:
    QImage m_captureImg;
};

class PreviewView : public DisplayView
{
	Q_OBJECT
public:
    explicit PreviewView(QWidget* parent = nullptr);
};

#endif
