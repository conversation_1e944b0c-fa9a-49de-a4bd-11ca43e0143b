#include "monitorview.h"
#include "../../../mainwindow.h"
#include <QPainter>
#include <QPaintEvent>
#include <QGridLayout>

MonitorViewItem::MonitorViewItem(QWidget* parent)
    : QWidget(parent)
{

}

MonitorViewItem::~MonitorViewItem()
{

}

void MonitorViewItem::Notify(int notify)
{
    Q_UNUSED(notify);
}

void MonitorViewItem::SetName(const QString &name)
{
    m_name = name;
}

void MonitorViewItem::paintEvent(QPaintEvent *event)
{
    QPainter painter(this);
    painter.fillRect(event->rect(), QColor(255, 255, 255));
}

void MonitorViewItem::mouseDoubleClickEvent(QMouseEvent *event)
{
    if(event->button() == Qt::LeftButton)
        g_pMainWindow->ShowMonitorDialog(m_name);
    QWidget::mouseDoubleClickEvent(event);
}

MonitorView::MonitorView(QWidget *parent)
    : QWidget(parent)
{
    QGridLayout* mainLyt = new QGridLayout();

    for(int i = 0; i < 9; ++i)
    {
        m_pViewItemList.append(new MonitorViewItem(this));
        m_pViewItemList[i]->SetName(QString::number(i + 1));
        mainLyt->addWidget(m_pViewItemList[i], i / 3, i % 3);
    }

    setLayout(mainLyt);
}

void MonitorView::Notify(int notify)
{
    for(int i = 0; i < m_pViewItemList.count(); ++i)
        m_pViewItemList[i]->Notify(notify);
}
