﻿#ifndef CAMERALISTWIDGET_H
#define CAMERALISTWIDGET_H

#include <QButtonGroup>
#include <QVBoxLayout>
#include <QFrame>
#include "../collapsewidget.h"

class CamListBtn;

class CameraListWidget : public CollapseWidget
{
    Q_OBJECT
public:
    explicit CameraListWidget(QWidget* parent = nullptr);

    void Notify(int notify);

private slots:
    void OnButtonGroupClicked(int id);

private:
    void HotPlug();

    QList<CamListBtn*> btns_;
    QButtonGroup* buttonGroup_;
    QVBoxLayout* mainLyt_;
    QList<QFrame*> hFrames_;
};

#endif
