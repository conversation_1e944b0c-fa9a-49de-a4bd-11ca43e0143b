#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include <QMainWindow>
#include <QMenu>
#include <QAction>
#include <QActionGroup>
#include <QToolBar>
#include <QToolButton>
#include <QDockWidget>
#include <QComboBox>

class TpTabWidget;
class LeftPanel;
class MonitorDialog;
class TpCalibrationDlg;

class MainWindow : public QMainWindow
{
    Q_OBJECT

public:
    MainWindow(QWidget *parent = nullptr);
    ~MainWindow();

    void Notify(int notify);
    void ShowMonitorDialog(const QString& title);
    void SetZoomScale(double scale);
    double GetZoomScale();

public slots:
    void OnCamEvent(int event);
    void OnNotify(int notify);
    void OnTabCloseRequested(int index);
    void OnZoomScaleActived(int index);
    void OnMeasureTriggered(QAction* action);
    void OnMagnificationActived(int index);
    void OnUnitActived(int index);

    void OnCalibrationClicked();
    void OnCalibrationComplete(const Calibration& calibration);
    void OnCalibrationApply(const double& resolution);
    void OnCalibrationLength(double length);

    void OnTextEditDlg();

    void OnCapture();

protected:
    bool nativeEvent(const QByteArray&, void*, long*);
    void closeEvent(QCloseEvent *event);
    bool eventFilter(QObject* watched, QEvent* event);

private:
    MonitorDialog* GetMonitorDlgFromList(const QString &title);
    void InitZoomComboBox();

    void EnableCalibration(bool bEnabled);
    void UpdateMeasure(const Calibration& calibration);

    QMenu*       m_pMenuFile;
    QAction*     m_pActionOpen;
    QAction*     m_pActionSave;
    QAction*     m_pActionQuickSave;
    QAction*     m_pActionPreference;
    QMenu*       m_pMenuMeasure;
    QAction*     m_pActionSelect;
    QAction*     m_pActionPoint;
    QAction*     m_pActionArrow;
    QToolButton* m_pToolBtnAngle;
    QAction*     m_pActionAngle3pt;
    QAction*     m_pActionAngle4pt;
    QToolButton* m_pToolBtnLine;
    QAction*     m_pActionArbline;
    QAction*     m_pActionHLine;
    QAction*     m_pActionVLine;
    QToolButton* m_pToolBtnParallel;
    QAction*     m_pActionParallel4pt;
    QAction*     m_pActionParallel8pt;
    QToolButton* m_pToolBtnVertical;
    QAction*     m_pActionVertical3pt;
    QAction*     m_pActionVertical4pt;
    QAction*     m_pActionRectangle;
    QAction*     m_pActionEllipse;
    QToolButton* m_pToolBtnCircle;
    QAction*     m_pActionCircleCR;
    QAction*     m_pActionCircle2pt;
    QAction*     m_pActionCircle3pt;
    QToolButton* m_pToolBtnTwoCircles;
    QAction*     m_pActionTwoCirclesCR;
    QAction*     m_pActionTwoCircles3pt;
    QAction*     m_pActionAnnulus;
    QAction*     m_pActionArc;
    QAction*     m_pActionCurve;
    QAction*     m_pActionPolygon;
    QAction*     m_pActionText;
    QAction*     m_pActionScaleBar;
    QAction*     m_pActionDeleteMeasure;
    QAction*     m_pActionCalibration;
    QActionGroup* m_pActionGpMeasures;
    QMenu*       m_pMenuTeaching;
    QAction*     m_pActionSameScreen;
    QAction*     m_pActionWhiteboard;
    QAction*     m_pActionBlackScreen;
    QMenu*       m_pMenuHelp;
    QAction*     m_pActionAbout;

    QToolBar*    m_pToolBar;
    QComboBox*   m_pZoomScaleCB;
    QComboBox*   m_pMagCB;
    QComboBox*   m_pUnitCB;

    LeftPanel*   m_pLeftPanel;
    TpTabWidget* m_pTabWidget;
    TpCalibrationDlg* m_pCaliDlg;

    QList<MonitorDialog*> m_monitorDlgList;
};

extern MainWindow* g_pMainWindow;

#endif // MAINWINDOW_H
