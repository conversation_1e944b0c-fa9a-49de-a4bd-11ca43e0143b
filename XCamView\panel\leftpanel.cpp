#include "leftpanel.h"
#include "camerapanel/camerapanel.h"
#include "monitorpanel/monitorwidget.h"
#include "chatpanel/chatwidget.h"

LeftPanel::LeftPanel(QWidget *parent)
    : QDockWidget(parent)
{
    setMinimumWidth(g_pFontMetrics->averageCharWidth() * 50);
    setAllowedAreas(Qt::LeftDockWidgetArea);
    setFeatures(QDockWidget::DockWidgetClosable);

    m_pCameraPanel = new CameraPanel();
    m_pMonitorWidget = new MonitorWidget();
    m_pChatWidget = new ChatWidget();

    m_pTabWidget = new QTabWidget(this);
    m_pTabWidget->setTabPosition(QTabWidget::South);
    m_pTabWidget->addTab(m_pCameraPanel, g_i18n->value("ids_camera"));
    m_pTabWidget->addTab(m_pMonitorWidget, g_i18n->value("ids_monitor"));
    m_pTabWidget->addTab(m_pChatWidget, g_i18n->value("ids_chat_room"));
    setWidget(m_pTabWidget);
}

void LeftPanel::Notify(int notify)
{
    m_pCameraPanel->Notify(notify);
    m_pMonitorWidget->Notify(notify);
}
