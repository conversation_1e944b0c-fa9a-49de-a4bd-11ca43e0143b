#include "mainwindow.h"
#include "panel/login/logindialog.h"
#include <QApplication>

int main(int argc, char *argv[])
{
    QApplication a(argc, argv);
    LogInit();
    I18n i18n;
    i18n.init();
    LoginDialog loginDlg;
    if(loginDlg.exec() != QDialog::Accepted)
    {
        return 0;
    }
    CameraManager camera;
    if(!camera.InitLoader())
        return -1;
    g_pFontMetrics = new QFontMetrics(QApplication::font());
    g_pMainWindow = new MainWindow();
    g_pMainWindow->setWindowState(Qt::WindowMaximized);
    g_pMainWindow->show();
    return a.exec();
}
