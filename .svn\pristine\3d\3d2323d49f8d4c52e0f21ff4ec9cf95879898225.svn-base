#include "previewview.h"
#include "../../module/auxrect/auxrect.h"
#include "../../../mainwindow.h"
#include "measure/tpmeasuremanager.h"
#include <QPainter>

PreviewWidget::PreviewWidget(QWidget* parent)
    : DisplayWidget(parent, true)
{
    connect(g_pCameraManager, SIGNAL(CamEvent(int)), this, SLOT(OnCamEvent(int)));
    connect(g_pSignalConnect, SIGNAL(Capture()), this, SLOT(OnCapture()));
}

void PreviewWidget::Notify(int notify)
{
    if(NOTIFY_CAM_OPEN == notify || NOTIFY_CAM_RES == notify)
    {
        const int width = g_pCameraManager->GetImageWidth();
        const int height = g_pCameraManager->GetImageHeight();
        if(m_pImage && (m_pImage->width() != width || m_pImage->height() != height))
        {
            delete m_pImage;
            m_pImage = nullptr;
        }
        if(nullptr == m_pImage)
        {
            m_pImage = new QImage(width, height, QImage::Format_RGB888);
            m_pAWBRoiRect->setMaxW(width);
            m_pAWBRoiRect->setMaxH(height);
        }
        m_ptCenter = m_pImage->rect().center();
        m_pMeasureManager->setMSize(width, height);

        int WB_x = 0, WB_y = 0, WB_width = 0, WB_height = 0;
        g_pCameraManager->GetPara(CAM_PARA_WBROILEFT, &WB_x);
        g_pCameraManager->GetPara(CAM_PARA_WBROITOP, &WB_y);
        g_pCameraManager->GetPara(CAM_PARA_WBROIWIDTH, &WB_width);
        g_pCameraManager->GetPara(CAM_PARA_WBROIHEIGHT, &WB_height);
        m_pAWBRoiRect->setAuxRect(QRectF(WB_x, WB_y, WB_width, WB_height));
        DoZoomChanged();
    }
    else if(NOTIFY_CAM_CLOSE == notify)
    {
        if(m_pImage)
        {
            delete m_pImage;
            m_pImage = nullptr;
        }
    }
    else if(NOTIFY_WBROI_VISIBLE == notify)
    {
        m_pAWBRoiRect->setHidden(g_bWBDlgHidden || (WB_ROI != g_WBMode));
    }
    else if(NOTIFY_ZOOM_CHANGED == notify)
    {
        DoZoomChanged();
    }
    else if(NOTIFY_MEASURE_CHANGED == notify)
    {
        UpdateMeasureType();
    }
}

void PreviewWidget::OnCamEvent(int event)
{
    if(CAM_EVENT_IMAGE == event)
    {
        if(m_pImage && isVisible())
        {
            g_pCameraManager->GetImage(m_pImage->bits());
            update();
        }
    }
}

void PreviewWidget::OnCapture()
{
    m_captureImg = *m_pImage;
    if(m_pMeasureManager && m_pMeasureManager->hasShape())
    {
        QPainter painter(&m_captureImg);
        painter.translate(m_captureImg.rect().center());
        m_pMeasureManager->paint(painter, true);
    }
    g_pMainWindow->OnCapture(&m_captureImg);
}

PreviewView::PreviewView(QWidget *parent)
    : DisplayView(new PreviewWidget(), parent)
{

}
