/****************************************************************************
** Meta object code from reading C++ file 'exposuregainwidget.h'
**
** Created by: The Qt Meta Object Compiler version 69 (Qt 6.10.0)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../../../panel/camerapanel/exposuregainwidget.h"
#include <QtGui/qtextcursor.h>
#include <QtCore/qmetatype.h>

#include <QtCore/qtmochelpers.h>

#include <memory>


#include <QtCore/qxptype_traits.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'exposuregainwidget.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 69
#error "This file was generated using the moc from 6.10.0. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {
struct qt_meta_tag_ZN18ExposureGainWidgetE_t {};
} // unnamed namespace

template <> constexpr inline auto ExposureGainWidget::qt_create_metaobjectdata<qt_meta_tag_ZN18ExposureGainWidgetE_t>()
{
    namespace QMC = QtMocConstants;
    QtMocHelpers::StringRefStorage qt_stringData {
        "ExposureGainWidget",
        "OnAECkbChanged",
        "",
        "state",
        "OnExpTargetChanged",
        "value",
        "OnExpTimeChanged",
        "OnExpTimeValueLabelClicked",
        "OnExpGainChanged",
        "OnExpGainValueLabelClicked",
        "OnDefBtnClicked",
        "OnCamEvent",
        "event"
    };

    QtMocHelpers::UintData qt_methods {
        // Slot 'OnAECkbChanged'
        QtMocHelpers::SlotData<void(int)>(1, 2, QMC::AccessProtected, QMetaType::Void, {{
            { QMetaType::Int, 3 },
        }}),
        // Slot 'OnExpTargetChanged'
        QtMocHelpers::SlotData<void(int)>(4, 2, QMC::AccessProtected, QMetaType::Void, {{
            { QMetaType::Int, 5 },
        }}),
        // Slot 'OnExpTimeChanged'
        QtMocHelpers::SlotData<void(int)>(6, 2, QMC::AccessProtected, QMetaType::Void, {{
            { QMetaType::Int, 5 },
        }}),
        // Slot 'OnExpTimeValueLabelClicked'
        QtMocHelpers::SlotData<void()>(7, 2, QMC::AccessProtected, QMetaType::Void),
        // Slot 'OnExpGainChanged'
        QtMocHelpers::SlotData<void(int)>(8, 2, QMC::AccessProtected, QMetaType::Void, {{
            { QMetaType::Int, 5 },
        }}),
        // Slot 'OnExpGainValueLabelClicked'
        QtMocHelpers::SlotData<void()>(9, 2, QMC::AccessProtected, QMetaType::Void),
        // Slot 'OnDefBtnClicked'
        QtMocHelpers::SlotData<void()>(10, 2, QMC::AccessProtected, QMetaType::Void),
        // Slot 'OnCamEvent'
        QtMocHelpers::SlotData<void(int)>(11, 2, QMC::AccessProtected, QMetaType::Void, {{
            { QMetaType::Int, 12 },
        }}),
    };
    QtMocHelpers::UintData qt_properties {
    };
    QtMocHelpers::UintData qt_enums {
    };
    return QtMocHelpers::metaObjectData<ExposureGainWidget, qt_meta_tag_ZN18ExposureGainWidgetE_t>(QMC::MetaObjectFlag{}, qt_stringData,
            qt_methods, qt_properties, qt_enums);
}
Q_CONSTINIT const QMetaObject ExposureGainWidget::staticMetaObject = { {
    QMetaObject::SuperData::link<CollapseWidget::staticMetaObject>(),
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN18ExposureGainWidgetE_t>.stringdata,
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN18ExposureGainWidgetE_t>.data,
    qt_static_metacall,
    nullptr,
    qt_staticMetaObjectRelocatingContent<qt_meta_tag_ZN18ExposureGainWidgetE_t>.metaTypes,
    nullptr
} };

void ExposureGainWidget::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    auto *_t = static_cast<ExposureGainWidget *>(_o);
    if (_c == QMetaObject::InvokeMetaMethod) {
        switch (_id) {
        case 0: _t->OnAECkbChanged((*reinterpret_cast<std::add_pointer_t<int>>(_a[1]))); break;
        case 1: _t->OnExpTargetChanged((*reinterpret_cast<std::add_pointer_t<int>>(_a[1]))); break;
        case 2: _t->OnExpTimeChanged((*reinterpret_cast<std::add_pointer_t<int>>(_a[1]))); break;
        case 3: _t->OnExpTimeValueLabelClicked(); break;
        case 4: _t->OnExpGainChanged((*reinterpret_cast<std::add_pointer_t<int>>(_a[1]))); break;
        case 5: _t->OnExpGainValueLabelClicked(); break;
        case 6: _t->OnDefBtnClicked(); break;
        case 7: _t->OnCamEvent((*reinterpret_cast<std::add_pointer_t<int>>(_a[1]))); break;
        default: ;
        }
    }
}

const QMetaObject *ExposureGainWidget::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *ExposureGainWidget::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_staticMetaObjectStaticContent<qt_meta_tag_ZN18ExposureGainWidgetE_t>.strings))
        return static_cast<void*>(this);
    return CollapseWidget::qt_metacast(_clname);
}

int ExposureGainWidget::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = CollapseWidget::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 8)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 8;
    }
    if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 8)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 8;
    }
    return _id;
}
QT_WARNING_POP
