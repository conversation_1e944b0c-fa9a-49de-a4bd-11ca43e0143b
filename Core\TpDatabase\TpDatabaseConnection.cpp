#include "TpDatabaseConnection.h"
#include <QApplication>
#include <QThread>
#include <QDateTime>
#include <QDebug>
#include <QMutexLocker>

// 静态成员初始化
int TpDatabaseConnection::s_connectionCounter = 0;
QMutex TpDatabaseConnection::s_counterMutex;

TpDatabaseConnection::TpDatabaseConnection(const TpDatabaseConfig& config, QObject *parent)
    : QObject(parent)
    , m_config(config)
    , m_connectionState(TpDatabaseConnectionState::NotConnected)
    , m_connectionName(generateConnectionName())
    , m_healthCheckTimer(new QTimer(this))
    , m_reconnectTimer(new QTimer(this))
    , m_healthCheckEnabled(true)
    , m_reconnectAttempts(0)
    , m_queryCount(0)
    , m_errorCount(0)
{
    qDebug() << "TpDatabaseConnection: 初始化数据库连接，连接名:" << m_connectionName;
    
    // 设置定时器
    m_healthCheckTimer->setSingleShot(false);
    m_reconnectTimer->setSingleShot(true);
    
    // 连接信号槽
    connect(m_healthCheckTimer, &QTimer::timeout, this, &TpDatabaseConnection::onHealthCheckTimer);
    connect(m_reconnectTimer, &QTimer::timeout, this, &TpDatabaseConnection::onReconnectTimer);
    
    qDebug() << "TpDatabaseConnection: 配置 -" << m_config.formatConnectionString();
}

TpDatabaseConnection::TpDatabaseConnection(QObject *parent)
    : TpDatabaseConnection(TpDatabaseConfig::createDefault(), parent)
{
    // 尝试从文件加载配置
    m_config.loadFromFile();
    m_config.loadFromEnvironment();
}

TpDatabaseConnection::~TpDatabaseConnection()
{
    disconnectFromDatabase();
}

TpDatabaseResult TpDatabaseConnection::connectToDatabase()
{
    QMutexLocker locker(&m_mutex);
    
    if (m_connectionState == TpDatabaseConnectionState::Connected) {
        qDebug() << "TpDatabaseConnection: 数据库已连接";
        return TpDatabaseResult::success();
    }
    
    if (m_connectionState == TpDatabaseConnectionState::Connecting) {
        return TpDatabaseResult::error(TpDatabaseErrorCode::ConnectionFailed, "正在连接中，请等待");
    }
    
    setState(TpDatabaseConnectionState::Connecting);
    logConnectionAttempt();
    
    // 创建连接
    TpDatabaseResult createResult = createConnection();
    if (!createResult.isSuccess()) {
        setState(TpDatabaseConnectionState::ConnectionFailed);
        return createResult;
    }
    
    // 尝试打开连接
    if (!m_database.open()) {
        TpDatabaseResult errorResult = TpDatabaseResult::fromSqlError(m_database.lastError());
        handleConnectionError(m_database.lastError());
        setState(TpDatabaseConnectionState::ConnectionFailed);
        return errorResult;
    }
    
    // 测试连接
    TpDatabaseResult testResult = performHealthCheck();
    if (!testResult.isSuccess()) {
        setState(TpDatabaseConnectionState::ConnectionFailed);
        return testResult;
    }
    
    // 连接成功
    setState(TpDatabaseConnectionState::Connected);
    m_connectionTime = QDateTime::currentDateTime();
    m_reconnectAttempts = 0;
    
    // 启动健康检查
    if (m_healthCheckEnabled) {
        startHealthCheckTimer();
    }
    
    logConnectionResult(true);
    emit connectionRestored();
    
    return TpDatabaseResult::success();
}

void TpDatabaseConnection::disconnectFromDatabase()
{
    QMutexLocker locker(&m_mutex);
    
    if (m_connectionState == TpDatabaseConnectionState::NotConnected) {
        return;
    }
    
    setState(TpDatabaseConnectionState::Disconnecting);
    
    // 停止定时器
    stopHealthCheckTimer();
    stopReconnectTimer();
    
    // 关闭数据库连接
    if (m_database.isOpen()) {
        m_database.close();
        qDebug() << "TpDatabaseConnection: 数据库连接已关闭";
    }
    
    // 移除连接
    if (QSqlDatabase::contains(m_connectionName)) {
        QSqlDatabase::removeDatabase(m_connectionName);
        qDebug() << "TpDatabaseConnection: 数据库连接已移除:" << m_connectionName;
    }
    
    setState(TpDatabaseConnectionState::NotConnected);
}

bool TpDatabaseConnection::isConnected() const
{
    QMutexLocker locker(&m_mutex);
    return m_connectionState == TpDatabaseConnectionState::Connected && m_database.isOpen();
}

TpDatabaseConnectionState TpDatabaseConnection::connectionState() const
{
    QMutexLocker locker(&m_mutex);
    return m_connectionState;
}

TpDatabaseResult TpDatabaseConnection::testConnection()
{
    QMutexLocker locker(&m_mutex);
    return performHealthCheck();
}

TpDatabaseConfig TpDatabaseConnection::config() const
{
    QMutexLocker locker(&m_mutex);
    return m_config;
}

TpDatabaseResult TpDatabaseConnection::setConfig(const TpDatabaseConfig& config)
{
    QMutexLocker locker(&m_mutex);
    
    if (!config.isValid()) {
        return TpDatabaseResult::error(TpDatabaseErrorCode::ConfigError, "无效的数据库配置");
    }
    
    // 如果已连接，先断开
    if (isConnected()) {
        locker.unlock();
        disconnectFromDatabase();
        locker.relock();
    }
    
    m_config = config;
    qDebug() << "TpDatabaseConnection: 配置已更新 -" << m_config.formatConnectionString();
    
    return TpDatabaseResult::success();
}

TpDatabaseResult TpDatabaseConnection::reloadConfig(const QString& configPath)
{
    QMutexLocker locker(&m_mutex);
    
    TpDatabaseConfig newConfig = m_config;
    
    if (!newConfig.loadFromFile(configPath)) {
        return TpDatabaseResult::error(TpDatabaseErrorCode::ConfigError, "配置文件加载失败");
    }
    
    // 加载环境变量配置
    newConfig.loadFromEnvironment();
    
    locker.unlock();
    return setConfig(newConfig);
}

QSqlDatabase& TpDatabaseConnection::database()
{
    // 注意：此方法不是线程安全的，调用者需要自行处理同步
    return m_database;
}

QSqlDatabase TpDatabaseConnection::getDatabase() const
{
    QMutexLocker locker(&m_mutex);
    return QSqlDatabase::database(m_connectionName);
}

QString TpDatabaseConnection::connectionName() const
{
    QMutexLocker locker(&m_mutex);
    return m_connectionName;
}

TpDatabaseResult TpDatabaseConnection::validateUser(const QString& username, const QString& password)
{
    QMutexLocker locker(&m_mutex);
    
    if (!isConnected()) {
        return TpDatabaseResult::error(TpDatabaseErrorCode::ConnectionFailed, "数据库未连接");
    }
    
    QSqlQuery query(m_database);
    query.prepare("SELECT id FROM admin WHERE username = :username AND password = :password");
    query.bindValue(":username", username);
    query.bindValue(":password", password);
    
    if (!query.exec()) {
        m_errorCount++;
        QString errorMsg = QString("用户验证SQL执行失败: %1").arg(query.lastError().text());
        qWarning() << "TpDatabaseConnection:" << errorMsg;
        return TpDatabaseResult::error(TpDatabaseErrorCode::QueryFailed, errorMsg);
    }
    
    m_queryCount++;
    
    if (query.next()) {
        int userId = query.value(0).toInt();
        qDebug() << "TpDatabaseConnection: 用户验证成功，用户ID:" << userId;
        return TpDatabaseResult::success(userId);
    }
    
    qDebug() << "TpDatabaseConnection: 用户验证失败，用户名:" << username;
    return TpDatabaseResult::error(TpDatabaseErrorCode::ValidationError, "用户名或密码错误");
}

void TpDatabaseConnection::setHealthCheckEnabled(bool enabled)
{
    QMutexLocker locker(&m_mutex);
    
    if (m_healthCheckEnabled == enabled) {
        return;
    }
    
    m_healthCheckEnabled = enabled;
    
    if (enabled && m_connectionState == TpDatabaseConnectionState::Connected) {
        startHealthCheckTimer();
    } else {
        stopHealthCheckTimer();
    }
    
    qDebug() << "TpDatabaseConnection: 健康检查" << (enabled ? "已启用" : "已禁用");
}

bool TpDatabaseConnection::isHealthCheckEnabled() const
{
    QMutexLocker locker(&m_mutex);
    return m_healthCheckEnabled;
}

QString TpDatabaseConnection::lastError() const
{
    QMutexLocker locker(&m_mutex);
    return m_lastError;
}

QVariantMap TpDatabaseConnection::getConnectionStats() const
{
    QMutexLocker locker(&m_mutex);
    
    QVariantMap stats;
    stats["connectionName"] = m_connectionName;
    stats["connectionState"] = static_cast<int>(m_connectionState);
    stats["connectionTime"] = m_connectionTime;
    stats["lastHealthCheck"] = m_lastHealthCheck;
    stats["reconnectAttempts"] = m_reconnectAttempts;
    stats["queryCount"] = m_queryCount;
    stats["errorCount"] = m_errorCount;
    stats["healthCheckEnabled"] = m_healthCheckEnabled;
    
    return stats;
}

QMutex& TpDatabaseConnection::mutex() const
{
    return m_mutex;
}

void TpDatabaseConnection::onHealthCheckTimer()
{
    QMutexLocker locker(&m_mutex);
    
    if (m_connectionState != TpDatabaseConnectionState::Connected) {
        return;
    }
    
    TpDatabaseResult result = performHealthCheck();
    if (!result.isSuccess()) {
        qWarning() << "TpDatabaseConnection: 健康检查失败，开始重连";
        setState(TpDatabaseConnectionState::Reconnecting);
        emit connectionLost();
        
        locker.unlock();
        startReconnectTimer();
    }
}

void TpDatabaseConnection::onReconnectTimer()
{
    qDebug() << "TpDatabaseConnection: 尝试重新连接...";
    
    TpDatabaseResult result = connectToDatabase();
    if (!result.isSuccess()) {
        m_reconnectAttempts++;
        qWarning() << "TpDatabaseConnection: 重连失败，尝试次数:" << m_reconnectAttempts;
        
        // 指数退避重连
        int delay = qMin(1000 * (1 << m_reconnectAttempts), 60000); // 最大60秒
        m_reconnectTimer->start(delay);
    } else {
        qDebug() << "TpDatabaseConnection: 重连成功";
    }
}

QString TpDatabaseConnection::generateConnectionName()
{
    QMutexLocker locker(&s_counterMutex);
    return QString("TpDatabase_Connection_%1_%2")
           .arg(QApplication::applicationPid())
           .arg(++s_connectionCounter);
}

TpDatabaseResult TpDatabaseConnection::createConnection()
{
    // 移除已存在的连接
    if (QSqlDatabase::contains(m_connectionName)) {
        QSqlDatabase existingDb = QSqlDatabase::database(m_connectionName);
        if (existingDb.isOpen()) {
            existingDb.close();
        }
        QSqlDatabase::removeDatabase(m_connectionName);
        qDebug() << "TpDatabaseConnection: 移除已存在的连接:" << m_connectionName;
    }

    // 创建新连接
    m_database = QSqlDatabase::addDatabase(m_config.driverName(), m_connectionName);

    if (!m_database.isValid()) {
        QString errorMsg = QString("无法创建数据库连接，驱动: %1").arg(m_config.driverName());
        setLastError(errorMsg);
        qCritical() << "TpDatabaseConnection:" << errorMsg;
        return TpDatabaseResult::error(TpDatabaseErrorCode::ConnectionFailed, errorMsg);
    }

    // 设置连接参数
    m_database.setHostName(m_config.hostName());
    m_database.setPort(m_config.port());
    m_database.setDatabaseName(m_config.databaseName());
    m_database.setUserName(m_config.userName());
    m_database.setPassword(m_config.password());

    // 设置连接选项
    if (!m_config.connectOptions().isEmpty()) {
        m_database.setConnectOptions(m_config.connectOptions());
    }

    qDebug() << "TpDatabaseConnection: 数据库连接对象创建成功";
    return TpDatabaseResult::success();
}

TpDatabaseResult TpDatabaseConnection::performHealthCheck()
{
    if (!m_database.isOpen()) {
        return TpDatabaseResult::error(TpDatabaseErrorCode::ConnectionFailed, "数据库连接未打开");
    }

    QSqlQuery testQuery(m_database);

    // 使用简单的测试查询
    if (!testQuery.exec("SELECT 1 as test")) {
        QString errorMsg = QString("健康检查失败: %1").arg(testQuery.lastError().text());
        setLastError(errorMsg);
        qWarning() << "TpDatabaseConnection:" << errorMsg;
        return TpDatabaseResult::error(TpDatabaseErrorCode::QueryFailed, errorMsg);
    }

    if (testQuery.next()) {
        m_lastHealthCheck = QDateTime::currentDateTime();
        qDebug() << "TpDatabaseConnection: 健康检查成功";

        // 尝试获取更多信息（如果失败也不影响健康检查）
        QSqlQuery infoQuery(m_database);
        if (infoQuery.exec("SELECT NOW() as server_time, USER() as current_user")) {
            if (infoQuery.next()) {
                qDebug() << "  - 服务器时间:" << infoQuery.value("server_time").toString();
                qDebug() << "  - 当前用户:" << infoQuery.value("current_user").toString();
            }
        }

        return TpDatabaseResult::success();
    }

    QString errorMsg = "健康检查查询无结果";
    setLastError(errorMsg);
    return TpDatabaseResult::error(TpDatabaseErrorCode::QueryFailed, errorMsg);
}

void TpDatabaseConnection::handleConnectionError(const QSqlError& error)
{
    setLastError(error.text());
    m_errorCount++;

    qCritical() << "TpDatabaseConnection: 数据库连接失败";
    qCritical() << "  - 错误信息:" << error.text();
    qCritical() << "  - 错误类型:" << error.type();
    qCritical() << "  - 错误代码:" << error.nativeErrorCode();

    // 分析错误并提供解决方案
    QString errorMsg = error.text();
    if (errorMsg.contains("Access denied") && errorMsg.contains("@")) {
        QRegExp rx("'[^']*'@'([^']*)'");
        if (rx.indexIn(errorMsg) != -1) {
            QString actualHostname = rx.cap(1);
            qCritical() << "TpDatabaseConnection: MySQL反向DNS解析问题";
            qCritical() << "  - MySQL识别的主机名:" << actualHostname;
            qCritical() << "  - 解决方案1: CREATE USER '" + m_config.userName() + "'@'%' IDENTIFIED BY '" + m_config.password() + "';";
            qCritical() << "  - 解决方案2: CREATE USER '" + m_config.userName() + "'@'" + actualHostname + "' IDENTIFIED BY '" + m_config.password() + "';";
            qCritical() << "  - 解决方案3: 在MySQL配置中添加 skip-name-resolve";
        }
    } else if (errorMsg.contains("Can't connect")) {
        qCritical() << "TpDatabaseConnection: 网络连接问题";
        qCritical() << "  - 检查MySQL服务器是否运行";
        qCritical() << "  - 检查网络连通性: ping" << m_config.hostName();
        qCritical() << "  - 检查端口是否开放: telnet" << m_config.hostName() << m_config.port();
    } else if (errorMsg.contains("Unknown database")) {
        qCritical() << "TpDatabaseConnection: 数据库不存在";
        qCritical() << "  - 检查数据库名称:" << m_config.databaseName();
    }

    emit errorOccurred(m_lastError);
}

void TpDatabaseConnection::startHealthCheckTimer()
{
    if (m_config.poolConfig().enableHealthCheck && m_config.poolConfig().healthCheckInterval > 0) {
        m_healthCheckTimer->start(m_config.poolConfig().healthCheckInterval);
        qDebug() << "TpDatabaseConnection: 健康检查定时器已启动，间隔:" << m_config.poolConfig().healthCheckInterval << "ms";
    }
}

void TpDatabaseConnection::stopHealthCheckTimer()
{
    if (m_healthCheckTimer->isActive()) {
        m_healthCheckTimer->stop();
        qDebug() << "TpDatabaseConnection: 健康检查定时器已停止";
    }
}

void TpDatabaseConnection::startReconnectTimer()
{
    if (!m_reconnectTimer->isActive()) {
        int delay = qMin(1000 * (1 << m_reconnectAttempts), 60000); // 指数退避，最大60秒
        m_reconnectTimer->start(delay);
        qDebug() << "TpDatabaseConnection: 重连定时器已启动，延迟:" << delay << "ms";
    }
}

void TpDatabaseConnection::stopReconnectTimer()
{
    if (m_reconnectTimer->isActive()) {
        m_reconnectTimer->stop();
        qDebug() << "TpDatabaseConnection: 重连定时器已停止";
    }
}

void TpDatabaseConnection::logConnectionAttempt() const
{
    qDebug() << "TpDatabaseConnection: 尝试连接数据库";
    qDebug() << "  - 连接字符串:" << m_config.formatConnectionString();
    qDebug() << "  - 连接名:" << m_connectionName;
}

void TpDatabaseConnection::logConnectionResult(bool success) const
{
    if (success) {
        qDebug() << "TpDatabaseConnection: 数据库连接成功!";
    } else {
        qCritical() << "TpDatabaseConnection: 数据库连接失败!";
    }
}

void TpDatabaseConnection::updateConnectionStats()
{
    // 更新统计信息的方法，可以在需要时调用
    // 目前统计信息在各个操作中直接更新
}

void TpDatabaseConnection::setState(TpDatabaseConnectionState state)
{
    if (m_connectionState != state) {
        TpDatabaseConnectionState oldState = m_connectionState;
        m_connectionState = state;

        qDebug() << "TpDatabaseConnection: 状态变化:"
                 << static_cast<int>(oldState) << "->" << static_cast<int>(state);

        emit connectionStateChanged(state);
    }
}

void TpDatabaseConnection::setLastError(const QString& error)
{
    m_lastError = error;
}
