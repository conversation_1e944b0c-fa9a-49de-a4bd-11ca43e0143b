#include "logindialog.h"
#include <QHBoxLayout>
#include <QGridLayout>
#include <QVBoxLayout>
#include <QMessageBox>
#include <QDebug>

LoginDialog::LoginDialog(QWidget *parent)
    : QDialog(parent), m_db<PERSON>and<PERSON>(this)  // 初始化数据库处理器
{
    // 设置窗口标题和属性
    setWindowTitle("XCamView - 登录");
    setWindowFlags(windowFlags() & ~Qt::WindowContextHelpButtonHint);
    setFixedSize(350, 280); // 设置固定窗口大小

    // 创建用户名输入组件
    m_pNameLabel = new QLabel("用户名:");
    m_pNameLineEdit = new QLineEdit();
    m_pNameLineEdit->setPlaceholderText("请输入用户名");
    
    // 创建密码输入组件
    m_pPasswordLabel = new QLabel("密码:");
    m_pPasswordLineEdit = new QLineEdit();
    m_pPasswordLineEdit->setEchoMode(QLineEdit::Password);
    m_pPasswordLineEdit->setPlaceholderText("请输入密码");
    
    // 创建用户类型选择组件
    m_pUserTypeLabel = new QLabel("用户类型:");   m_pTeacherRadio = new QRadioButton("教师端");   m_pStudentRadio = new QRadioButton("学生端");
    
    // 创建按钮组，确保只能选择一个选项
    m_pUserTypeGroup = new QButtonGroup(this);
    m_pUserTypeGroup->addButton(m_pTeacherRadio, 0);
    m_pUserTypeGroup->addButton(m_pStudentRadio, 1); m_pTeacherRadio->setChecked(true); // 默认选择教师端
    
    // 创建按钮
    m_pOKBtn = new QPushButton("登录");
    m_pOKBtn->setMinimumHeight(35);
    m_pCancelBtn = new QPushButton("取消");
    m_pCancelBtn->setMinimumHeight(35);

    // 创建用户类型选择的水平布局
    QHBoxLayout* userTypeLayout = new QHBoxLayout();
    userTypeLayout->addWidget(m_pTeacherRadio);
    userTypeLayout->addWidget(m_pStudentRadio);
    userTypeLayout->addStretch();

    // 创建按钮的水平布局
    QHBoxLayout* btnLayout = new QHBoxLayout();
    btnLayout->addStretch(1);
    btnLayout->addWidget(m_pOKBtn);
    btnLayout->addWidget(m_pCancelBtn);
    btnLayout->addStretch(1);

    // 创建主布局
    QGridLayout* mainLayout = new QGridLayout();
    mainLayout->setSpacing(15); // 设置组件间距
    mainLayout->setContentsMargins(30, 30, 30, 30); // 设置边距
    
    // 添加组件到布局
    mainLayout->addWidget(m_pNameLabel, 0, 0);
    mainLayout->addWidget(m_pNameLineEdit, 0, 1);
    mainLayout->addWidget(m_pPasswordLabel, 1, 0);
    mainLayout->addWidget(m_pPasswordLineEdit, 1, 1);
    mainLayout->addWidget(m_pUserTypeLabel, 2, 0);
    mainLayout->addLayout(userTypeLayout, 2, 1);
    mainLayout->addLayout(btnLayout, 3, 0, 1, 2);
    
    // 设置列拉伸
    mainLayout->setColumnStretch(1, 1);

    setLayout(mainLayout);

    // 连接信号和槽
    connect(m_pOKBtn, &QPushButton::clicked, this, &LoginDialog::onLogin);
    connect(m_pCancelBtn, &QPushButton::clicked, this, &QDialog::reject);
    
    // 支持回车键登录
    connect(m_pNameLineEdit, &QLineEdit::returnPressed, this, &LoginDialog::onLogin);
    connect(m_pPasswordLineEdit, &QLineEdit::returnPressed, this, &LoginDialog::onLogin);

    // 初始化数据库连接
    if (!m_dbHandler.connectToDatabase()) {
        QMessageBox::critical(this, "数据库错误",
                          "无法连接到数据库，请检查设置。");
    }
}

// 获取用户类型：0-教师端，1-学生端
int LoginDialog::getUserType() const
{
    return m_pUserTypeGroup->checkedId();
}

// 登录处理函数
void LoginDialog::onLogin()
{
    QString username = m_pNameLineEdit->text().trimmed();
    QString password = m_pPasswordLineEdit->text();
    int userType = getUserType();

    // 基本验证
    if (username.isEmpty() || password.isEmpty()) {
        QMessageBox::warning(this, "输入错误", "用户名和密码不能为空。");
        return;
    }

    // 使用数据库验证
    if (m_dbHandler.validateUser(username, password)) {
        QString userTypeStr = (userType == 0) ? "教师端" : "学生端";
        qDebug() << "登录成功 - 用户:" << username << "类型:" << userTypeStr;
        
        // 可以在这里根据用户类型进行不同的处理
        if (userType == 0) {
            // 教师端登录逻辑
            qDebug() << "教师端登录，拥有完整权限";
        } else {
            // 学生端登录逻辑
            qDebug() << "学生端登录，拥有受限权限";
        }
        
        accept(); // 关闭对话框
        emit loginSuccess(); // 发出登录成功信号
    } else {
        QMessageBox::critical(this, "登录失败", "用户名或密码错误！");
        m_pPasswordLineEdit->clear();
        m_pPasswordLineEdit->setFocus();
    }
} 