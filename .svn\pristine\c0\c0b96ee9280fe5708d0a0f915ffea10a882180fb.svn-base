zlib:
cmake -G "Visual Studio 17 2022" -A Win32 -DCMAKE_INSTALL_PREFIX=E:\work\01Camera\01ToupHdmi_dev\001Demo\demoApp\XCamView\ThirdParties\zlib-1.3.1\bin\x86 ..

cmake -G "Visual Studio 17 2022" -A x64 -DCMAKE_INSTALL_PREFIX=E:\work\01Camera\01ToupHdmi_dev\001Demo\demoApp\XCamView\ThirdParties\zlib-1.3.1\bin\x64 ..

cmake --build . --config Release --target install

expat:
cmake -G "Visual Studio 17 2022" -A Win32 -DCMAKE_INSTALL_PREFIX=E:\work\01Camera\01ToupHdmi_dev\001Demo\demoApp\XCamView\ThirdParties\expat-2.2.4\bin\x86 ..

cmake -G "Visual Studio 17 2022" -A x64 -DCMAKE_INSTALL_PREFIX=E:\work\01Camera\01ToupHdmi_dev\001Demo\demoApp\XCamView\ThirdParties\expat-2.2.4\bin\x64 ..

cmake -G "Visual Studio 17 2022" -A Win32 -DCMAKE_INSTALL_PREFIX=E:\work\01Camera\01ToupHdmi_dev\001Demo\demoApp\XCamView\ThirdParties\expat-2.7.1\bin\x86 ..

cmake -G "Visual Studio 17 2022" -A x64 -DCMAKE_INSTALL_PREFIX=E:\work\01Camera\01ToupHdmi_dev\001Demo\demoApp\XCamView\ThirdParties\expat-2.7.1\bin\x64 ..

cmake --build . --config Release --target install

exiv2:
cmake -G "Visual Studio 17 2022" -A Win32 -DCMAKE_INSTALL_PREFIX=E:\work\01Camera\01ToupHdmi_dev\001Demo\demoApp\XCamView\ThirdParties\exiv2-trunk\bin\x86 -DZLIB_INCLUDE_DIR=E:\work\01Camera\01ToupHdmi_dev\001Demo\demoApp\XCamView\ThirdParties\lib\zlib\include -DZLIB_LIBRARY=E:\work\01Camera\01ToupHdmi_dev\001Demo\demoApp\XCamView\ThirdParties\lib\zlib\x86\zlib.lib -DEXPAT_INCLUDE_DIR=E:\work\01Camera\01ToupHdmi_dev\001Demo\demoApp\XCamView\ThirdParties\lib\expat\include -DEXPAT_LIBRARY=E:\work\01Camera\01ToupHdmi_dev\001Demo\demoApp\XCamView\ThirdParties\lib\expat\x86\libexpat.lib -DEXV_UNICODE_PATH=ON ..

cmake -G "Visual Studio 17 2022" -A x64 -DCMAKE_INSTALL_PREFIX=E:\work\01Camera\01ToupHdmi_dev\001Demo\demoApp\XCamView\ThirdParties\exiv2-trunk\bin\x64 -DZLIB_INCLUDE_DIR=E:\work\01Camera\01ToupHdmi_dev\001Demo\demoApp\XCamView\ThirdParties\lib\zlib\include -DZLIB_LIBRARY=E:\work\01Camera\01ToupHdmi_dev\001Demo\demoApp\XCamView\ThirdParties\lib\zlib\x64\zlib.lib -DEXPAT_INCLUDE_DIR=E:\work\01Camera\01ToupHdmi_dev\001Demo\demoApp\XCamView\ThirdParties\lib\expat\include -DEXPAT_LIBRARY=E:\work\01Camera\01ToupHdmi_dev\001Demo\demoApp\XCamView\ThirdParties\lib\expat\x64\libexpat.lib -DEXV_UNICODE_PATH=ON ..

cmake -G "Visual Studio 17 2022" -A Win32 -DCMAKE_INSTALL_PREFIX=E:\work\01Camera\01ToupHdmi_dev\001Demo\demoApp\XCamView\ThirdParties\exiv2-main\bin\x86 -DZLIB_INCLUDE_DIR=E:\work\01Camera\01ToupHdmi_dev\001Demo\demoApp\XCamView\ThirdParties\lib\zlib\include -DZLIB_LIBRARY=E:\work\01Camera\01ToupHdmi_dev\001Demo\demoApp\XCamView\ThirdParties\lib\zlib\x86\zlib.lib -DEXPAT_INCLUDE_DIR=E:\work\01Camera\01ToupHdmi_dev\001Demo\demoApp\XCamView\ThirdParties\lib\expat\include -DEXPAT_LIBRARY=E:\work\01Camera\01ToupHdmi_dev\001Demo\demoApp\XCamView\ThirdParties\lib\expat\x86\libexpat.lib -DEXV_UNICODE_PATH=ON ..

cmake -G "Visual Studio 17 2022" -A x64 -DCMAKE_INSTALL_PREFIX=E:\work\01Camera\01ToupHdmi_dev\001Demo\demoApp\XCamView\ThirdParties\exiv2-main\bin\x64 -DZLIB_INCLUDE_DIR=E:\work\01Camera\01ToupHdmi_dev\001Demo\demoApp\XCamView\ThirdParties\lib\zlib\include -DZLIB_LIBRARY=E:\work\01Camera\01ToupHdmi_dev\001Demo\demoApp\XCamView\ThirdParties\lib\zlib\x64\zlib.lib -DEXPAT_INCLUDE_DIR=E:\work\01Camera\01ToupHdmi_dev\001Demo\demoApp\XCamView\ThirdParties\lib\expat\include -DEXPAT_LIBRARY=E:\work\01Camera\01ToupHdmi_dev\001Demo\demoApp\XCamView\ThirdParties\lib\expat\x64\libexpat.lib -DEXV_UNICODE_PATH=ON ..

cmake --build . --config Release --target install