/**
 * @file example_usage.cpp
 * @brief TpDatabase使用示例
 * 
 * 这个文件展示了如何在XCamView项目中使用TpDatabase模块，
 * 包括基本的数据库连接、用户认证等功能。
 */

#include "TpDatabase.h"
#include <QApplication>
#include <QDebug>
#include <QMessageBox>
#include <QDialog>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QLabel>
#include <QLineEdit>
#include <QPushButton>

/**
 * @brief 使用TpDatabase的登录对话框示例
 */
class TpDatabaseLoginDialog : public QDialog
{
    Q_OBJECT

public:
    explicit TpDatabaseLoginDialog(QWidget *parent = nullptr)
        : QDialog(parent)
        , m_connection(nullptr)
    {
        setWindowTitle("TpDatabase 登录示例");
        setFixedSize(350, 200);
        
        setupUI();
        setupDatabase();
    }
    
    ~TpDatabaseLoginDialog()
    {
        if (m_connection) {
            delete m_connection;
        }
    }

private slots:
    void onLoginClicked()
    {
        QString username = m_usernameEdit->text().trimmed();
        QString password = m_passwordEdit->text();
        
        if (username.isEmpty() || password.isEmpty()) {
            QMessageBox::warning(this, "输入错误", "用户名和密码不能为空");
            return;
        }
        
        // 使用TpDatabase进行用户验证
        if (!m_connection || !m_connection->isConnected()) {
            QMessageBox::critical(this, "数据库错误", "数据库未连接");
            return;
        }
        
        TpDatabaseResult result = m_connection->validateUser(username, password);
        if (result.isSuccess()) {
            int userId = result.data().toInt();
            QMessageBox::information(this, "登录成功", 
                QString("欢迎用户 %1 (ID: %2)").arg(username).arg(userId));
            accept();
        } else {
            QMessageBox::critical(this, "登录失败", 
                QString("用户验证失败: %1").arg(result.errorMessage()));
            m_passwordEdit->clear();
            m_passwordEdit->setFocus();
        }
    }
    
    void onTestConnectionClicked()
    {
        if (!m_connection) {
            QMessageBox::warning(this, "错误", "数据库连接对象未创建");
            return;
        }
        
        TpDatabaseResult result = m_connection->testConnection();
        if (result.isSuccess()) {
            QMessageBox::information(this, "连接测试", "数据库连接正常");
        } else {
            QMessageBox::critical(this, "连接测试", 
                QString("数据库连接失败: %1").arg(result.errorMessage()));
        }
    }

private:
    void setupUI()
    {
        QVBoxLayout* mainLayout = new QVBoxLayout(this);
        
        // 用户名输入
        QHBoxLayout* usernameLayout = new QHBoxLayout();
        usernameLayout->addWidget(new QLabel("用户名:"));
        m_usernameEdit = new QLineEdit();
        m_usernameEdit->setPlaceholderText("请输入用户名");
        usernameLayout->addWidget(m_usernameEdit);
        mainLayout->addLayout(usernameLayout);
        
        // 密码输入
        QHBoxLayout* passwordLayout = new QHBoxLayout();
        passwordLayout->addWidget(new QLabel("密码:"));
        m_passwordEdit = new QLineEdit();
        m_passwordEdit->setEchoMode(QLineEdit::Password);
        m_passwordEdit->setPlaceholderText("请输入密码");
        passwordLayout->addWidget(m_passwordEdit);
        mainLayout->addLayout(passwordLayout);
        
        // 按钮
        QHBoxLayout* buttonLayout = new QHBoxLayout();
        m_loginButton = new QPushButton("登录");
        m_testButton = new QPushButton("测试连接");
        QPushButton* cancelButton = new QPushButton("取消");
        
        buttonLayout->addWidget(m_testButton);
        buttonLayout->addStretch();
        buttonLayout->addWidget(m_loginButton);
        buttonLayout->addWidget(cancelButton);
        mainLayout->addLayout(buttonLayout);
        
        // 连接信号槽
        connect(m_loginButton, &QPushButton::clicked, this, &TpDatabaseLoginDialog::onLoginClicked);
        connect(m_testButton, &QPushButton::clicked, this, &TpDatabaseLoginDialog::onTestConnectionClicked);
        connect(cancelButton, &QPushButton::clicked, this, &QDialog::reject);
        connect(m_usernameEdit, &QLineEdit::returnPressed, this, &TpDatabaseLoginDialog::onLoginClicked);
        connect(m_passwordEdit, &QLineEdit::returnPressed, this, &TpDatabaseLoginDialog::onLoginClicked);
    }
    
    void setupDatabase()
    {
        // 创建数据库配置
        TpDatabaseConfig config;
        
        // 尝试从配置文件加载
        if (!config.loadFromFile()) {
            qDebug() << "未找到配置文件，使用默认配置";
            // 使用与Qt5Demo兼容的默认配置
            config.setHostName("*************");
            config.setPort(3306);
            config.setDatabaseName("student");
            config.setUserName("liu");
            config.setPassword("SecurePass123!");
        }
        
        // 从环境变量加载配置（优先级更高）
        config.loadFromEnvironment();
        
        qDebug() << "数据库配置:" << config.formatConnectionString();
        
        // 创建数据库连接
        m_connection = new TpDatabaseConnection(config, this);
        
        // 连接信号槽
        connect(m_connection, &TpDatabaseConnection::connectionStateChanged,
                this, [this](TpDatabaseConnectionState state) {
            qDebug() << "数据库连接状态变化:" << static_cast<int>(state);
        });
        
        connect(m_connection, &TpDatabaseConnection::errorOccurred,
                this, [this](const QString& error) {
            qDebug() << "数据库错误:" << error;
        });
        
        // 尝试连接数据库
        TpDatabaseResult result = m_connection->connectToDatabase();
        if (result.isSuccess()) {
            qDebug() << "数据库连接成功";
            m_loginButton->setEnabled(true);
        } else {
            qDebug() << "数据库连接失败:" << result.errorMessage();
            m_loginButton->setEnabled(false);
            
            // 显示连接失败提示
            QMessageBox::warning(this, "数据库连接失败", 
                QString("无法连接到数据库:\n%1\n\n请检查配置文件或网络连接。")
                .arg(result.errorMessage()));
        }
    }

private:
    QLineEdit* m_usernameEdit;
    QLineEdit* m_passwordEdit;
    QPushButton* m_loginButton;
    QPushButton* m_testButton;
    TpDatabaseConnection* m_connection;
};

/**
 * @brief 基本使用示例函数
 */
void basicUsageExample()
{
    qDebug() << "=== TpDatabase 基本使用示例 ===";
    
    // 1. 初始化模块
    TpDatabase::initialize();
    qDebug() << "TpDatabase版本:" << TpDatabase::version();
    
    // 2. 创建配置
    TpDatabaseConfig config;
    config.setHostName("localhost");
    config.setPort(3306);
    config.setDatabaseName("test");
    config.setUserName("root");
    config.setPassword("");
    
    // 3. 创建连接
    TpDatabaseConnection connection(config);
    
    // 4. 连接数据库
    TpDatabaseResult result = connection.connectToDatabase();
    if (result.isSuccess()) {
        qDebug() << "数据库连接成功";
        
        // 5. 测试连接
        TpDatabaseResult testResult = connection.testConnection();
        if (testResult.isSuccess()) {
            qDebug() << "连接测试通过";
        }
        
        // 6. 用户验证示例
        TpDatabaseResult authResult = connection.validateUser("admin", "password");
        if (authResult.isSuccess()) {
            qDebug() << "用户验证成功，用户ID:" << authResult.data().toInt();
        } else {
            qDebug() << "用户验证失败:" << authResult.errorMessage();
        }
        
    } else {
        qDebug() << "数据库连接失败:" << result.errorMessage();
    }
    
    // 7. 清理
    TpDatabase::cleanup();
}

/**
 * @brief 配置管理示例函数
 */
void configManagementExample()
{
    qDebug() << "=== TpDatabase 配置管理示例 ===";
    
    // 1. 从文件加载配置
    TpDatabaseConfig config;
    if (config.loadFromFile("config/database.ini")) {
        qDebug() << "配置文件加载成功:" << config.formatConnectionString();
    } else {
        qDebug() << "配置文件加载失败，使用默认配置";
        config = TpDatabaseConfig::createDefault();
    }
    
    // 2. 从环境变量加载配置
    config.loadFromEnvironment();
    
    // 3. 修改配置
    TpDatabasePoolConfig poolConfig = config.poolConfig();
    poolConfig.minConnections = 1;
    poolConfig.maxConnections = 3;
    config.setPoolConfig(poolConfig);
    
    // 4. 保存配置
    if (config.saveToFile("config/database_backup.ini")) {
        qDebug() << "配置已保存到备份文件";
    }
    
    qDebug() << "最终配置:" << config.formatConnectionString();
}

// 如果作为独立程序运行
#ifndef TPDATABASE_EXAMPLE_AS_LIBRARY
int main(int argc, char *argv[])
{
    QApplication app(argc, argv);
    
    // 运行基本示例
    basicUsageExample();
    configManagementExample();
    
    // 显示登录对话框示例
    TpDatabaseLoginDialog dialog;
    return dialog.exec();
}
#endif

#include "example_usage.moc"
