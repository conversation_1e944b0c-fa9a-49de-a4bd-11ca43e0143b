#include "mainwindow.h"
#include "panel/login/logindialog.h"
#include <QApplication>

int main(int argc, char *argv[])
{
    QApplication a(argc, argv);
    LogInit();

    // 初始化TpDatabase模块
    TpDatabase::initialize();
    TpDatabase::setLogLevel(3); // 设置信息级别日志

    TpSignalConnect signalConnect; Q_UNUSED(signalConnect);
    I18n i18n;
    i18n.init();
    LoginDialog loginDlg;
    if(loginDlg.exec() != QDialog::Accepted)
    {
        return 0;
    }
    CameraManager camera;
    if(!camera.InitLoader())
        return -1;
    g_pFontMetrics = new QFontMetrics(QApplication::font());
    g_pMainWindow = new MainWindow();
    g_pMainWindow->setWindowState(Qt::WindowMaximized);
    g_pMainWindow->show();

    int result = a.exec();

    // 清理TpDatabase模块
    TpDatabase::cleanup();

    return result;
}


