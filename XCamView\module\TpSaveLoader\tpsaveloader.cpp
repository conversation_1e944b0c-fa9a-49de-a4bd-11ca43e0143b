#include "tpsaveloader.h"

TpSaveLoader g_saveLoader;

static const QString CaliCountKey = "CaliCount";
static const QString CaliMagKey = "CaliMag";
static const QString CaliPpmKey = "CaliPpm";
static const QString CaliUnitKey = "CaliUnit";
static const QString CaliCurIndexKey = "CaliCurIndex";

TpSaveLoader::TpSaveLoader(QObject *parent)
    : QObject(parent)
    , m_settings("Touptek", "XCamView")
{

}

void TpSaveLoader::SaveCalibration(const QList<Calibration> &caliList)
{
    if(caliList.empty())
        return;

    m_settings.setValue(CaliCountKey, caliList.count());
    for(int i = 0; i < caliList.count(); ++i)
    {
        m_settings.setValue(CaliMagKey + QString::number(i), caliList[i].magnification);
        m_settings.setValue(CaliPpmKey + QString::number(i), caliList[i].resolution);
        m_settings.setValue(CaliUnitKey + QString::number(i), caliList[i].unit);
    }
}

void TpSaveLoader::LoadCalibration(QList<Calibration> *pCaliList)
{
    if(nullptr == pCaliList)
        return;

    pCaliList->clear();

    int count = m_settings.value(CaliCountKey, 0).toInt();
    if(count <= 0)
    {
        pCaliList->append({ "NA", 1.0, 0 });
        return;
    }

    for(int i = 0; i < count; ++i)
    {
        Calibration cali;
        cali.magnification = m_settings.value(CaliMagKey + QString::number(i)).toString();
        cali.resolution = m_settings.value(CaliPpmKey + QString::number(i), 1.0).toDouble();
        cali.unit = m_settings.value(CaliUnitKey + QString::number(i), 0).toInt();
        pCaliList->append(cali);
    }
}

void TpSaveLoader::SaveCurCaliIndex(int index)
{
    m_settings.setValue(CaliCurIndexKey, index);
}

int TpSaveLoader::LoadCurCaliIndex()
{
    return m_settings.value(CaliCurIndexKey, 0).toInt();
}
