-- 准备MySQL以支持远程连接（在*************上执行）

-- 1. 确保liu用户可以从任何IP连接
CREATE USER IF NOT EXISTS 'liu'@'%' IDENTIFIED BY 'SecurePass123!';
GRANT ALL PRIVILEGES ON student.* TO 'liu'@'%';

-- 2. 特别为*************创建用户（如果需要）
CREATE USER IF NOT EXISTS 'liu'@'*************' IDENTIFIED BY 'SecurePass123!';
GRANT ALL PRIVILEGES ON student.* TO 'liu'@'*************';

-- 3. 为整个网段创建用户
CREATE USER IF NOT EXISTS 'liu'@'192.168.6.%' IDENTIFIED BY 'SecurePass123!';
GRANT ALL PRIVILEGES ON student.* TO 'liu'@'192.168.6.%';

-- 4. 刷新权限
FLUSH PRIVILEGES;

-- 5. 验证用户创建
SELECT user, host FROM mysql.user WHERE user = 'liu' ORDER BY host;

-- 6. 显示权限
SHOW GRANTS FOR 'liu'@'%';

-- 完成！现在*************应该可以连接了
