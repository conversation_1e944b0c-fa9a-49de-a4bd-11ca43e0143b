# 监控用户同步测试指南

## 🧪 **测试场景**

### **测试1: 用户加入同步**
1. **A主机先启动**
   - 预期：A主机显示1个用户（自己）
   - 检查：在线用户数 = 1

2. **B主机后启动**
   - 预期：B主机显示2个用户（自己+A主机）
   - 预期：A主机显示2个用户（自己+B主机）
   - 检查：两边在线用户数都 = 2

### **测试2: 心跳检测**
1. **正常心跳**
   - 预期：每1.5秒发送心跳包
   - 预期：每1.5秒检查心跳超时
   - 检查：日志中有心跳发送记录

2. **B主机正常退出**
   - 操作：B主机正常关闭程序
   - 预期：A主机在3秒内检测到B主机离线
   - 预期：A主机用户列表变为1个用户
   - 检查：在线用户数 = 1

3. **B主机异常断线**
   - 操作：B主机强制关闭（任务管理器结束进程）
   - 预期：A主机在3秒内检测到B主机超时
   - 预期：A主机自动移除B主机
   - 检查：在线用户数 = 1

## 📋 **关键日志检查**

### **用户加入日志**
```
MonitorWidget: 处理新用户加入: UserB (192.168.6.140)
MonitorWidget: 收到来自 UserB (192.168.6.140) 的用户列表请求
MonitorWidget: 当前在线用户数: 2 表格行数: 2
MonitorWidget: 准备发送用户列表响应，包含 2 个用户:
  - UserA (192.168.6.189)
  - UserB (192.168.6.140)
MonitorWidget: 用户列表响应已发送
```

### **心跳检测日志**
```
MonitorWidget: 发送心跳包: UserA
MonitorWidget: 收到心跳: UserB (192.168.6.140)
```

### **超时检测日志**
```
MonitorWidget: 检测到用户超时: UserB 超时时间: 3500ms
MonitorWidget: 移除超时用户: UserB (192.168.6.140)
MonitorWidget: 从表格移除用户，行号: 1
MonitorWidget: 心跳检测完成，移除1个超时用户，当前在线: 1
```

## 🔧 **修复内容总结**

### **1. 心跳检测定时器**
- ✅ 添加了缺失的心跳检测定时器
- ✅ 每1.5秒检查一次用户超时

### **2. 超时检测逻辑**
- ✅ 改进了超时用户移除逻辑
- ✅ 同步更新表格、在线列表、映射表

### **3. 用户同步机制**
- ✅ 改进了用户列表响应发送
- ✅ 添加了详细的调试日志

### **4. 心跳处理**
- ✅ 只处理其他用户的心跳
- ✅ 防止自己被超时移除

## ⚠️ **常见问题排查**

### **问题1: B主机看不到A主机**
- 检查：B主机是否发送了UserListRequest
- 检查：A主机是否响应了UserListResponse
- 检查：网络是否正常（防火墙、端口12312）

### **问题2: 心跳检测不工作**
- 检查：是否有"发送心跳包"日志
- 检查：是否有"收到心跳"日志
- 检查：超时阈值是否合理（当前3秒）

### **问题3: 用户不会自动移除**
- 检查：是否有"检测到用户超时"日志
- 检查：是否有"移除超时用户"日志
- 检查：表格行数是否正确更新

## 🎯 **预期修复效果**

1. **B主机启动后能看到A主机** ✅
2. **A主机能看到B主机加入** ✅
3. **B主机下线后，A主机能检测到并移除** ✅
4. **在线用户数实时准确** ✅
5. **心跳机制正常工作** ✅

## 🚀 **测试步骤**

1. **编译新版本**
2. **A主机启动** → 检查显示1个用户
3. **B主机启动** → 检查两边都显示2个用户
4. **B主机退出** → 检查A主机3秒内变为1个用户
5. **查看日志** → 确认心跳和超时检测正常

如果测试通过，说明用户同步和心跳检测问题已完全解决！
