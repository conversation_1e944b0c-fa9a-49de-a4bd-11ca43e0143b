#ifndef TPMEASUREMANAGAER_H
#define TPMEASUREMANAGAER_H

#include <QUndoStack>
#include <QMap>
#include "tpshape.h"
#include "tpmeasuredef.h"

QT_FORWARD_DECLARE_CLASS(QUndoStack)

class TPMEASUREMANAGER_API TpMeasureManager : public QObject
{
    Q_OBJECT
public:
    friend class AddShapeCommand;
    friend class RemoveShapeCommand;
    friend class AddAllShapesCommand;
    friend class RemoveAllShapesCommand;
    friend class SetVisibleCommand;
    friend class SetLockCommand;
    friend class SetNameCommand;
    friend class SetColorCommand;
    friend class SetWidthCommand;
    friend class SetStyleCommand;
    friend class SetTextCommand;
    friend class SetPtsCommand;
    friend class SetLabelDiffCommand;

    TpMeasureManager();
    ~TpMeasureManager();
    void setMSize(int w, int h){ TpShape::initSize(w * 1.0, h * 1.0); }
    QSize mSize() const { return QSizeF(TpShape::maxW(), TpShape::maxH()).toSize(); }
    void setMeasureMode(eMMode mode);
    void rmShape();
    void rmAllObjects();
    void rmAllShapes();
    void reSetMMager();
    bool isEmpty() { return m_shapeList.isEmpty(); }
    void hFlip();
    void vFlip();
    void setScaleBar(float length, eUnit unit);
    void enableCalibration(bool bCalibration = true);
    void setCaliWidth(const int& width) { m_caliWidth = width; }
    void setCaliCenter(const QPointF& center) { m_caliCenter = center; }
    bool hasCrossLine() const;
    bool hasShape() const;
    void setEndPointType(eEndPtType type);
    void paint(QPainter& painter, bool bToImage = false);
    bool mousePress(QMouseEvent* event, const QPointF& pos);
    void mouseRelease(QMouseEvent* event, const QPointF& pos);
    void mouseMove(const QPointF& pos);
    eMMProcedure getCurMMProc() const { return m_drawProcess; }
    QUndoStack* getUndoStack() const { return m_undoStack; }
    void rmAllCrossHLines();
    void rmAllCrossVLines();
    void rmAllCrossLines();
    void updateProfile();
    eMMCursor getCursorShape(const QPointF& pos, Qt::CursorShape& cursorShape);
    void setScale(double scale);
    void setLangMap(const QMap<QString, QString> &langMap);

    void setResolution(double resolution);
    double getResolution() const;
    void setUnit(eUnit unit);
    eUnit getUnit() const;

    void SetTextDlgResult(const QString& text, bool bOK);

    void GetCurShapeItem(ShapeItem *pItem);
    void SetCurShapeItem(const ShapeItem& item);

    QList<TpShape*> getShapeListRef() const;
    int getCurShapeIndex() { return indexOf(m_curShape); }
    TpShape* getCurShape(int index = -1) const { return (index < 0 || index >= m_shapeList.count() ? m_curShape : m_shapeList.at(index)); }
    int getShapeIndex(TpShape* shape) {return indexOf(shape);}
    void getShapeList(QList<TpShape*>& list);
    void getShapeItemList(QList<ShapeItem>& list);
    int getShapeCount() { return m_shapeList.length(); }
    eShapeType getCurShapeType() const;
    eMMode getMeasureMode() const { return m_mMode; }

    void setVisible(TpShape* shape, const bool& visible);
    void setLock(TpShape* shape, const bool& lock);
    void setName(TpShape* shape, const QString& name);
    void setColor(TpShape* shape, const QColor& color);
    void setWidth(TpShape* shape, const int& width);
    void setStyle(TpShape* shape, const ePenStyle& style);
    void setText(TpShape* shape, const QString& text);
    void setPts(TpShape* shape, const QList<QPointF>& pts);
    void setLabelDiff(TpShape* shape, const QPointF& diffPt);
    void updateShape(TpShape* shape, bool bOnlyLabel = false);
    void updateAll();

signals:
    void currentShapeChanged(const QString& shapeName);
    void calibrationLengthChanged(double length);
    void singleDeleteClicked();
    void colorClicked();
    void rowColOfGridsChanged(int row, int col);
    void showErrorDlg(QString info);
    void parametersOfGridsChanged();
    void postMeasureMsg(eMMsg msg, int value1 = -1, int value2 = -1);
    void postProfileMsg(QPointF startPt = QPointF(), QPointF endPt = QPointF());
    void textDlg();

private:
    void setScaleAndOffset(double scale, QPointF offset);
    void move(const eDirection direct, const int step = 1);
    void addExifShapes(const QList<TpShape*>& shapes);
    void addShape(TpShape* shape);
    double getReadPpm() const { return m_readPpm; }
    void setReadPpm(const double& ppm) { m_readPpm = ppm; updateAll(); }
    void setCurShape(TpShape* shape);
    bool appendShape(TpShape* shape);
    void choseShape(TpShape* shape, const bool& bMulti = false);
    void choseShape(const int& row, const bool& bMulti = false);
    void choseShape(const int& startRow, const int& stopRow);
    void updateShapeWithInfo(const ShapeItem& item);

private:
    void setMeasureCursor(const QPointF& pos);
    void _addShape(TpShape* shape, int index = -1);
    void _addAllShapes(const QList<TpShape*>& shapes);
    void _rmShape(TpShape* shape);
    void _rmShape(QList<int> indexes);
    void _rmAllShapes();
    void _setVisible(TpShape* shape, const bool& visible);
    void _setLock(TpShape* shape, const bool& lock);
    void _setName(TpShape* shape, const QString& name);
    void _setColor(TpShape* shape, const QColor& color);
    void _setWidth(TpShape* shape, const int& width);
    void _setStyle(TpShape* shape, const ePenStyle& style);
    void _setText(TpShape* shape, const QString& text);
    void _setPt(TpShape* shape, int index, const QPointF& pt);
    void _setPts(TpShape* shape, const QList<QPointF>& pts);
    void _setLabelDiff(TpShape* shape, const QPointF& diffPt);
    void _movePts(TpShape* shape, const QPointF& diff);
    void _moveLabelPts(TpShape* shape, const QPointF& diff);
    void _resize(TpShape* shape, int index, const QPointF& pos);
    TpShape* shapeAt(const QPointF& pos) const;
    int indexOf(TpShape* shape) const;
    bool isMoved(const QPointF& pos) const;
    bool isLabelMoved(const QPointF& pos) const;
    bool isResized(const QPointF& pos) const;
    Qt::CursorShape getCursor(TpShape* shape, const QPointF& pos);
    void clearFocus();
    QRegion shapeRegion(TpShape* shape, bool bOnlyLabel = false);
    QString assignDftName(eShapeType type);
    QUndoStack*            m_undoStack;
    QList<TpShape*>        m_shapeList;
    TpShape*               m_curShape;
    TpShape*               m_caliShape;
    int                    m_caliWidth;
    QPointF                m_caliCenter;
    eMMode                 m_mMode;
    eMMProcedure           m_drawProcess;
    int                    m_drawPtCount;
    QPointF                m_oldPt;
    QPointF                m_oldPt_lbl;
    QPointF                m_orgLblDiff;
    bool                   m_bDrag;
    bool                   m_bResized;
    bool                   m_bDrag_lbl;
    bool                   m_bToMove;
    bool                   m_bToMoveLabel;
    int                    m_resizeHandleIndex;
    double                 m_readPpm;
    double                 m_scale;
    double                 m_resolution;
    eUnit                  m_unit;
    QMap<QString, QString> m_langMap;
    eEndPtType             m_EndPointType;
    QString                m_text;
    bool                   m_bTextDlgOK;
};

class TPMEASUREMANAGER_API TpShapeFactory
{
public:
    static TpShape* createShape(eShapeType type);
};

#endif
