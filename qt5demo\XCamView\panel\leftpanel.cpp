#include "leftpanel.h"
#include "camerapanel/camerapanel.h"
#include "monitorpanel/monitorwidget.h"
#include "chatpanel/chatwidget.h"
#include "login/tpdatabasehandler.h"
#include <QDebug>

LeftPanel::LeftPanel(QWidget *parent)
    : QDockWidget(parent)
{
    qDebug() << "LeftPanel: 开始初始化...";
    
    setMinimumWidth(g_pFontMetrics->averageCharWidth() * 50);
    setAllowedAreas(Qt::LeftDockWidgetArea);
    setFeatures(QDockWidget::DockWidgetClosable);

    qDebug() << "LeftPanel: 创建数据库处理器...";
    TpdatabaseHandler* dbHandler = new TpdatabaseHandler(this);
    dbHandler->connectToDatabase();

    qDebug() << "LeftPanel: 创建 CameraPanel...";
    m_pCameraPanel = new CameraPanel();
    
    qDebug() << "LeftPanel: 创建 MonitorWidget...";
    m_pMonitorWidget = new MonitorWidget();
    
    qDebug() << "LeftPanel: 创建 ChatWidget...";
    m_pChatWidget = new ChatWidget(dbHandler, this);

    qDebug() << "LeftPanel: 创建 TabWidget...";
    m_pTabWidget = new QTabWidget(this);
    m_pTabWidget->setTabPosition(QTabWidget::South);
    m_pTabWidget->addTab(m_pCameraPanel, g_i18n->value("ids_camera"));
    m_pTabWidget->addTab(m_pMonitorWidget, g_i18n->value("ids_monitor"));
    m_pTabWidget->addTab(m_pChatWidget, g_i18n->value("ids_chat_room"));
    setWidget(m_pTabWidget);
    
    qDebug() << "LeftPanel: 初始化完成";
}

void LeftPanel::Notify(int notify)
{
    m_pCameraPanel->Notify(notify);
    m_pMonitorWidget->Notify(notify);
}
