#include "tptabwidget.h"
#include "displayview/imageview.h"
#include "displayview/previewview.h"
#include "monitorview/monitorview.h"

TpTabWidget::TpTabWidget(QWidget* parent)
    : QTabWidget(parent)
{
    setTabsClosable(true);
    QString stylesheet = "QStackedWidget{background-color:rgb(171,171,171);border:none;}";
    setStyleSheet(stylesheet);
    setUsesScrollButtons(true);
}

void TpTabWidget::Notify(int notify)
{
    for(int i = 0; i < count(); ++i)
    {
        QWidget* pWidget = widget(i);
        if(0 == strcmp(pWidget->metaObject()->className(), "ImageView"))
            reinterpret_cast<ImageView*>(pWidget)->Notify(notify);
        else if(0 == strcmp(pWidget->metaObject()->className(), "PreviewView"))
            reinterpret_cast<PreviewView*>(pWidget)->Notify(notify);
        else if(0 == strcmp(pWidget->metaObject()->className(), "MonitorView"))
            reinterpret_cast<MonitorView*>(pWidget)->Notify(notify);
    }
}
