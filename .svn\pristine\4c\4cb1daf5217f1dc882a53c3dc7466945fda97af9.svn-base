#ifndef GLOBAL_H
#define GLOBAL_H

#include <QFontMetrics>
#include <QString>
#include <QList>

typedef struct
{
    QString magnification;
    double resolution;
    int unit;
}Calibration;

extern QFontMetrics* g_pFontMetrics;
extern bool g_bWBDlgHidden;
extern int g_WBMode;
extern QList<Calibration> g_caliList;
extern int g_caliIndex;

extern int g_zoomcnt;
extern int g_zoom[];
extern int g_zoomIndex;
extern double g_zoomScale;

extern double g_zoomWheel[];
extern int g_zoomWheelCnt;
int GetZoomWheelIndex(double scale, bool bUp);

extern int g_measureActType;

extern int g_minResWidth;
extern int g_minResHeight;
extern int g_maxResWidth;
extern int g_maxResHeight;

enum LogLevel {
    Log_Debug,
    Log_Info,
    Log_Warning,
    Log_Error,
    Log_Critical
};

void LogInit();
void Log(LogLevel level, const char* format, ...);
void Log(LogLevel level, const QString& str);

#endif // GLOBAL_H
